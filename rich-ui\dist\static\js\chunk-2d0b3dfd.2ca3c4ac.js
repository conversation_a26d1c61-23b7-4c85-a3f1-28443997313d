(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b3dfd"],{"29dd":function(e,r,s){"use strict";s.r(r);var t=s("5530");s("14d9"),s("2ef0");r["default"]={data:function(){return{serviceTypeMap:{1:"整柜海运",2:"拼柜海运",10:"空运",20:"整柜铁路",21:"拼柜铁路",40:"快递",50:"整柜拖车",51:"散货拖车",60:"单证报关",61:"全包报关",70:"代理放单",71:"清关代理",80:"仓储服务"}}},methods:{createServiceInstance:function(){return{accountConfirmTime:null,agreementNo:"",agreementTypeCode:null,clientConfirmedTime:null,confirmAccountId:null,createBy:null,createByName:null,createTime:null,deleteBy:null,deleteByName:null,deleteStatus:null,deleteTime:null,inquiryInnerRemark:"",inquiryLeatestUpdatedTime:null,inquiryNo:null,inquiryNotice:null,inquiryPsaId:null,isAccountConfirmed:null,isDnClientConfirmed:null,isDnOpConfirmed:null,isDnPsaConfirmed:null,isDnSalesConfirmed:null,isDnSupplierConfirmed:null,logisticsPaymentTermsCode:null,maxWeight:null,opConfirmedTime:null,paymentTitleCode:null,permissionLevel:null,psaConfirmedTime:null,rctId:null,rctNo:null,remark:null,salesConfirmedTime:null,serviceBelongTo:null,serviceId:null,serviceTypeId:null,supplierConfirmedTime:null,supplierContact:null,supplierId:null,supplierName:null,supplierSummary:null,supplierTel:null,updateBy:null,updateByName:null,updateTime:null}},createServiceObject:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object(t["a"])(Object(t["a"])({},e&&{rsServiceInstances:this.createServiceInstance()}),{},{rsChargeList:[],rsOpLogList:[],rsDocList:[]},r)},getServiceObject:function(e){if(!e)return null;switch(parseInt(e)){case 1:return this.form.rsOpSeaFclList&&this.form.rsOpSeaFclList.length>0?this.form.rsOpSeaFclList[0]:null;case 2:return this.form.rsOpSeaLclList&&this.form.rsOpSeaLclList.length>0?this.form.rsOpSeaLclList[0]:null;case 10:return this.form.rsOpAirList&&this.form.rsOpAirList.length>0?this.form.rsOpAirList[0]:null;case 50:return this.form.rsOpCtnrTruckList&&this.form.rsOpCtnrTruckList.length>0?this.form.rsOpCtnrTruckList[0]:null;case 51:return this.form.rsOpBulkTruckList&&this.form.rsOpBulkTruckList.length>0?this.form.rsOpBulkTruckList[0]:null;case 60:return this.form.rsOpDocDeclareList&&this.form.rsOpDocDeclareList.length>0?this.form.rsOpDocDeclareList[0]:null;case 61:return this.form.rsOpFreeDeclareList&&this.form.rsOpFreeDeclareList.length>0?this.form.rsOpFreeDeclareList[0]:null;case 20:return this.rsOpRailFCL;case 21:return this.rsOpRailLCL;case 40:return this.rsOpExpress;case 70:return this.rsOpDOAgent;case 71:return this.rsOpClearAgent;case 80:return this.rsOpWHS;default:return null}},getServiceInstance:function(e){var r=this.getServiceObject(e);if(r&&r.rsServiceInstances)return r.rsServiceInstances;switch(parseInt(e)){case 1:return this.rsOpSeaFclServiceInstance;case 2:return this.rsOpSeaLclServiceInstance;case 10:return this.rsOpAirServiceInstance;case 20:return this.rsOpRailFclServiceInstance;case 21:return this.rsOpRailLclServiceInstance;case 40:return this.rsOpExpressServiceInstance;case 50:return this.rsOpCtnrTruckServiceInstance;case 51:return this.rsOpBulkTruckServiceInstance;case 60:return this.rsOpDocDeclareServiceInstance;case 61:return this.rsOpFreeDeclareServiceInstance;case 70:return this.rsOpDOAgentServiceInstance;case 71:return this.rsOpClearAgentServiceInstance;case 80:return this.rsOpWHSServiceInstance;default:return null}},addEmptyService:function(e){if(e)switch(parseInt(e)){case 1:if(!this.form.rsOpSeaFclList||0===this.form.rsOpSeaFclList.length){this.form.rsOpSeaFclList||(this.form.rsOpSeaFclList=[]);var r=this.createServiceObject();r.rsServiceInstances=this.createServiceInstance(),this.form.rsOpSeaFclList.push(r)}break;case 2:if(!this.form.rsOpSeaLclList||0===this.form.rsOpSeaLclList.length){this.form.rsOpSeaLclList||(this.form.rsOpSeaLclList=[]);var s=this.createServiceObject();s.rsServiceInstances=this.createServiceInstance(),this.form.rsOpSeaLclList.push(s)}break;case 10:if(!this.form.rsOpAirList||0===this.form.rsOpAirList.length){this.form.rsOpAirList||(this.form.rsOpAirList=[]);var t=this.createServiceObject();t.rsServiceInstances=this.createServiceInstance(),this.form.rsOpAirList.push(t)}break;case 50:if(!this.form.rsOpCtnrTruckList||0===this.form.rsOpCtnrTruckList.length){this.form.rsOpCtnrTruckList||(this.form.rsOpCtnrTruckList=[]);var i=this.createServiceObject(!0,{rsOpTruckList:[]});i.rsServiceInstances=this.createServiceInstance(),this.form.rsOpCtnrTruckList.push(i)}break;case 51:if(!this.form.rsOpBulkTruckList||0===this.form.rsOpBulkTruckList.length){this.form.rsOpBulkTruckList||(this.form.rsOpBulkTruckList=[]);var c=this.createServiceObject(!0,{rsOpTruckList:[]});c.rsServiceInstances=this.createServiceInstance(),this.form.rsOpBulkTruckList.push(c)}break;case 60:if(!this.form.rsOpDocDeclareList||0===this.form.rsOpDocDeclareList.length){this.form.rsOpDocDeclareList||(this.form.rsOpDocDeclareList=[]);var n=this.createServiceObject();n.rsServiceInstances=this.createServiceInstance(),this.form.rsOpDocDeclareList.push(n)}break;case 61:if(!this.form.rsOpFreeDeclareList||0===this.form.rsOpFreeDeclareList.length){this.form.rsOpFreeDeclareList||(this.form.rsOpFreeDeclareList=[]);var l=this.createServiceObject();l.rsServiceInstances=this.createServiceInstance(),this.form.rsOpFreeDeclareList.push(l)}break}},getServiceTypeName:function(e){return this.serviceTypeMap[e]||"未知服务类型"},toggleServiceFold:function(e){if(e)switch(parseInt(e)){case 1:this.rsOpSealFclFold=!this.rsOpSealFclFold;break;case 2:this.rsOpSealLclFold=!this.rsOpSealLclFold;break;case 10:this.rsOpAirFold=!this.rsOpAirFold;break;case 20:this.rsOpRailFclFold=!this.rsOpRailFclFold;break;case 21:this.rsOpRailLclFold=!this.rsOpRailLclFold;break;case 40:this.rsOpExpressFold=!this.rsOpExpressFold;break;case 50:this.rsOpCtnrTruckFold=!this.rsOpCtnrTruckFold;break;case 51:this.rsOpBulkTruckFold=!this.rsOpBulkTruckFold;break;case 60:this.rsOpDocDeclareFold=!this.rsOpDocDeclareFold;break;case 61:this.rsOpFreeDeclareFold=!this.rsOpFreeDeclareFold;break;case 70:this.rsOpDOAgentFold=!this.rsOpDOAgentFold;break;case 71:this.rsOpClearAgentFold=!this.rsOpClearAgentFold;break;case 80:this.rsOpWHSFold=!this.rsOpWHSFold;break}}}}}}]);