(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-87ba0f1e","chunk-87ba0f1e","chunk-497bf3b4","chunk-497bf3b4","chunk-2d0cc474","chunk-2d20feaa","chunk-2d2268eb","chunk-2d0aa556","chunk-2d0cbcf1"],{1122:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.loading||e.scope.row.loading.length<5,"open-delay":500,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s(e.scope.row.loading)+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.loading)+" ")])])])],1)},i=[],s={name:"loadingLocation",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=s,l=r("2877"),n=Object(l["a"])(o,a,i,!1,null,"52ee6249",null);t["default"]=n.exports},"4af4":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(0!=e.scope.row.grossWeight?"+"+e.scope.row.grossWeight+e.scope.row.cargoUnit:""))])])},i=[],s={name:"weight",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=s,l=r("2877"),n=Object(l["a"])(o,a,i,!1,null,"de015dcc",null);t["default"]=n.exports},"4cf5":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.psaRemark||e.scope.row.psaRemark.length<12,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.psaRemark))])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.psaRemark)+" ")])])])],1)},i=[],s={name:"businessRemark",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=s,l=r("2877"),n=Object(l["a"])(o,a,i,!1,null,"b581cfc8",null);t["default"]=n.exports},aae1:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[e.showSearch?r("el-form",{ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"35px"}},[r("el-form-item",{attrs:{label:"序号",prop:"inquiryNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"询价单号"},model:{value:e.queryParams.inquiryNo,callback:function(t){e.$set(e.queryParams,"inquiryNo",t)},expression:"queryParams.inquiryNo"}})],1),r("el-form-item",{attrs:{label:"服务",prop:"logisticsTypeId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,placeholder:"服务项目",type:"serviceTypeLoad",typeId:e.typeId,"d-load":!0,dbn:!0},on:{returnData:e.queryLogisticsType,return:e.queryLogisticsTypeId}})],1),-1!==[5].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"装运",prop:"departureIds"}},[r("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.queryParams.precarriageRegionId},on:{return:e.queryPrecarriageRegionId}})],1):e._e(),-1!==[1,2,3,4,5,6,7].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"启运",prop:"departureIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.departureIds,"load-options":e.locationOptions},on:{return:e.queryDepartureIds}})],1):e._e(),-1!==[1,2,3,4].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"目的",prop:"destinationIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{"load-options":e.locationOptions,multiple:!0,pass:e.queryParams.destinationIds,en:!0},on:{return:e.queryDestinationIds}})],1):e._e(),-1!==[1].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,"d-load":!0,placeholder:"目的航线",type:"line"},on:{return:e.queryLineDestinationIds}})],1):e._e(),-1!==[1,2,3,4,6,7].indexOf(e.serviceTypeId)?r("el-form-item",{attrs:{label:"柜型",prop:"unitIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.unitIds,type:"unit","d-load":!0,placeholder:"柜型"},on:{return:e.queryUnitIds}})],1):e._e(),r("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!0,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征","d-load":!0,type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),r("el-form-item",{attrs:{label:e.company.substring(0,2),prop:"companyIds"}},[r("tree-select",{ref:"supplier",staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.companyIds,placeholder:e.company,type:"supplier"},on:{return:e.queryCompanyIds},nativeOn:{click:function(t){return e.remoteSupplier(!0)}}})],1),-1!==[1,2,3,4].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,"flatten-search-results":!0,multiple:!1,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{input:e.deselectAllQueryCarrierIds,deselect:e.handleDeselectQueryCarrierIds,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,o=t.labelClassName,l=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1):e._e(),r("el-form-item",{attrs:{label:"合约",prop:"contractTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.contractTypeIds,flat:!1,multiple:!0,dbn:!0,"d-load":!0,placeholder:"合约类别",type:"contractType"},on:{return:e.queryContractTypeIds}})],1),r("el-form-item",{attrs:{label:"有效",prop:"validFrom"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{"end-placeholder":"有效期结束日期","range-separator":"至","start-placeholder":"有效期开始日期","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange"},on:{change:e.handleQuery},model:{value:e.queryParams.dateRange,callback:function(t){e.$set(e.queryParams,"dateRange",t)},expression:"queryParams.dateRange"}})],1),r("el-form-item",{attrs:{label:"搜索",prop:"freightQuery"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"合约号/船期/报价综述/备注"},model:{value:e.queryParams.freightQuery,callback:function(t){e.$set(e.queryParams,"freightQuery",t)},expression:"queryParams.freightQuery"}})],1),r("el-form-item",{attrs:{label:"录入",prop:"updateBy"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanRecordBy,open:e.loadBusinesses,select:e.handleSelectRecordBy},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,o=t.labelClassName,l=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,553526311),model:{value:e.updateBy,callback:function(t){e.updateBy=t},expression:"updateBy"}})],1),r("el-form-item",{attrs:{label:"来源",prop:"isSalesRequired"}},[r("el-select",{attrs:{clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.isSalesRequired,callback:function(t){e.$set(e.queryParams,"isSalesRequired",t)},expression:"queryParams.isSalesRequired"}},[r("el-option",{attrs:{label:"业务询价",value:"1"}}),r("el-option",{attrs:{label:"商务播报",value:"0"}})],1)],1),r("el-form-item",{attrs:{label:"回复",prop:"replyMark"}},[r("el-select",{attrs:{clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.replyMark,callback:function(t){e.$set(e.queryParams,"replyMark",t)},expression:"queryParams.replyMark"}},[r("el-option",{attrs:{label:"未回复",value:"0"}}),r("el-option",{attrs:{label:"已回复",value:"1"}})],1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:add"],expression:"['system:freight:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:remove"],expression:"['system:freight:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:import"],expression:"['system:freight:import']"}],attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"},on:{click:e.handleImport}},[e._v("导入 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:export"],expression:"['system:freight:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{columns:e.columns,types:e.types,showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList,refreshColumns:e.refreshColumns}})],1),r("h4",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" 服务项目："+e._s(e.serviceType)+"-"+e._s(e.charge)+" ")]),e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.freightList,border:"","row-class-name":e.tableRowClassName},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),e._l(e.columns,(function(t){return t.visible?r("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width,"show-overflow-tooltip":t.tooltip},scopedSlots:e._u([{key:"default",fn:function(a){return[r(t.prop,{tag:"component",attrs:{scope:a,typeId:e.typeId}})]}}],null,!0)}):e._e()})),r("el-table-column",{key:"isSalesRequired",attrs:{align:"center",label:"价格来源",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.salesName))]),r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(1==t.row.isSalesRequired?"业务询价":"")+" "+e._s(0==t.row.isSalesRequired?"商务播报":"")+" ")])]}}],null,!1,2589291158)}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:edit"],expression:"['system:freight:edit']"}],staticStyle:{margin:"0 3px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:remove"],expression:"['system:freight:remove']"}],staticStyle:{margin:"0"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}],null,!1,2405305129)})],2):e._e(),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total?e.total:0,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"1000px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"70px"}},[r("el-row",[r("el-col",{staticStyle:{"padding-right":"10px"},attrs:{span:12}},[r("el-form-item",{attrs:{label:"询价单号",prop:"inquiryNo"}},[e._v(" "+e._s(e.form.inquiryNo)+" ")]),r("el-form-item",{attrs:{label:"服务项目",prop:"serviceTypeId"}},[r("el-row",{staticStyle:{padding:"0"}},[r("el-col",{staticStyle:{padding:"0"},attrs:{span:16}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.serviceTypeId,dbn:!0,placeholder:"服务项目",type:"serviceType",typeId:e.typeId},on:{return:e.getServiceTypeId}})],1),r("el-col",{staticStyle:{display:"flex",padding:"0"},attrs:{span:8}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.chargeId,placeholder:"运费",type:"charge",dbn:!0,disabled:9!=e.typeId},on:{return:e.getChargeId}})],1)],1)],1),"1"!=e.typeId&&"2"!=e.typeId&&"3"!=e.typeId&&"4"!=e.typeId?r("el-form-item",{attrs:{label:"所属物流",prop:"logisticsTypeId"}},[r("el-row",{staticStyle:{margin:"0"},attrs:{gutter:5}},[r("el-col",{staticStyle:{padding:"0"},attrs:{span:16,type:"flex"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.logisticsTypeId,dbn:!0,main:!0,placeholder:"所属物流类型",type:"mainServiceType"},on:{return:e.getLogisticsTypeId}})],1),r("el-col",{staticStyle:{display:"flex",padding:"0"},attrs:{span:8}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进出口"},model:{value:e.form.importExport,callback:function(t){e.$set(e.form,"importExport",t)},expression:"form.importExport"}},[r("el-option",{attrs:{value:"1",label:"出口"}},[e._v("出口")]),r("el-option",{attrs:{value:"2",label:"进口"}},[e._v("进口")])],1)],1)],1)],1):e._e(),"4"==e.typeId||"5"==e.typeId||"9"==e.typeId||"8"==e.typeId?r("el-form-item",{attrs:{label:"装运区域",prop:"precarriageRegionId"}},[r("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.form.precarriageRegionId},on:{return:e.getLoadingId}})],1):e._e(),"8"!=e.typeId&&"7"!=e.typeId&&"4"!=e.typeId?r("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[r("location-select",{attrs:{"check-port":e.checkPort,en:!0,"load-options":e.locationOptions,multiple:!1,pass:e.form.polId},on:{return:e.getDepartureId}})],1):e._e(),"8"!=e.typeId&&"4"!=e.typeId&&"5"!=e.typeId&&"6"!=e.typeId?r("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[r("location-select",{attrs:{"check-port":e.checkPort,en:!0,"load-options":e.locationOptions,multiple:!1,pass:e.form.destinationPortId},on:{return:e.getDestinationId}})],1):e._e(),"4"==e.typeId||"9"==e.typeId?r("el-form-item",{attrs:{label:"目的区域",prop:"destinationPortId"}},[r("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.form.destinationPortId,"check-port":"1",en:!0},on:{return:e.getDestinationId}})],1):e._e(),r("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1),"8"!=e.typeId&&"5"!=e.typeId&&"7"!=e.typeId?r("el-form-item",{attrs:{label:"货物限重",prop:"maxWeight"}},[r("el-row",{staticStyle:{margin:"0"},attrs:{gutter:5}},[r("el-col",{staticStyle:{"line-height":"26px",padding:"0"},attrs:{span:18}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"货物限重",precision:2,step:.01},model:{value:e.form.maxWeight,callback:function(t){e.$set(e.form,"maxWeight",t)},expression:"form.maxWeight"}})],1),r("el-col",{staticStyle:{padding:"0"},attrs:{span:6}},[r("tree-select",{attrs:{pass:e.form.weightUnitId,type:"unit",placeholder:"限重单位"},on:{return:e.getCargoUnitId}})],1)],1)],1):e._e(),"7"==e.typeId?r("el-form-item",{attrs:{label:"货值",prop:"cargoValue"}},[r("el-row",[r("el-col",{attrs:{span:18}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"货值",precision:2,step:.01},model:{value:e.form.cargoValue,callback:function(t){e.$set(e.form,"cargoValue",t)},expression:"form.cargoValue"}})],1),r("el-col",{staticStyle:{"margin-top":"1px"},attrs:{span:6}},[r("tree-select",{attrs:{pass:e.form.cargoCurrencyCode,type:"currency"},on:{return:e.getCargoCurrencyId}})],1)],1)],1):e._e(),"1"==e.typeId||"2"==e.typeId||"3"==e.typeId||"4"==e.typeId?r("el-form-item",{attrs:{label:"承运人",prop:"carrierCode"}},[r("el-row",{staticStyle:{margin:"0"},attrs:{gutter:5}},[r("el-col",{staticStyle:{padding:"0"},attrs:{span:10}},[r("treeselect",{attrs:{"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!1,"disable-branch-nodes":!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{select:e.handleSelectCarrierId},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(null!=a.raw.carrierIntlCode?a.raw.carrierIntlCode:" ")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,o=t.labelClassName,l=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,1728271422),model:{value:e.form.carrierCode,callback:function(t){e.$set(e.form,"carrierCode",t)},expression:"form.carrierCode"}})],1),r("el-col",{staticStyle:{padding:"0"},attrs:{span:14}},[r("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:e.form.contractType,placeholder:"合约类别",type:"contractType"},on:{return:e.getContractTypeId}})],1)],1)],1):e._e(),r("el-form-item",{attrs:{label:e.company,prop:"supplierId"}},[r("el-row",[r("el-col",{attrs:{span:10}},[r("company-select",{attrs:{"load-options":e.companyList,multiple:!1,"no-parent":!0,pass:e.form.supplierId,placeholder:"供应商","role-control":!0,"role-supplier":"1"},on:{return:function(t){e.form.supplierId=t}}})],1),r("el-col",{staticStyle:{"line-height":"26px",padding:"0"},attrs:{span:14}},[r("el-input",{attrs:{placeholder:"合约号"},model:{value:e.form.agreementNo,callback:function(t){e.$set(e.form,"agreementNo",t)},expression:"form.agreementNo"}})],1)],1)],1),r("el-form-item",{attrs:{label:"价格",prop:"currencyCode"}},[r("el-row",[r("el-col",{attrs:{span:5}},[r("tree-select",{attrs:{pass:e.form.currencyCode,type:"currency"},on:{return:e.getCurrencyId}})],1),r("el-col",{attrs:{span:14}},[r("el-input",{attrs:{placeholder:"具体费用"},nativeOn:{focusout:function(t){return e.autoCompletion(t)}},model:{value:e.form.formValue,callback:function(t){e.$set(e.form,"formValue",t)},expression:"form.formValue"}})],1),r("el-col",{attrs:{span:5}},[r("tree-select",{ref:"unitCode",attrs:{pass:e.form.unitCode,type:"unit"},on:{return:e.getUnitId}})],1)],1)],1),"8"==e.typeId?r("el-form-item",{attrs:{label:"免堆期",prop:"freeStorage"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"免堆期"},model:{value:e.form.freeStorage,callback:function(t){e.$set(e.form,"freeStorage",t)},expression:"form.freeStorage"}})],1),r("el-col",{staticStyle:{"margin-top":"1px"},attrs:{span:12}},[r("tree-select",{attrs:{pass:e.form.storageUnitCode,type:"unit"},on:{return:e.getStorageUnitId}})],1)],1)],1):e._e(),"8"==e.typeId?r("el-form-item",{attrs:{label:"超期费",prop:"demurrage"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"超期费单价",precision:2,step:.01},model:{value:e.form.demurrage,callback:function(t){e.$set(e.form,"demurrage",t)},expression:"form.demurrage"}})],1),r("el-col",{staticStyle:{"margin-top":"1px"},attrs:{span:12}},[r("tree-select",{attrs:{pass:e.form.demurrageUnitCode,type:"unit"},on:{return:e.getDemurrageUnitId}})],1)],1)],1):e._e(),r("el-form-item",{attrs:{label:"船期",prop:"logisticsSchedule"}},[r("el-input",{attrs:{autosize:{minRows:1},type:"textarea",maxlength:"400",placeholder:"船期","show-word-limit":""},model:{value:e.form.logisticsSchedule,callback:function(t){e.$set(e.form,"logisticsSchedule",t)},expression:"form.logisticsSchedule"}})],1),"1"==e.typeId||"2"==e.typeId||"3"==e.typeId?r("el-form-item",{attrs:{label:"中转港",prop:"transitPortId"}},[r("location-select",{attrs:{multiple:!1,pass:e.form.transitPortId,"load-options":e.locationOptions,"check-port":"1",en:!0},on:{return:e.getTransitPortId}})],1):e._e(),"1"==e.typeId||"2"==e.typeId||"3"==e.typeId?r("el-form-item",{attrs:{label:"物流时效",prop:"logisticsTimeliness"}},[r("el-row",{staticStyle:{margin:"0"},attrs:{gutter:2}},[r("el-col",{staticStyle:{"line-height":"26px",padding:"0"},attrs:{span:18}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"物流时效",precision:2,step:.01},model:{value:e.form.logisticsTimeliness,callback:function(t){e.$set(e.form,"logisticsTimeliness",t)},expression:"form.logisticsTimeliness"}})],1),r("el-col",{staticStyle:{padding:"0"},attrs:{span:6}},[r("tree-select",{attrs:{pass:e.form.ttUnitCode,placeholder:"物流时效柜型",type:"unit"},on:{return:e.getLogisticsUnitId}})],1)],1)],1):e._e(),r("el-form-item",{attrs:{label:"有效期",prop:"validPeriodTimeNodeId"}},["1"==e.typeId||"2"==e.typeId||"3"==e.typeId?r("el-row",{staticStyle:{margin:"0",padding:"0"}},[r("el-col",{staticStyle:{padding:"0"},attrs:{span:8}},[r("treeselect",{attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,"flatten-search-results":!0,multiple:!1,normalizer:e.nodeNormalizer,options:e.commonInfoList,placeholder:"有效期时间","show-count":!0},on:{input:e.deselectValidPeriodTimeNodeId,open:e.loadCommonInfo,select:e.getValidPeriodTimeNodeId},scopedSlots:e._u([{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,o=t.labelClassName,l=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}},{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(null!=a.raw.basCommonInfo.infoShortName?a.raw.basCommonInfo.infoShortName:null!=a.raw.basCommonInfo.infoLocalName?a.raw.basCommonInfo.infoLocalName:null!=a.raw.basCommonInfo.infoEnName?a.raw.basCommonInfo.infoEnName:"")+" ")])}}],null,!1,1609413407),model:{value:e.validPeriodTimeNodeId,callback:function(t){e.validPeriodTimeNodeId=t},expression:"validPeriodTimeNodeId"}})],1),r("el-col",{staticStyle:{padding:"0"},attrs:{span:12}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.changeTime},model:{value:e.validTime,callback:function(t){e.validTime=t},expression:"validTime"}})],1),r("el-col",{staticStyle:{"line-height":"26px",padding:"0"},attrs:{span:4}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},model:{value:e.form.isValid,callback:function(t){e.$set(e.form,"isValid",t)},expression:"form.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1):r("el-row",[r("el-col",{staticStyle:{padding:"0"},attrs:{span:18}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{"default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.changeTime},model:{value:e.validTime,callback:function(t){e.validTime=t},expression:"validTime"}})],1),r("el-col",{staticStyle:{"line-height":"26px"},attrs:{span:6}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},model:{value:e.form.isValid,callback:function(t){e.$set(e.form,"isValid",t)},expression:"form.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1),r("el-col",{staticStyle:{"padding-left":"10px"},attrs:{span:12}},[r("el-form-item",{attrs:{label:"业务须知",prop:"noticeForSales"}},[r("el-input",{key:"noticeForSales",attrs:{autosize:{minRows:6},maxlength:"400",placeholder:"业务须知,报价注意事项","show-word-limit":"",type:"textarea"},model:{value:e.form.noticeForSales,callback:function(t){e.$set(e.form,"noticeForSales",t)},expression:"form.noticeForSales"}})],1),r("el-form-item",{attrs:{label:"商务备注",prop:"psaRemark"}},[r("el-input",{key:"psaRemark",attrs:{autosize:{minRows:6},maxlength:"400",placeholder:"商务内部备注,报价注意事项","show-word-limit":"",type:"textarea"},model:{value:e.form.psaRemark,callback:function(t){e.$set(e.form,"psaRemark",t)},expression:"form.psaRemark"}})],1),r("el-form-item",{attrs:{label:"来源",prop:"isSalesRequired"}},[e._v(" "+e._s(0==e.form.isSalesRequired?"商务播报":"")+" "+e._s(1==e.form.isSalesRequired?"业务询价":"")+" "+e._s(null!=e.form.goodsTime?"货好时间："+e.parseTime(e.form.goodsTime,"{y}-{m}-{d}"):"")+" "+e._s(0==e.form.replyMark?"未回复":"")+" "+e._s(1==e.form.replyMark?"已回复":"")+" ")]),r("el-form-item",{attrs:{label:"询价备注",prop:"requireRemark"}},[e._v(" "+e._s(e.form.requireRemark)+" ")]),r("el-row",[r("el-col",{attrs:{span:17}},[r("el-form-item",{attrs:{label:"录入人",prop:"updateByName"}},[e._v(" "+e._s(e.$store.state.user.name)+" ")])],1),r("el-col",{attrs:{span:7}},[r("el-form-item",{attrs:{prop:"updateTime","label-width":"0"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:e.edit,placeholder:"录入时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.updateTime,callback:function(t){e.$set(e.form,"updateTime",t)},expression:"form.updateTime"}})],1)],1)],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.edit?"确认修改":"确 认"))]),e.edit?r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("true")}}},[e._v("另存为")]):e._e(),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.upload.title,visible:e.upload.open,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[r("el-upload",{ref:"upload",attrs:{action:e.upload.url+"?updateSupport="+e.upload.updateSupport+"&logistic="+e.serviceTypeId+"&typeId="+e.typeId,"auto-upload":!1,disabled:e.upload.isUploading,headers:e.upload.headers,limit:1,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,accept:".xlsx, .xls",drag:""}},[r("i",{staticClass:"el-icon-upload"}),r("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),r("em",[e._v("点击上传")])]),r("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[r("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[r("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),r("span",[e._v("仅允许导入xls、xlsx格式文件。")]),r("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{underline:!1,type:"primary"},on:{click:e.importTemplate}},[e._v("下载模板 ")])],1)]),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),r("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},i=[],s=r("c7eb"),o=r("1da1"),l=r("5530"),n=r("b85c"),d=(r("d3b7"),r("6062"),r("3ca3"),r("ddb0"),r("14d9"),r("159b"),r("25f0"),r("d81d"),r("a15b"),r("a9e3"),r("4de4"),r("d6c9")),c=r("ca17"),p=r.n(c),u=(r("6f8d"),r("b0b8")),m=r.n(u),h=r("5f87"),f=r("4360"),y=r("aff7"),g=r("4cf5"),I=r("2a23"),v=r("e996"),b=r("b655"),w=r("b66c"),T=r("0f6b"),S=r("6b1f"),C=r("98dc"),_=r("87df"),N=r("de8e"),q=r("bfbe"),x=r("42dd"),k=r("8eee"),P=r("0c23"),L=r("27cd"),$=r("86a4"),O=r("363f"),D=r("2947"),R=r("46aa"),z=r("2a3a"),F=r("efbd"),U=r("8eca"),E=r("4af4"),Q=r("ed08"),j=r("fba1"),V=r("1122"),B=r("e8bd"),W=(r("717a"),r("6e71")),M={name:"Freight",dicts:["sys_normal_disable","sys_yes_no"],components:{CompanySelect:W["a"],destinationLocation:B["default"],loadingLocation:V["default"],Treeselect:p.a,businessRemark:g["default"],logisticsType:I["default"],serviceType:v["default"],cargoPrice:S["default"],cargoType:C["default"],company:b["default"],contract:w["default"],currency:T["default"],demurrage:_["default"],departureTodestination:x["default"],freeStorage:k["default"],loading:P["default"],price:L["default"],recorder:$["default"],salesRemark:O["default"],shippingDate:D["default"],unit:R["default"],validTime:z["default"],carrier:F["default"],local:U["default"],departure:q["default"],destination:N["default"],weight:E["default"]},props:["typeId"],data:function(){return{showLeft:3,showRight:21,carrierCode:null,unitCode:null,queryCarrierIds:null,temCarrierList:[],locationOptions:new Set,logisticsEfficiencyNodeId:null,validPeriodTimeNodeId:null,serviceTypeList:[],businessList:[],carrierList:[],commonInfoList:[],serviceType:null,refreshTable:!0,serviceTypeId:null,logisticsTypeId:null,updateBy:null,charge:null,chargeId:null,validTime:[],company:"订舱口",types:null,edit:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,freightList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,typeId:this.typeId,logisticsTypeId:null,inquiryNo:null,cargoTypeIds:[],carrierIds:[],companyIds:[],departureIds:[],lineDepartureIds:[],transitPortIds:[],destinationIds:[],lineDestinationIds:[],contractTypeIds:[],unitIds:[],validFrom:null,validTo:null,freightQuery:null,updateBy:null,recordFrom:null,recordTo:null,dateRange:null},upload:{open:!1,title:"",isUploading:!1,updateSupport:!0,headers:{Authorization:"Bearer "+Object(h["a"])()},url:"/prod-api/system/freight/importData"},form:{},pattern:"2"==this.typeId||"3"==this.typeId?"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,5}$/":"5"==this.typeId||"6"==this.typeId||"7"==this.typeId?"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,1}$/":"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,4}$/",rules:{polId:[{required:"1"==this.typeId||"2"==this.typeId||"3"==this.typeId,trigger:"blur"}],destinationPortId:[{required:"5"!=this.typeId&&"9"!=this.typeId,trigger:"blur"}],carrierCode:[{required:!0,trigger:"blur"}],supplierId:[{required:!0,trigger:"blur"}],unitCode:[{required:!0,trigger:"blur"}],serviceTypeId:[{required:!0,trigger:"blur"}],logisticsTypeId:[{required:!0,trigger:"blur"}],formValue:[{required:!0,pattern:this.pattern,trigger:"blur"}]},checkPort:null,companyList:[]}},computed:{columns:{get:function(){return"1"==this.typeId?(this.logisticsTypeId=1,this.serviceTypeId=1,this.roleId=200,this.ttUnitCode="Day",this.chargeId=1,this.unitId=2,this.unitCode="Ctnr",this.serviceType="整柜海运",this.charge="海运费",this.types="seafreight",this.$store.state.listSettings.seafreightSetting):"2"==this.typeId?(this.logisticsTypeId=10,this.serviceTypeId=10,this.roleId=200,this.ttUnitCode="Day",this.chargeId=7,this.unitId=5,this.unitCode="KGS",this.serviceType="空运",this.charge="空运费",this.types="airfreight",this.$store.state.listSettings.airfreightSetting):"3"==this.typeId?(this.serviceTypeId=20,this.ttUnitCode="Day",this.chargeId=52,this.unitId=5,this.unitCode="KGS",this.serviceType="铁路",this.charge="铁路费",this.types="tailwayfreight",this.$store.state.listSettings.seafreightSetting):"4"==this.typeId?(this.serviceTypeId=40,this.logisticsTypeId=40,this.roleId=200,this.ttUnitCode="Day",this.chargeId=43,this.unitId=5,this.unitCode="KGS",this.serviceType="快递",this.charge="快递费",this.company="快递公司",this.types="expressdelivery",this.$store.state.listSettings.expressdeliverySetting):"5"==this.typeId?(this.roleId=203,this.chargeId=8,this.unitId=2,this.unitCode="Ctnr",this.serviceType="拖车",this.charge="拖车费",this.company="拖车行",this.types="trailer",this.$store.state.listSettings.trailerSetting):"6"==this.typeId?(this.roleId=205,this.chargeId=42,this.unitId=7,this.unitCode="BL",this.serviceType="报关",this.charge="报关费",this.company="报关行",this.types="declare",this.$store.state.listSettings.declareSetting):"7"==this.typeId?(this.roleId=205,this.chargeId=53,this.unitId=7,this.unitCode="BL",this.serviceType="清关",this.charge="清关费",this.company="报关行",this.types="clearance",this.$store.state.listSettings.customsSetting):"8"==this.typeId?(this.serviceTypeId=80,this.logisticsTypeId=80,this.chargeId=46,this.roleId=204,this.serviceType="仓储",this.charge="仓储费",this.company="仓储公司",this.types="warehouse",this.$store.state.listSettings.warehouseSetting):"9"==this.typeId?(this.chargeId=51,this.roleId=204,this.serviceType="拓展服务",this.charge="拓展服务费",this.company="供应商",this.types="extend",this.$store.state.listSettings.insuranceSetting):void 0}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.logisticsTypeId":function(e){var t=this,r=[];if(void 0!=this.carrierList){var a,i=Object(n["a"])(this.carrierList);try{for(i.s();!(a=i.n()).done;){var s=a.value;if(s.serviceTypeId==e&&r.push(s),void 0!=s.children&&s.children.length>0){var o,l=Object(n["a"])(s.children);try{for(l.s();!(o=l.n()).done;){var d=o.value;d.serviceTypeId==e&&r.push(d)}}catch(g){l.e(g)}finally{l.f()}}}}catch(g){i.e(g)}finally{i.f()}}this.temCarrierList=r;var c,p=Object(n["a"])(this.carrierList);try{for(p.s();!(c=p.n()).done;){var u=c.value;if(null!=u.carrier&&null!=u.carrier.carrierId&&void 0!=u.carrier.carrierId&&null!=this.carrierId&&void 0!=this.carrierId&&u.carrier.carrierId==this.carrierId&&-1==this.carrierId&&(this.carrierId=u.serviceTypeId),void 0!=u.children&&u.children.length>0){var m,h=Object(n["a"])(u.children);try{for(h.s();!(m=h.n()).done;){var y=m.value;null!=y.carrier&&null!=y.carrier.carrierId&&void 0!=y.carrier.carrierId&&null!=this.carrierId&&void 0!=this.carrierId&&y.carrier.carrierId==this.carrierId&&-1==this.carrierId&&(this.carrierId=y.serviceTypeId)}}catch(g){h.e(g)}finally{h.f()}}}}catch(g){p.e(g)}finally{p.f()}(0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType)&&f["a"].dispatch("getServiceTypeList").then((function(){t.$store.state.data.serviceTypeList.forEach((function(r){if(r.children){var a,i=Object(n["a"])(r.children);try{for(i.s();!(a=i.n()).done;){var s=a.value;s.serviceTypeId===e&&(t.checkPort=s.typeId.toString())}}catch(g){i.e(g)}finally{i.f()}}r.serviceTypeId===e&&(t.checkPort=r.typeId.toString())}))}))}},created:function(){var e=this;this.getList().then((function(){e.loadCarrier(),e.loadCommonInfo()}))},methods:{changeTime:function(e){void 0==e&&(this.form.validFrom=null,this.form.validTo=null),this.form.validFrom=e[0],this.form.validTo=e[1]},remoteSupplier:function(e){var t=this,r={pageNum:1,pageSize:99999,roleSupplier:"1",roleIds:[this.roleId],serviceTypeIds:[e?this.queryParams.serviceTypeId:this.form.serviceTypeId],cargoTypeIds:e?this.queryParams.cargoTypeIds:this.form.cargoTypeIds,locationDepartureIds:[e?this.queryParams.polId:this.form.polId],locationDestinationIds:[e?this.queryParams.destinationPortId:this.form.destinationPortId],carrierIds:e?this.queryParams.carrierIds:[this.form.carrierId]};Object(y["f"])(r).then((function(e){t.$refs.supplier.getLoadOptions(e.rows)}))},cancel:function(){this.open=!1,this.edit=!1,this.reset()},reset:function(){this.form={freightId:null,serviceTypeId:this.serviceTypeId,agreementNo:null,agreementCode:null,carrierId:null,carrierCode:null,supplierId:null,logisticsTypeId:this.logisticsTypeId,importExport:"1",precarriageRegionId:null,polId:null,transitPortId:null,destinationPortId:null,maxWeight:null,cargoTypeIds:[1],weightUnitId:5,cargoValue:null,cargoCurrencyCode:null,chargeId:this.chargeId,chargeTypeId:null,currencyId:"1"==this.typeId?10:1,unitId:this.unitId,unitCode:this.unitCode,priceA:null,priceB:null,priceC:null,priceD:null,priceE:null,shippingDate:null,shippingWeek:["Mon","Tue","Wed","Thur","Fri","Sat","Sun"],logisticsSchedule:null,logisticsEfficiencyNodeId:null,logisticsTimeliness:null,ttUnitCode:this.ttUnitCode,validPeriodTimeNodeId:null,freeStorage:null,demurrage:null,storageUnitCode:null,demurrageUnitCode:null,validFrom:null,validTo:null,isValid:"Y",status:"0",psaRemark:null,noticeForSales:null,requireRemark:null,createBy:null,createTime:null,updateBy:this.$store.state.user.sid,updateTime:Object(j["f"])(new Date),deleteBy:null,deleteTime:null,deleteStatus:"0",isSalesRequired:0,replyMark:0},this.validTime=[],this.carrierId=null,this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.queryParams.validFrom=this.queryParams.dateRange&&this.queryParams.dateRange[0]?this.queryParams.dateRange[0]:null,this.queryParams.validTo=this.queryParams.dateRange&&this.queryParams.dateRange[1]?this.queryParams.dateRange[1]:null,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.freightId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){if(this.reset(),this.open=!0,this.title="添加"+this.$route.matched[1].meta.title,this.commonInfoList.length>0){var e,t=Object(n["a"])(this.commonInfoList[0].children);try{for(t.s();!(e=t.n()).done;){var r=e.value;46==r.basCommonInfo.infoId?(this.logisticsEfficiencyNodeId=r.infoTypeId,this.validPeriodTimeNodeId=r.infoTypeId):2==this.typeId&&61==r.basCommonInfo.infoId&&(this.validPeriodTimeNodeId=r.infoTypeId)}}catch(i){t.e(i)}finally{t.f()}}if(7!=this.typeId){var a=new Date;this.validTime.push(a),this.validTime.push(new Date(new Date(a.getFullYear(),a.getMonth()+1,1)-864e5))}1==this.typeId&&(this.form.currencyCode="USD"),this.validPeriodTimeNodeId},handleUpdate:function(e){var t=this;this.reset(),this.edit=!0,this.loading=!0;var r=e.freightId||this.ids;Object(d["c"])(r).then((function(e){t.form=e.data,t.form.shippingWeek=null!=e.data.shippingWeek?e.data.shippingWeek.split(","):null;var r,a=Object(n["a"])(t.commonInfoList[0].children);try{for(a.s();!(r=a.n()).done;){var i=r.value;i.basCommonInfo.infoId==e.data.validPeriodTimeNodeId&&(t.validPeriodTimeNodeId=i.infoTypeId),i.basCommonInfo.infoId==e.data.logisticsEfficiencyNodeId&&(t.logisticsEfficiencyNodeId=i.infoTypeId)}}catch(u){a.e(u)}finally{a.f()}if(t.$set(t.form,"formValue",(null!=e.data.priceB?e.data.priceB+(null!=e.data.priceC?"/":""):"")+(null!=e.data.priceC?e.data.priceC+(null!=e.data.priceD?"/":""):"")+(null!=e.data.priceD?e.data.priceD+(null!=e.data.priceE?"/":""):"")+(null!=e.data.priceE?e.data.priceE+(null!=e.data.priceA?"/":""):"")+(null!=e.data.priceA?e.data.priceA:"")),t.temCarrierList.length>0){var s,o=Object(n["a"])(t.temCarrierList);try{for(o.s();!(s=o.n()).done;){var l=s.value;if(null!=l.carrier&&null!=l.carrier.carrierId&&void 0!=l.carrier.carrierId&&null!=e.data.carrierId&&void 0!=e.data.carrierId&&l.carrier.carrierId==e.data.carrierId&&(t.carrierId=l.serviceTypeId),void 0!=l.children&&l.children.length>0){var d,c=Object(n["a"])(l.children);try{for(c.s();!(d=c.n()).done;){var p=d.value;null!=p.carrier&&null!=p.carrier.carrierId&&void 0!=p.carrier.carrierId&&null!=e.data.carrierId&&void 0!=e.data.carrierId&&p.carrier.carrierId==e.data.carrierId&&(t.carrierId=p.serviceTypeId)}}catch(u){c.e(u)}finally{c.f()}}}}catch(u){o.e(u)}finally{o.f()}}e.data.validFrom&&t.validTime.push(e.data.validFrom),e.data.validTo&&t.validTime.push(e.data.validTo),t.form.cargoTypeIds=e.cargoTypeIds,t.locationOptions=e.locationOptions,t.companyList=e.company?[e.company]:[],t.open=!0,t.title="修改"+t.$route.matched[1].meta.title,t.loading=!1}))},autoCompletion:function(){if(this.form.formValue){var e=this.form.formValue.split("/");2==e.length&&(e[2]=e[1]),this.form.formValue=e.join("/")}},submitForm:function(e){var t=this;this.$refs["form"].validate((function(r){if(r){var a=t.form.formValue.split("/");if(a.length>1&&3!=t.typeId?(t.form.priceB=void 0!=a[0]?Number(a[0]):null,t.form.priceC=void 0!=a[1]?Number(a[1]):null,t.form.priceD=void 0!=a[2]?Number(a[2]):void 0!=a[1]?Number(a[1]):null,t.form.priceA=void 0!=a[3]?Number(a[3]):null,t.form.unitId=t.unitId):a.length>1&&3==t.typeId?(t.form.priceB=void 0!=a[0]?Number(a[0]):null,t.form.priceC=void 0!=a[1]?Number(a[1]):null,t.form.priceD=void 0!=a[2]?Number(a[2]):null,t.form.priceE=void 0!=a[3]?Number(a[3]):null,t.form.priceA=void 0!=a[4]?Number(a[4]):null,t.form.unitId=t.unitId):1==a.length&&(t.form.priceB=null,t.form.priceC=null,t.form.priceD=null,t.form.priceE=null,t.form.priceA=Number(t.form.formValue)),t.form.replyMark=1,null!=t.form.shippingWeek&&t.form.shippingWeek.length>0?t.form.shippingWeek=t.form.shippingWeek.toString():t.form.shippingWeek=null,"true"==e&&(t.form.freightId=null,t.form.inquiryNo=null),1!=t.typeId&&2!=t.typeId&&3!=t.typeId&&4!=t.typeId||(t.form.logisticsTypeId=t.form.serviceTypeId),null!==t.form.formValue&&1===t.form.formValue.split("/").length&&"Ctnr"===t.form.unitCode)return void t.$message.error("请选择正确单位");null!=t.form.freightId?Object(d["e"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.edit=!1,t.getList()})):Object(d["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.reset(),t.getList()}))}}))},handleDelete:function(e){var t=e.freightId||this.ids,r=this;r.$modal.confirm('是否确认删除基础运费编号为"'+t+'"的数据项？').then((function(){return Object(d["b"])(t).then((function(e){r.getList(),r.$modal.msgSuccess("成功删除"+t.length+"个数据，但有"+e.data+"不能删除")}))}))},handleExport:function(){this.download("system/freight/export",Object(l["a"])({},this.queryParams),"SeaFCL_Export_".concat(Object(Q["d"])(new Date),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/freight/importTemplate",{},"SeaFCL_Template_".concat(Object(Q["d"])(new Date),".xlsx"))},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$message.info(e.msg),"全部上传成功"!=e.msg&&this.download("system/freight/failList",{},"上传失败列表.xlsx"),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},queryLogisticsTypeId:function(e){void 0==e&&(this.queryParams.logisticsTypeId=1,this.serviceType="海运整箱")},queryLogisticsType:function(e){this.queryParams.logisticsTypeId=e.serviceTypeId,this.serviceType=e.serviceLocalName,this.handleQuery()},queryContractTypeIds:function(e){this.queryParams.contractTypeIds=e,this.handleQuery()},queryUnitIds:function(e){this.queryParams.unitIds=e,this.handleQuery()},queryCompanyIds:function(e){this.queryParams.companyIds=e,this.handleQuery()},queryPrecarriageRegionId:function(e){this.queryParams.loadingIds=[e],this.queryParams.precarriageRegionId=e,this.handleQuery()},queryDepartureIds:function(e){this.queryParams.departureIds=e,this.handleQuery()},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},queryTransitPortIds:function(e){this.queryParams.transitPortIds=e,this.handleQuery()},queryDestinationIds:function(e){this.queryParams.destinationIds=e,this.handleQuery()},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},getContractTypeId:function(e){this.form.agreementCode=e},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},handleSelectRecordBy:function(e){this.queryParams.updateBy=e.staffId,this.handleQuery()},cleanRecordBy:function(e){void 0==e&&(this.queryParams.updateBy=null,this.handleQuery())},handleSelectCarrierId:function(e){this.form.carrierCode=e.carrierIntlCode},deselectCarrierId:function(e){void 0==e&&(this.form.carrierId=null)},getLogisticsTypeId:function(e){this.form.logisticsTypeId=e},getServiceTypeId:function(e){this.form.serviceTypeId=e},getChargeId:function(e){this.form.chargeId=e},getCompanyId:function(e){this.form.supplierId=e},getLoadingId:function(e){this.form.precarriageRegionId=e},getDepartureId:function(e){this.form.polId=e},getTransitPortId:function(e){this.form.transitPortId=e},getDestinationId:function(e){this.form.destinationPortId=e},getCargoCurrencyId:function(e){this.form.cargoCurrencyCode=e},getCargoUnitId:function(e){this.form.weightUnitId=e},getStorageUnitId:function(e){this.form.storageUnitCode=e},getDemurrageUnitId:function(e){this.form.demurrageUnitCode=e},getCurrencyId:function(e){this.form.currencyCode=e},getUnitId:function(e){var t=this;(null===this.form.formValue||this.form.formValue&&1===this.form.formValue.split("/").length)&&"Ctnr"===e?this.$nextTick((function(){t.$refs.unitCode.clear(),t.$message.error("请选择正确单位")})):(this.form.unitCode=e,console.log(e))},getLogisticsUnitId:function(e){this.form.ttUnitCode=e},getValidPeriodTimeNodeId:function(e){this.form.validPeriodTimeNodeId=e.basCommonInfo.infoId},deselectValidPeriodTimeNodeId:function(e){void 0==e&&(this.form.validPeriodTimeNodeId=null)},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},refreshColumns:function(){var e=this;this.refreshTable=!1,this.$nextTick((function(){e.refreshTable=!0}))},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?f["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadCarrier:function(){var e=this;0==this.$store.state.data.carrierList.length||this.$store.state.data.redisList.carrier?f["a"].dispatch("getCarrierList").then((function(){e.carrierList=e.$store.state.data.carrierList.filter((function(t){return t.typeIds&&-1!=t.typeIds.split(",").indexOf(e.typeId)}))})):this.carrierList=this.$store.state.data.carrierList.filter((function(t){return t.typeIds&&-1!=t.typeIds.split(",").indexOf(e.typeId)}))},loadCommonInfo:function(){var e=this;0==this.$store.state.data.logisticsTimeNodeList.length||this.$store.state.data.redisList.logisticsTimeNodeList?f["a"].dispatch("getLogisticsTimeNodeList").then((function(){e.commonInfoList=e.$store.state.data.logisticsTimeNodeList})):this.commonInfoList=this.$store.state.data.logisticsTimeNodeList},getList:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(d["d"])(e.queryParams).then((function(t){e.freightList=t.rows,e.total=t.total,e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},tableRowClassName:function(e){var t=e.row,r=Object(j["f"])(new Date,"{y}-{m}-{d}"),a=Object(j["f"])(t.validFrom,"{y}-{m}-{d}"),i=Object(j["f"])(t.validTo,"{y}-{m}-{d}");return a<r<i?"":i<r?"valid-row":a>r?"valid-before":""},carrierNormalizer:function(e){return{id:e.carrierIntlCode,label:(null!=e.carrierShortName?e.carrierShortName:"")+" "+(null!=e.carrierLocalName?e.carrierLocalName:"")+" "+(null!=e.carrierEnName?e.carrierEnName:"")+","+m.a.getFullChars((null!=e.carrierShortName?e.carrierShortName:"")+" "+(null!=e.carrierLocalName?e.carrierLocalName:""))}},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+m.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+m.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+m.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},nodeNormalizer:function(e){var t;return t=null==e.basCommonInfo?(null!=e.infoTypeShortName?e.infoTypeShortName:"")+" "+(null!=e.infoTypeLocalName?e.infoTypeLocalName:"")+" "+(null!=e.infoTypeEnName?e.infoTypeEnName:"")+","+m.a.getFullChars(e.infoTypeShortName+e.infoTypeLocalName):(null!=e.basCommonInfo.infoShortName?e.basCommonInfo.infoShortName:"")+" "+(null!=e.basCommonInfo.infoLocalName?e.basCommonInfo.infoLocalName:"")+" "+(null!=e.basCommonInfo.infoEnName?e.basCommonInfo.infoEnName:"")+","+m.a.getFullChars(e.basCommonInfo.infoShortName+e.basCommonInfo.infoLocalName),{id:e.infoTypeId,label:t}}}},A=M,G=r("2877"),H=Object(G["a"])(A,a,i,!1,null,null,null);t["default"]=H.exports},b655:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{placement:"top",disabled:null==e.scope.row.company||e.scope.row.company.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.company)+" ")]),r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.company)+" ")]),r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])])])],1)},i=[],s={name:"company",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=s,l=r("2877"),n=Object(l["a"])(o,a,i,!1,null,"6d377e90",null);t["default"]=n.exports},e8bd:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.loading||e.scope.row.loading.length<5,"open-delay":500,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s(e.scope.row.destinationLocation)+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.destinationLocation)+" ")])])])],1)},i=[],s={name:"destinationLocation",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=s,l=r("2877"),n=Object(l["a"])(o,a,i,!1,null,"2890467b",null);t["default"]=n.exports}}]);