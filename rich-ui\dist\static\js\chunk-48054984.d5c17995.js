(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-48054984","chunk-2d0a3aa6"],{"02c1":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.scope.row.slipFile?n("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-search"},on:{click:function(t){e.previewImgOpen=!0}}}):e._e(),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"银行水单",visible:e.previewImgOpen,"append-to-body":"",height:"50%",width:"50%"},on:{"update:visible":function(t){e.previewImgOpen=t}}},[n("el-image",{attrs:{src:e.scope.row.slipFile}})],1)],1)},r=[],a={name:"imgPreview",props:["scope"],data:function(){return{previewImgOpen:!1}}},i=a,l=n("2877"),c=Object(l["a"])(i,o,r,!1,null,"6d267c04",null);t["default"]=c.exports},"36dc":function(e,t,n){"use strict";n.d(t,"g",(function(){return r})),n.d(t,"f",(function(){return a})),n.d(t,"a",(function(){return i})),n.d(t,"h",(function(){return l})),n.d(t,"c",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"e",(function(){return d}));var o=n("b775");function r(e){return Object(o["a"])({url:"/system/bankrecord/list",method:"get",params:e})}function a(e){return Object(o["a"])({url:"/system/bankrecord/"+e,method:"get"})}function i(e){return Object(o["a"])({url:"/system/bankrecord",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/system/bankrecord",method:"put",data:e})}function c(e){return Object(o["a"])({url:"/system/bankrecord/"+e,method:"delete"})}function s(e,t){var n={bankRecordId:e,status:t};return Object(o["a"])({url:"/system/bankrecord/changeStatus",method:"put",data:n})}function u(e){return Object(o["a"])({url:"/system/bankrecord/deleteImg",method:"delete",params:e})}function d(e){return Object(o["a"])({url:"/system/bankrecord/statistics",method:"post",data:e})}},bcaf:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],attrs:{size:"mini",type:"text",disabled:0==e.scope.row.opAccept},on:{click:e.openBankSlip}},[e._v(" "+e._s("["+e.currency("pay"===e.type?e.scope.row.cnInRmb:e.scope.row.dnInRmb,{separator:",",symbol:"¥",precision:2}).format()+("pay"===e.type?" "+(e.scope.row.sqdDnPaySlipStatus?e.scope.row.sqdDnPaySlipStatus:"-"):" "+(e.scope.row.sqdDnReceiveSlipStatus?e.scope.row.sqdDnReceiveSlipStatus:"-"))+"]")+" ")]),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"pay"===e.type?"付款水单":"收款水单",visible:e.bankSlipOpen,"append-to-body":"","destroy-on-close":"",height:"60%",width:"60%"},on:{"update:visible":function(t){e.bankSlipOpen=t},open:e.loadCompanyOptions,close:function(t){e.showDetail=!1}}},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{"margin-top":"20px"},attrs:{data:e.bankrecordList,"highlight-current-row":"",stripe:""}},[n("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),n("el-table-column",{attrs:{align:"center",label:"银行流水",prop:"bankRecordNo"}}),n("el-table-column",{attrs:{align:"center",label:"收支",prop:"isRecievingOrPaying",width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(1==t.row.isRecievingOrPaying?"付":"收"))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"所属公司",prop:"sqdPaymentTitleCode",width:"50"}}),n("el-table-column",{attrs:{align:"center",label:"银行账户",prop:"bankAccountCode",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?n("div",[e._v(" "+e._s(t.row.bankAccountCode)+" ")]):n("tree-select",{staticClass:"edit",class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,flat:!1,multiple:!1,pass:t.row.bankAccountCode,placeholder:"银行账户",type:"companyAccount"},on:{return:function(e){t.row.bankAccountCode=e},returnData:function(e){t.row.sqdPaymentTitleCode=e.sqdBelongToCompanyCode}}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"结算公司",prop:"sqdClearingCompanyShortname"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-select",{class:1==t.row.isBankRecordLocked&&1==t.row.slipConfirmed?"":"edit",attrs:{disabled:1==t.row.isBankRecordLocked&&1==t.row.slipConfirmed,placeholder:"请选择"},model:{value:t.row.clearingCompanyId,callback:function(n){e.$set(t.row,"clearingCompanyId",n)},expression:"scope.row.clearingCompanyId"}},e._l(e.clients,(function(e){return n("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),n("el-table-column",{attrs:{align:"center",label:"银行币种",prop:"bankCurrencyCode",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?n("div",[e._v(" "+e._s(t.row.bankCurrencyCode)+" ")]):n("tree-select",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,pass:t.row.bankCurrencyCode,placeholder:"币种",type:"currency"},on:{return:function(e){t.row.bankCurrencyCode=e}}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"水单金额",prop:"slipAmount"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?n("div",[e._v(" "+e._s(t.row.slipAmount)+" ")]):n("el-input",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isBankSlipConfirmed,placeholder:"水单金额"},model:{value:t.row.slipAmount,callback:function(n){e.$set(t.row,"slipAmount",n)},expression:"scope.row.slipAmount"}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"水单"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[n("div",{staticStyle:{display:"flex"}},[0==t.row.slipConfirmed?n("el-upload",{ref:"bankSlipUpload",staticClass:"upload-demo",staticStyle:{flex:"1"},attrs:{action:e.uploadUrl,"auto-upload":!1,disabled:!t.row.bankRecordNo,"http-request":e.customHttpRequest,"on-change":e.handleChange,"on-error":e.handleError,"on-success":e.handleSuccess,"show-file-list":!1,action:"xxx"}},[n("el-button",{staticStyle:{color:"rgb(103, 194, 58)"},attrs:{icon:"el-icon-top-right",type:"text"}})],1):e._e(),t.row.slipFile||t.row.slipFile&&t.row.slipConfirmed?n("img-preview",{staticStyle:{flex:"1"},attrs:{scope:t}}):e._e(),t.row.slipFile&&0==t.row.slipConfirmed?n("el-button",{staticStyle:{flex:"1",color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(n){return e.deleteBankSlip(t.row)}}}):e._e()],1)])]}}])}),n("el-table-column",{attrs:{align:"center",label:"水单日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?n("div",[e._v(" "+e._s(e.parseTime(t.row.slipDate,"{y}-{m}-{d}"))+" ")]):n("el-date-picker",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,clearable:"","default-time":"12:00:00",placeholder:"银行时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.row.slipDate,callback:function(n){e.$set(t.row,"slipDate",n)},expression:"scope.row.slipDate"}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"相关操作单号",prop:"sqdRaletiveRctList"}}),n("el-table-column",{attrs:{label:"水单确认",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.slipConfirmed?"√":"-")+" ")]}}])}),"receive"===e.type?n("el-table-column",{attrs:{align:"center",label:"实收金额",prop:"actualBankRecievedAmount"}}):e._e(),"pay"===e.type?n("el-table-column",{attrs:{align:"center",label:"实付金额",prop:"actualBankPaidAmount"}}):e._e(),n("el-table-column",{attrs:{align:"center",label:"银行时间",prop:"bankRecordTime",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.bankRecordTime,"{y}-{m}-{d}")))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"流水审核",prop:"isBankRecordLocked"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(1==t.row.isBankRecordLocked?"√":"-"))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.slipConfirmed?n("el-button",{attrs:{icon:"el-icon-top-right",type:"text"},on:{click:function(n){return e.submitForm(t.row)}}}):e._e(),0==t.row.slipConfirmed?n("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(n){return e.handleDelete(t.row)}}}):e._e()]}}])})],1),n("el-button",{attrs:{type:"text"},on:{click:e.handleAdd}},[e._v("[+]")]),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1)},r=[],a=n("c7eb"),i=n("1da1"),l=(n("d81d"),n("14d9"),n("b0c0"),n("99af"),n("d3b7"),n("72f9")),c=n.n(l),s=n("ca17"),u=n.n(s),d=n("6e71"),p=n("36dc"),m=n("fba1"),f=n("b775"),b=n("02c1"),h=n("c2aa"),y=n("b0b8"),g=n.n(y),w=n("aff7"),k={name:"bankSlip",components:{ImgPreview:b["default"],CompanySelect:d["a"],Treeselect:u.a},props:["scope","type"],data:function(){return{queryParams:{pageNum:1,pageSize:20},form:{},rules:{},open:!1,showDetail:!1,bankSlipOpen:!1,bankrecordList:[],total:0,loading:!1,companyList:[],fileList:[],imageFile:null,uploadUrl:"/system/bankrecord/upload",imgUrl:"",bankSlipPreview:!1,previewImgOpen:!1,clients:[]}},beforeMount:function(){},computed:{isLocked:function(){return 1==this.form.isBankRecordLocked},isBankSlipConfirmed:function(){return 1==this.form.slipConfirmed}},methods:{loadCompanyOptions:function(){var e=this,t=[this.scope.row.clientId];this.scope.row.relationClientIdList.split(",").length>0&&this.scope.row.relationClientIdList.split(",").map((function(e){return parseInt(e)?t.push(parseInt(e)):null})),Object(w["h"])({companyIds:t}).then((function(t){e.clients=t.rows}))},selectCompany:function(e,t){e.clearingCompanyId=t.companyId,e.sqdClearingCompanyShortname=t.companyShortName},deleteBankSlip:function(e){var t=this;return Object(i["a"])(Object(a["a"])().mark((function n(){return Object(a["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,Object(p["d"])({url:e.slipFile});case 3:n.next=9;break;case 5:return n.prev=5,n.t0=n["catch"](0),n.next=9,Object(p["h"])({bankRecordId:e.bankRecordId,slipFile:null,isRecievingOrPaying:"pay"===t.type?"1":"0",bankRecordNo:e.bankRecordNo});case 9:return n.next=11,t.getList();case 11:case"end":return n.stop()}}),n,null,[[0,5]])})))()},customHttpRequest:function(e){var t=this,n=new FormData;n.append("file",e.file),Object(f["a"])({url:"/system/bankrecord/uploadImg",method:"post",data:n}).then((function(n){e.onSuccess(n,e.file),t.imgUrl=n.url,t.form.slipFile=n.url,e.row.slipFile=n.url})).catch((function(t){e.onError(t)}))},handleChange:function(e,t){var n=e.name.substring(e.name.lastIndexOf(".")),o="".concat(this.form.bankRecordNo).concat(n);this.imageFile=new File([e.raw],o,{type:e.type}),this.$message.success("图片已选择")},handleSuccess:function(e,t,n){this.form.slipFile=e.url},handleError:function(e,t,n){this.$message.error("Upload failed:",e)},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)},handleExceed:function(e,t){this.$message.warning("当前限制选择 3 个文件，本次选择了 ".concat(e.length," 个文件，共选择了 ").concat(e.length+t.length," 个文件"))},beforeRemove:function(e,t){return this.$confirm("确定移除 ".concat(e.name,"？"))},uploadImage:function(e){var t=this;return new Promise((function(n,o){t.customHttpRequest({file:t.imageFile,onSuccess:function(e){t.handleSuccess(e),n(e)},onError:function(e){t.handleError(e),o(e)},row:e})}))},submitForm:function(e){var t=this;return Object(i["a"])(Object(a["a"])().mark((function n(){return Object(a["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!t.imageFile){n.next=3;break}return n.next=3,t.uploadImage(e);case 3:console.log(e),Object(p["h"])(e).then((function(e){t.$modal.msgSuccess("修改成功"),t.getList()})),"pay"===t.type?Object(h["s"])({rctId:t.scope.row.rctId,sqdDnPaySlipStatus:"√"}).then((function(e){})):Object(h["s"])({rctId:t.scope.row.rctId,sqdDnReceiveSlipStatus:"√"}).then((function(e){}));case 6:case"end":return n.stop()}}),n)})))()},parseTime:m["f"],currency:c.a,openBankSlip:function(){var e=this;return Object(i["a"])(Object(a["a"])().mark((function t(){return Object(a["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getList();case 2:e.bankSlipOpen=!0;case 3:case"end":return t.stop()}}),t)})))()},getList:function(){var e=this;this.loading=!0,Object(p["g"])({clearingCompanyId:this.scope.row.clientId,isRecievingOrPaying:"pay"===this.type?"1":"0",rctNo:this.scope.row.rctNo}).then((function(t){t.rows.map((function(t){return t.clients=e.clients})),e.bankrecordList=t.rows,e.total=t.total?t.total:0,e.loading=!1,t.rows&&0===t.rows.length&&"receive"===e.type&&null===e.scope.row.sqdDnReceiveSlipStatus&&Object(h["s"])({rctId:e.scope.row.rctId,sqdDnReceiveSlipStatus:"-"}).then((function(e){})),t.rows&&0===t.rows.length&&"pay"===e.type&&null===e.scope.row.sqdDnPaySlipStatus&&Object(h["s"])({rctId:e.scope.row.rctId,sqdDnPaySlipStatus:"-"}).then((function(e){}))}))},selectBankAccount:function(e){console.log(e),this.form.sqdPaymentTitleCode=e.sqdBelongToCompanyCode},handleDelete:function(e){var t=this,n=e.bankRecordId||this.ids;this.$confirm("是否确认删除？","提示",{customClass:"modal-confirm"}).then((function(){return Object(p["c"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleUpdate:function(e){var t=this;this.add=!1,this.reset();var n=e.bankRecordId||this.ids;Object(p["f"])(n).then((function(e){t.form=e.data,t.form.chargeType="订单",t.open=!0,t.title="查看银行流水-销账明细",t.companyList=[e.companyList]}))},verify:function(){var e=this;null!==this.form.clearingCompanyId?(this.form.isBankRecordLocked=1,Object(p["h"])(this.form).then((function(t){e.$message.success("修改成功")}))):this.$message.warning("请输入结算公司")},reset:function(){this.form={bankRecordId:null,isRecievingOrPaying:null,sqdPaymentTitleCode:null,bankAccountCode:null,clearingCompanyId:null,sqdClearingCompanyShortname:null,chargeTypeId:null,chargeDescription:null,bankCurrencyCode:null,actualBankRecievedAmount:null,actualBankPaidAmount:null,bankRecievedHandlingFee:null,bankPaidHandlingFee:null,bankRecievedExchangeLost:null,bankPaidExchangeLost:null,sqdBillRecievedAmount:null,sqdBillPaidAmount:null,billRecievedWriteoffAmount:null,billPaidWriteoffAmount:null,sqdBillRecievedWriteoffBalance:null,sqdBillPaidWriteoffBalance:null,writeoffStatus:"0",bankRecordTime:null,paymentTypeCode:null,voucherNo:null,invoiceNo:null,bankRecordRemark:null,bankRecordByStaffId:null,bankRecordUpdateTime:null,isBankRecordLocked:null,isWriteoffLocked:null,sqdChargeIdList:null,sqdRaletiveRctList:null,sqdRaletiveInvoiceList:null,sqdRsStaffId:null,writeoffRemark:null,writeoffStaffId:null,writeoffTime:null,chargeType:"订单"},this.resetForm("form")},handleAdd:function(){var e=this,t={};t.isRecievingOrPaying="pay"===this.type?"1":"0",t.paymentTypeCode="T/T",t.chargeType="订单",t.chargeTypeId=2,t.clearingCompanyId=this.scope.row.clientId,t.sqdClearingCompanyShortname=this.scope.row.clientSummary.split("/")[1],t.sqdRaletiveRctList=this.scope.row.rctNo,Object(p["a"])(t).then((function(t){e.form=t.data,e.$modal.msgSuccess("新增成功"),e.getList()}))},isRecievingOrPayingNormalizer:function(e){return{id:e.value,label:e.label}},companyNormalizer:function(e){return{id:e.companyId,label:(null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:"")+","+g.a.getFullChars((null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:""))}},queryCompany:function(e){this.queryParams.company=e.companyShortName,this.queryParams.companyId=e.companyId,this.handleQuery()}}},v=k,S=(n("d30c"),n("2877")),C=Object(S["a"])(v,o,r,!1,null,"6ed56fd2",null);t["default"]=C.exports},c2aa:function(e,t,n){"use strict";n.d(t,"k",(function(){return r})),n.d(t,"j",(function(){return a})),n.d(t,"l",(function(){return i})),n.d(t,"n",(function(){return l})),n.d(t,"m",(function(){return c})),n.d(t,"e",(function(){return s})),n.d(t,"c",(function(){return u})),n.d(t,"r",(function(){return d})),n.d(t,"s",(function(){return p})),n.d(t,"d",(function(){return m})),n.d(t,"b",(function(){return f})),n.d(t,"g",(function(){return b})),n.d(t,"f",(function(){return h})),n.d(t,"i",(function(){return y})),n.d(t,"p",(function(){return g})),n.d(t,"q",(function(){return w})),n.d(t,"h",(function(){return k})),n.d(t,"o",(function(){return v}));var o=n("b775");function r(e){return Object(o["a"])({url:"/system/rct/list",method:"get",params:e})}function a(e){return Object(o["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function i(e){return Object(o["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function l(e){return Object(o["a"])({url:"/system/rct/op",method:"get",params:e})}function c(e){return Object(o["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/system/rct/"+e,method:"get"})}function u(e){return Object(o["a"])({url:"/system/rct",method:"post",data:e})}function d(e){return Object(o["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function p(e){return Object(o["a"])({url:"/system/rct",method:"put",data:e})}function m(e){return Object(o["a"])({url:"/system/rct/"+e,method:"delete"})}function f(e){return Object(o["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function b(){return Object(o["a"])({url:"/system/rct/mon",method:"get"})}function h(){return Object(o["a"])({url:"/system/rct/CFmon",method:"get"})}function y(){return Object(o["a"])({url:"/system/rct/RSWHMon",method:"get"})}function g(e){return Object(o["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function w(e){return Object(o["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function k(e){return Object(o["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function v(e){return Object(o["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},d30c:function(e,t,n){"use strict";n("f20e")},f20e:function(e,t,n){}}]);