{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue?vue&type=style&index=0&id=5e02ada0&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue", "mtime": 1752832104318}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCmlucHV0OmZvY3VzIHsNCiAgb3V0bGluZTogbm9uZTsNCn0NCg0KLnVuSGlnaGxpZ2h0LXRleHQgew0KICBjb2xvcjogI2I3YmJjMjsNCiAgbWFyZ2luOiAwOw0KfQ0K"}, {"version": 3, "sources": ["chargeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+2BA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "chargeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table ref=\"chargeTable\" :data=\"chargeData\" :row-class-name=\"rowIndex\" border class=\"pd0\"\r\n                @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column v-if=\"isReceivable\" label=\"应收明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"11\">\r\n                <el-row>\r\n                  <el-col :span=\"4\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"11\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableTaxUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableTaxRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"客户\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showClient\" style=\"width: 50px;height: 20px;\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showClient = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <tree-select v-if=\"(companyList&&companyList.length>0)&&scope.row.showClient\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :multiple=\"false\" :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                           :custom-options=\"companyList\" :flat=\"false\"\r\n                           :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                           @close=\" showClientName==scope.row.companyName ? scope.row.showClient = false:null\"\r\n                           @returnData=\"showClientName=(($event.companyShortName&&$event.companyShortName!=='')?$event.companyShortName:$event.companyEnShortName)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate)\r\n                }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                               :min=\"0.0001\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                               @blur=\"scope.row.showUnitRate=false\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               @focusout.native=\"scope.row.showUnitRate=false\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationUnit\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                               :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\"\r\n                     :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none;width: 100%;height: 100%;\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"所属服务\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <!--{{ getServiceName(scope.row.sqd_service_type_id) }}-->\r\n                {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n              </div>\r\n              <!-- <el-select v-else v-model=\"scope.row.sqdServiceTypeId\" filterable placeholder=\"所属服务\">\r\n                 <el-option\r\n                   v-for=\"item in services\"\r\n                   :key=\"item.value\"\r\n                   :label=\"item.label\"\r\n                   :value=\"item.value\"\r\n                 >\r\n                 </el-option>\r\n               </el-select>-->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <!--应付-->\r\n        <el-table-column v-else label=\"应付明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"4\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">{{ currency(payTotalUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :span=\"12\">{{ currency(payTotalRMB, {separator: \",\", symbol: \"￥\"}).format() }}</el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row>\r\n                  <el-col :span=\"5\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{ currency(payDetailUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMB, {separator: \",\", symbol: \"￥\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(payDetailUSDTax, {separator: \",\", symbol: \"$\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMBTax, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column v-if=\"!hiddenSupplier\" align=\"center\" label=\"供应商\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showSupplier\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showSupplier = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <company-select v-if=\"(companyList && companyList.length>0)&&scope.row.showSupplier\"\r\n                              :class=\"disabled || scope.row.isAccountConfirmed == '1'?'disable-form':''\"\r\n                              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :load-options=\"companyList\"\r\n                              :multiple=\"false\"\r\n                              :no-parent=\"true\"\r\n                              :pass=\"scope.row.clearingCompanyId\"\r\n                              :placeholder=\"'供应商'\"\r\n                              @return=\"scope.row.clearingCompanyId=$event\"\r\n                              @returnData=\"$event.companyShortName==scope.row.companyName?scope.row.showSupplier = false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCostCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"$event.chargeLocalName == scope.row.chargeName ? scope.row.showCostCharge=false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate\r\n                }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @blur=\"scope.row.showUnitRate=false\"\r\n                               :precision=\"4\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"costUnitId\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostUnit\"\r\n                   @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showCostUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showCostUnit\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnitCost(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"小计\" prop=\"costTotal\" width=\"65\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已付金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未付余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"生成应收\" prop=\"costTotal\" width=\"65\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyAllFreight()\"\r\n              >生成应收\r\n              </el-button>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyFreight(scope.row)\"\r\n              >复制到应收\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n              style=\"color: red\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button style=\"padding: 0\" type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n               :disabled=\"disabled\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\n\r\nexport default {\r\n  name: \"charges\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\",\"debitNote\"],\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => this.$refs.chargeTable.toggleRowSelection(item, true)) : null\r\n\r\n  },\r\n  computed: {\r\n\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({ row, rowIndex }) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n</style>\r\n"]}]}