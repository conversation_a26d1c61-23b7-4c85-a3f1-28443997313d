{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteExample.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteExample.vue", "mtime": 1752832104319}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9kZWJpdE5vZGVMaXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2RlYml0Tm9kZUxpc3QudnVlIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogJ0RlYml0Tm90ZUV4YW1wbGUnLAogIGNvbXBvbmVudHM6IHsKICAgIERlYml0Tm90ZUxpc3Q6IF9kZWJpdE5vZGVMaXN0LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjb21wYW55TGlzdDogW10sCiAgICAgIC8vIOWFrOWPuOWIl+ihqAogICAgICBBVEQ6IG51bGwsCiAgICAgIC8vIOWunumZheWIsOi+vuaXpeacnwogICAgICBzZXJ2aWNlVHlwZUlkOiAxLAogICAgICAvLyDmnI3liqHnsbvlnotJRAogICAgICBzZXJ2aWNlSWQ6IDEyMywKICAgICAgLy8g5pyN5YqhSUQKICAgICAgcmN0SWQ6IDQ1NiAvLyDmk43kvZzljZVJRAogICAgfTsKICB9LAoKICBtZXRob2RzOiB7CiAgICAvLyDnvJbovpHliIbotKbljZUKICAgIGhhbmRsZUVkaXREZWJpdE5vdGU6IGZ1bmN0aW9uIGhhbmRsZUVkaXREZWJpdE5vdGUoZGViaXROb3RlKSB7CiAgICAgIGNvbnNvbGUubG9nKCfnvJbovpHliIbotKbljZU6JywgZGViaXROb3RlKTsKICAgICAgLy8g6L+Z6YeM5Y+v5Lul5omT5byA57yW6L6R5a+56K+d5qGG5oiW6Lez6L2s5Yiw57yW6L6R6aG16Z2iCiAgICB9LAogICAgLy8g5aSN5Yi26LS555SoCiAgICBoYW5kbGVDb3B5RnJlaWdodDogZnVuY3Rpb24gaGFuZGxlQ29weUZyZWlnaHQoY2hhcmdlKSB7CiAgICAgIGNvbnNvbGUubG9nKCflpI3liLbotLnnlKg6JywgY2hhcmdlKTsKICAgIH0sCiAgICAvLyDliKDpmaTotLnnlKjpobkKICAgIGhhbmRsZURlbGV0ZUl0ZW06IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZUl0ZW0oY2hhcmdlKSB7CiAgICAgIGNvbnNvbGUubG9nKCfliKDpmaTotLnnlKjpobk6JywgY2hhcmdlKTsKICAgIH0sCiAgICAvLyDliKDpmaTmiYDmnInotLnnlKgKICAgIGhhbmRsZURlbGV0ZUFsbDogZnVuY3Rpb24gaGFuZGxlRGVsZXRlQWxsKCkgewogICAgICBjb25zb2xlLmxvZygn5Yig6Zmk5omA5pyJ6LS555SoJyk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["_debitNodeList", "_interopRequireDefault", "require", "name", "components", "DebitNoteList", "data", "companyList", "ATD", "serviceTypeId", "serviceId", "rctId", "methods", "handleEditDebitNote", "debitNote", "console", "log", "handleCopyFreight", "charge", "handleDeleteItem", "handleDeleteAll", "exports", "default", "_default"], "sources": ["src/views/system/document/debitNoteExample.vue"], "sourcesContent": ["<!--\r\n使用示例：在父组件中使用 debitNoteList 组件\r\n\r\n使用方式：\r\n1. 引入组件\r\n2. 传入必要的属性\r\n3. 监听组件事件\r\n\r\n示例代码：\r\n-->\r\n<template>\r\n  <div>\r\n    <debit-note-list\r\n      :atd=\"ATD\"\r\n      :company-list=\"companyList\"\r\n      :disabled=\"false\"\r\n      :hidden-supplier=\"false\"\r\n      :rct-id=\"rctId\"\r\n      :service-id=\"serviceId\"\r\n      :service-type-id=\"serviceTypeId\"\r\n      @copyFreight=\"handleCopyFreight\"\r\n      @deleteAll=\"handleDeleteAll\"\r\n      @deleteItem=\"handleDeleteItem\"\r\n      @editDebitNote=\"handleEditDebitNote\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DebitNoteList from './debitNodeList.vue'\r\n\r\nexport default {\r\n  name: 'DebitNoteExample',\r\n  components: {\r\n    DebitNoteList\r\n  },\r\n  data() {\r\n    return {\r\n      companyList: [], // 公司列表\r\n      ATD: null, // 实际到达日期\r\n      serviceTypeId: 1, // 服务类型ID\r\n      serviceId: 123, // 服务ID\r\n      rctId: 456 // 操作单ID\r\n    }\r\n  },\r\n  methods: {\r\n    // 编辑分账单\r\n    handleEditDebitNote(debitNote) {\r\n      console.log('编辑分账单:', debitNote)\r\n      // 这里可以打开编辑对话框或跳转到编辑页面\r\n    },\r\n\r\n    // 复制费用\r\n    handleCopyFreight(charge) {\r\n      console.log('复制费用:', charge)\r\n    },\r\n\r\n    // 删除费用项\r\n    handleDeleteItem(charge) {\r\n      console.log('删除费用项:', charge)\r\n    },\r\n\r\n    // 删除所有费用\r\n    handleDeleteAll() {\r\n      console.log('删除所有费用')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<!--\r\n组件特性：\r\n\r\n1. 可展开行显示费用明细\r\n   - 点击展开箭头可以查看每个分账单的详细费用信息\r\n   - 费用明细使用原有的 charges 组件显示\r\n\r\n2. 分账单管理\r\n   - 如果没有创建分账单，显示\"创建分账单\"按钮\r\n   - 如果已创建分账单，显示\"编辑\"和\"删除\"按钮\r\n   - 支持分账单的状态显示（草稿、已确认、已关闭等）\r\n\r\n3. 费用管理\r\n   - 在展开区域中可以管理费用明细\r\n   - 支持添加、编辑、删除费用项\r\n   - 费用数据会实时更新分账单的应收应付金额\r\n\r\n4. 状态标识\r\n   - 账单状态：草稿、已确认、已关闭\r\n   - 发票状态：未开票、已开票、已作废\r\n   - 销账状态：未销账、部分销账、已销账\r\n\r\n5. 响应式设计\r\n   - 适配不同屏幕尺寸\r\n   - 合理的列宽和布局\r\n-->\r\n"], "mappings": ";;;;;;;AA6BA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,GAAA;MAAA;MACAC,aAAA;MAAA;MACAC,SAAA;MAAA;MACAC,KAAA;IACA;EACA;;EACAC,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,SAAA;MACAC,OAAA,CAAAC,GAAA,WAAAF,SAAA;MACA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAAC,MAAA;MACAH,OAAA,CAAAC,GAAA,UAAAE,MAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAD,MAAA;MACAH,OAAA,CAAAC,GAAA,WAAAE,MAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACAL,OAAA,CAAAC,GAAA;IACA;EACA;AACA;AAAAK,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}