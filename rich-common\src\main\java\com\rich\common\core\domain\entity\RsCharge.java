package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 费用明细对象 rs_charge
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
public class RsCharge extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long chargeId;

    /**
     * 所属服务实例id ,
     */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /**
     * 所属服务类型id ,
     */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;
    private Long debitNoteId;

    /**
     * 所属操作单号 ,
     */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 关联收费条目 ,与此条收入相关的成本，针对报价
     */
    @Excel(name = "关联收费条目 ,与此条收入相关的成本，针对报价")
    private Long relatedFreightId;

    /**
     * 应收应付标志 ,应收应付标志
     */
    @Excel(name = "应收应付标志 ,应收应付标志")
    private Long isRecievingOrPaying;

    /**
     * 结算公司 ,(应收/应付)
     */
    @Excel(name = "结算公司 ,(应收/应付)")
    private Long clearingCompanyId;

    /**
     * 结算公司概要 ,(应收/应付)
     */
    @Excel(name = "结算公司概要 ,(应收/应付)")
    private String clearingCompanySummary;

    /**
     * 报价策略 ,
     */
    @Excel(name = "报价策略 ,")
    private String quotationStrategyId;

    /**
     * 费用名称ID ,dn=debit note账单
     */
    @Excel(name = "费用名称ID ,dn=debit note账单")
    private Long dnChargeNameId;

    /**
     * 账单币种 ,
     */
    @Excel(name = "账单币种 ,")
    private String dnCurrencyCode;

    /**
     * 计费单价 ,
     */
    @Excel(name = "计费单价 ,")
    private BigDecimal dnUnitRate;

    /**
     * 计费单位 ,
     */
    @Excel(name = "计费单位 ,")
    private String dnUnitCode;

    /**
     * 计费数量 ,
     */
    @Excel(name = "计费数量 ,")
    private BigDecimal dnAmount;

    /**
     * 本位币汇率 ,
     */
    @Excel(name = "本位币汇率 ,")
    private BigDecimal basicCurrencyRate;

    /**
     * 税率 ,
     */
    @Excel(name = "税率 ,")
    private BigDecimal dutyRate;

    /**
     * 小计 ,
     */
    @Excel(name = "小计 ,")
    private BigDecimal subtotal;

    /**
     * 费用备注 ,
     */
    @Excel(name = "费用备注 ,")
    private String chargeRemark;

    /**
     * 业务确认 ,按条目确认
     */
    @Excel(name = "业务确认 ,按条目确认")
    private String isDnSalesConfirmed;

    /**
     * 客户确认 ,按条目确认
     */
    @Excel(name = "客户确认 ,按条目确认")
    private String isDnClientConfirmed;

    /**
     * 操作确认 ,按条目确认
     */
    @Excel(name = "操作确认 ,按条目确认")
    private String isDnOpConfirmed;

    /**
     * 商务确认 ,
     */
    @Excel(name = "商务确认 ,")
    private String isDnPsaConfirmed;

    /**
     * 供应商确认 ,
     */
    @Excel(name = "供应商确认 ,")
    private String isDnSupplierConfirmed;

    /**
     * 财务审核 ,
     */
    @Excel(name = "财务审核 ,")
    private String isAccountConfirmed;

    /**
     * 财务审核人 ,
     */
    @Excel(name = "财务审核人 ,")
    private Long confirmAccountId;

    /**
     * 财务审核时间 ,
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务审核时间 ,", width = 30, dateFormat = "yyyy-MM-dd")
    private Date accountConfirmTime;

    /**
     * 结算币种ID ,
     */
    @Excel(name = "结算币种ID ,")
    private String clearingCurrencyCode;

    /**
     * 折合账单币种已收 ,
     */
    @Excel(name = "折合账单币种已收 ,")
    private BigDecimal dnCurrencyReceived;

    /**
     * 折合账单币种已付 ,
     */
    @Excel(name = "折合账单币种已付 ,")
    private BigDecimal dnCurrencyPaid;

    /**
     * 本条目账单币种余额 ,
     */
    @Excel(name = "本条目账单币种余额 ,")
    private BigDecimal dnCurrencyBalance;

    /**
     * 财务已收销账流水号List ,
     */
    @Excel(name = "财务已收销账流水号List ,")
    private String accountReceivedIdList;

    /**
     * 财务已付销账流水号List ,
     */
    @Excel(name = "财务已付销账流水号List ,")
    private String accountPaidIdList;

    /**
     * 发票查询编号 ,发票查询编号（list）
     */
    @Excel(name = "发票查询编号 ,发票查询编号", readConverterExp = "l=ist")
    private String logisticsInvoiceIdList;

    private String companyName;

    private String chargeName;

    private boolean showClient;
    private boolean showSupplier;
    private boolean showQuotationCharge;
    private boolean showCostCharge;
    private boolean showQuotationCurrency;
    private boolean showCostCurrency;
    private boolean showQuotationUnit;
    private boolean showCostUnit;
    private boolean showStrategy;
    private boolean showUnitRate;
    private boolean showAmount;
    private boolean showCurrencyRate;
    private boolean showDutyRate;
    private String serviceName;
    private Long sqdRctId;
    private String writeoffStatusString;
    private Long salesId;
    private Date[] ATDDate;
    private Date[] ATADate;
    private String orderBelongsTo;
    private BigDecimal receivableUsd;
    private BigDecimal receivableRmb;
    private BigDecimal uncollectedUsd;
    private BigDecimal uncollectedRmb;
    private BigDecimal payableUsd;
    private BigDecimal payableRmb;
    private BigDecimal unpaidUsd;
    private BigDecimal unpaidRmb;
    private String clientSummary;
    private Long clientId;

    public Long getDebitNoteId() {
        return debitNoteId;
    }

    public void setDebitNoteId(Long debitNoteId) {
        this.debitNoteId = debitNoteId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientSummary() {
        return clientSummary;
    }

    public void setClientSummary(String clientSummary) {
        this.clientSummary = clientSummary;
    }

    public BigDecimal getUnpaidRmb() {
        return unpaidRmb;
    }

    public void setUnpaidRmb(BigDecimal unpaidRmb) {
        this.unpaidRmb = unpaidRmb;
    }

    public BigDecimal getUnpaidUsd() {
        return unpaidUsd;
    }

    public void setUnpaidUsd(BigDecimal unpaidUsd) {
        this.unpaidUsd = unpaidUsd;
    }

    public BigDecimal getPayableRmb() {
        return payableRmb;
    }

    public void setPayableRmb(BigDecimal payableRmb) {
        this.payableRmb = payableRmb;
    }

    public BigDecimal getPayableUsd() {
        return payableUsd;
    }

    public void setPayableUsd(BigDecimal payableUsd) {
        this.payableUsd = payableUsd;
    }

    public BigDecimal getUncollectedRmb() {
        return uncollectedRmb;
    }

    public void setUncollectedRmb(BigDecimal uncollectedRmb) {
        this.uncollectedRmb = uncollectedRmb;
    }

    public BigDecimal getUncollectedUsd() {
        return uncollectedUsd;
    }

    public void setUncollectedUsd(BigDecimal uncollectedUsd) {
        this.uncollectedUsd = uncollectedUsd;
    }

    public BigDecimal getReceivableRmb() {
        return receivableRmb;
    }

    public void setReceivableRmb(BigDecimal receivableRmb) {
        this.receivableRmb = receivableRmb;
    }

    public BigDecimal getReceivableUsd() {
        return receivableUsd;
    }

    public void setReceivableUsd(BigDecimal receivableUsd) {
        this.receivableUsd = receivableUsd;
    }

    public Date[] getATDDate() {
        return ATDDate;
    }

    public void setATDDate(Date[] ATDDate) {
        this.ATDDate = ATDDate;
    }

    public Date[] getATADate() {
        return ATADate;
    }

    public void setATADate(Date[] ATADate) {
        this.ATADate = ATADate;
    }

    public String getOrderBelongsTo() {
        return orderBelongsTo;
    }

    public void setOrderBelongsTo(String orderBelongsTo) {
        this.orderBelongsTo = orderBelongsTo;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public String getWriteoffStatusString() {
        return writeoffStatusString;
    }

    public void setWriteoffStatusString(String writeoffStatusString) {
        this.writeoffStatusString = writeoffStatusString;
    }

    /**
     * 已开票金额
     */
    @Excel(name = "已开票金额")
    private BigDecimal sqdInvoiceIssued;

    /**
     * 未开票余额
     */
    @Excel(name = "未开票余额 ")
    private BigDecimal sqdInvoiceBalance;

    /**
     * 财务审核标记
     */
    @Excel(name = "财务审核标记")
    private String sqdIsAccountConfirmed;

    /**
     * 已销账金额
     */
    @Excel(name = "已销账金额")
    private BigDecimal sqdDnCurrencyPaid;

    /**
     * 未销账余额
     */
    @Excel(name = "未销账余额")
    private BigDecimal sqdDnCurrencyBalance;

    /**
     * 销账流水号List
     */
    @Excel(name = "销账流水号List")
    private String sqdWriteoffNoList;
    private Long staffId;
    private Date currencyRateCalculateDate;

    private List<RsCharge> rsChargeList;

    private List<MidChargeBankWriteoff> midChargeBankWriteoffList;
    private Long bankRecordId;

    private MidChargeBankWriteoff midChargeBankWriteoff;
    private String writeoffStatus;
    private String paymentTitleCode;
    private String sqdRaletiveRctList;
    private String sqdServiceDetailsCode;
    private String logisticsPaymentTermsCode;
    private Long chargeTypeId;

    private String pol;
    private String destinationPort;
    private String blNo;
    private String sqdContainersSealsSum;
    private String revenueTon;
    private String eta;
    private String etd;

    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        this.etd = etd;
    }

    public String getEta() {
        return eta;
    }

    public void setEta(String eta) {
        this.eta = eta;
    }

    public String getRevenueTon() {
        return revenueTon;
    }

    public void setRevenueTon(String revenueTon) {
        this.revenueTon = revenueTon;
    }

    public String getSqdContainersSealsSum() {
        return sqdContainersSealsSum;
    }

    public void setSqdContainersSealsSum(String sqdContainersSealsSum) {
        this.sqdContainersSealsSum = sqdContainersSealsSum;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getDestinationPort() {
        return destinationPort;
    }

    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }

    public String getPol() {
        return pol;
    }

    public void setPol(String pol) {
        this.pol = pol;
    }

    public Long getChargeTypeId() {
        return chargeTypeId;
    }

    public void setChargeTypeId(Long chargeTypeId) {
        this.chargeTypeId = chargeTypeId;
    }

    public String getLogisticsPaymentTermsCode() {
        return logisticsPaymentTermsCode;
    }

    public void setLogisticsPaymentTermsCode(String logisticsPaymentTermsCode) {
        this.logisticsPaymentTermsCode = logisticsPaymentTermsCode;
    }

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public String getSqdRaletiveRctList() {
        return sqdRaletiveRctList;
    }

    public void setSqdRaletiveRctList(String sqdRaletiveRctList) {
        this.sqdRaletiveRctList = sqdRaletiveRctList;
    }

    public String getPaymentTitleCode() {
        return paymentTitleCode;
    }

    public void setPaymentTitleCode(String paymentTitleCode) {
        this.paymentTitleCode = paymentTitleCode;
    }

    public String getWriteoffStatus() {
        return writeoffStatus;
    }

    public void setWriteoffStatus(String writeoffStatus) {
        this.writeoffStatus = writeoffStatus;
    }

    public MidChargeBankWriteoff getMidChargeBankWriteoff() {
        return midChargeBankWriteoff;
    }

    public void setMidChargeBankWriteoff(MidChargeBankWriteoff midChargeBankWriteoff) {
        this.midChargeBankWriteoff = midChargeBankWriteoff;
    }

    public Long getBankRecordId() {
        return bankRecordId;
    }

    public void setBankRecordId(Long bankRecordId) {
        this.bankRecordId = bankRecordId;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<MidChargeBankWriteoff> getMidChargeBankWriteoffList() {
        return midChargeBankWriteoffList;
    }

    public void setMidChargeBankWriteoffList(List<MidChargeBankWriteoff> midChargeBankWriteoffList) {
        this.midChargeBankWriteoffList = midChargeBankWriteoffList;
    }

    public Date getCurrencyRateCalculateDate() {
        return currencyRateCalculateDate;
    }

    public void setCurrencyRateCalculateDate(Date currencyRateCalculateDate) {
        this.currencyRateCalculateDate = currencyRateCalculateDate;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public BigDecimal getSqdInvoiceIssued() {
        return sqdInvoiceIssued;
    }

    public void setSqdInvoiceIssued(BigDecimal sqdInvoiceIssued) {
        this.sqdInvoiceIssued = sqdInvoiceIssued;
    }

    public BigDecimal getSqdInvoiceBalance() {
        return sqdInvoiceBalance;
    }

    public void setSqdInvoiceBalance(BigDecimal sqdInvoiceBalance) {
        this.sqdInvoiceBalance = sqdInvoiceBalance;
    }

    public String getSqdIsAccountConfirmed() {
        return sqdIsAccountConfirmed;
    }

    public void setSqdIsAccountConfirmed(String sqdIsAccountConfirmed) {
        this.sqdIsAccountConfirmed = sqdIsAccountConfirmed;
    }

    public BigDecimal getSqdDnCurrencyPaid() {
        return sqdDnCurrencyPaid;
    }

    public void setSqdDnCurrencyPaid(BigDecimal sqdDnCurrencyPaid) {
        this.sqdDnCurrencyPaid = sqdDnCurrencyPaid;
    }

    public BigDecimal getSqdDnCurrencyBalance() {
        return sqdDnCurrencyBalance;
    }

    public void setSqdDnCurrencyBalance(BigDecimal sqdDnCurrencyBalance) {
        this.sqdDnCurrencyBalance = sqdDnCurrencyBalance;
    }

    public String getSqdWriteoffNoList() {
        return sqdWriteoffNoList;
    }

    public void setSqdWriteoffNoList(String sqdWriteoffNoList) {
        this.sqdWriteoffNoList = sqdWriteoffNoList;
    }

    public Long getSqdRctId() {
        return sqdRctId;
    }

    public void setSqdRctId(Long sqdRctId) {
        this.sqdRctId = sqdRctId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public boolean isShowStrategy() {
        return showStrategy;
    }

    public void setShowStrategy(boolean showStrategy) {
        this.showStrategy = showStrategy;
    }

    public boolean isShowUnitRate() {
        return showUnitRate;
    }

    public void setShowUnitRate(boolean showUnitRate) {
        this.showUnitRate = showUnitRate;
    }

    public boolean isShowAmount() {
        return showAmount;
    }

    public void setShowAmount(boolean showAmount) {
        this.showAmount = showAmount;
    }

    public boolean isShowCurrencyRate() {
        return showCurrencyRate;
    }

    public void setShowCurrencyRate(boolean showCurrencyRate) {
        this.showCurrencyRate = showCurrencyRate;
    }

    public boolean isShowDutyRate() {
        return showDutyRate;
    }

    public void setShowDutyRate(boolean showDutyRate) {
        this.showDutyRate = showDutyRate;
    }

    public boolean isShowClient() {
        return showClient;
    }

    public void setShowClient(boolean showClient) {
        this.showClient = showClient;
    }

    public boolean isShowSupplier() {
        return showSupplier;
    }

    public void setShowSupplier(boolean showSupplier) {
        this.showSupplier = showSupplier;
    }

    public boolean isShowQuotationCharge() {
        return showQuotationCharge;
    }

    public void setShowQuotationCharge(boolean showQuotationCharge) {
        this.showQuotationCharge = showQuotationCharge;
    }

    public boolean isShowCostCharge() {
        return showCostCharge;
    }

    public void setShowCostCharge(boolean showCostCharge) {
        this.showCostCharge = showCostCharge;
    }

    public boolean isShowQuotationCurrency() {
        return showQuotationCurrency;
    }

    public void setShowQuotationCurrency(boolean showQuotationCurrency) {
        this.showQuotationCurrency = showQuotationCurrency;
    }

    public boolean isShowCostCurrency() {
        return showCostCurrency;
    }

    public void setShowCostCurrency(boolean showCostCurrency) {
        this.showCostCurrency = showCostCurrency;
    }

    public boolean isShowQuotationUnit() {
        return showQuotationUnit;
    }

    public void setShowQuotationUnit(boolean showQuotationUnit) {
        this.showQuotationUnit = showQuotationUnit;
    }

    public boolean isShowCostUnit() {
        return showCostUnit;
    }

    public void setShowCostUnit(boolean showCostUnit) {
        this.showCostUnit = showCostUnit;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getChargeName() {
        return chargeName;
    }

    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public Long getRelatedFreightId() {
        return relatedFreightId;
    }

    public void setRelatedFreightId(Long relatedFreightId) {
        this.relatedFreightId = relatedFreightId;
    }

    public Long getIsRecievingOrPaying() {
        return isRecievingOrPaying;
    }

    public void setIsRecievingOrPaying(Long isRecievingOrPaying) {
        this.isRecievingOrPaying = isRecievingOrPaying;
    }

    public Long getClearingCompanyId() {
        return clearingCompanyId;
    }

    public void setClearingCompanyId(Long clearingCompanyId) {
        this.clearingCompanyId = clearingCompanyId;
    }

    public String getClearingCompanySummary() {
        return clearingCompanySummary;
    }

    public void setClearingCompanySummary(String clearingCompanySummary) {
        this.clearingCompanySummary = clearingCompanySummary;
    }

    public String getQuotationStrategyId() {
        return quotationStrategyId;
    }

    public void setQuotationStrategyId(String quotationStrategyId) {
        this.quotationStrategyId = quotationStrategyId;
    }

    public Long getDnChargeNameId() {
        return dnChargeNameId;
    }

    public void setDnChargeNameId(Long dnChargeNameId) {
        this.dnChargeNameId = dnChargeNameId;
    }

    public String getDnCurrencyCode() {
        return dnCurrencyCode;
    }

    public void setDnCurrencyCode(String dnCurrencyCode) {
        this.dnCurrencyCode = dnCurrencyCode;
    }

    public BigDecimal getDnUnitRate() {
        return dnUnitRate;
    }

    public void setDnUnitRate(BigDecimal dnUnitRate) {
        this.dnUnitRate = dnUnitRate;
    }

    public String getDnUnitCode() {
        return dnUnitCode;
    }

    public void setDnUnitCode(String dnUnitCode) {
        this.dnUnitCode = dnUnitCode;
    }

    public BigDecimal getDnAmount() {
        return dnAmount;
    }

    public void setDnAmount(BigDecimal dnAmount) {
        this.dnAmount = dnAmount;
    }

    public BigDecimal getBasicCurrencyRate() {
        return basicCurrencyRate;
    }

    public void setBasicCurrencyRate(BigDecimal basicCurrencyRate) {
        this.basicCurrencyRate = basicCurrencyRate;
    }

    public BigDecimal getDutyRate() {
        return dutyRate;
    }

    public void setDutyRate(BigDecimal dutyRate) {
        this.dutyRate = dutyRate;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public String getChargeRemark() {
        return chargeRemark;
    }

    public void setChargeRemark(String chargeRemark) {
        this.chargeRemark = chargeRemark;
    }

    public String getIsDnSalesConfirmed() {
        return isDnSalesConfirmed;
    }

    public void setIsDnSalesConfirmed(String isDnSalesConfirmed) {
        this.isDnSalesConfirmed = isDnSalesConfirmed;
    }

    public String getIsDnClientConfirmed() {
        return isDnClientConfirmed;
    }

    public void setIsDnClientConfirmed(String isDnClientConfirmed) {
        this.isDnClientConfirmed = isDnClientConfirmed;
    }

    public String getIsDnOpConfirmed() {
        return isDnOpConfirmed;
    }

    public void setIsDnOpConfirmed(String isDnOpConfirmed) {
        this.isDnOpConfirmed = isDnOpConfirmed;
    }

    public String getIsDnPsaConfirmed() {
        return isDnPsaConfirmed;
    }

    public void setIsDnPsaConfirmed(String isDnPsaConfirmed) {
        this.isDnPsaConfirmed = isDnPsaConfirmed;
    }

    public String getIsDnSupplierConfirmed() {
        return isDnSupplierConfirmed;
    }

    public void setIsDnSupplierConfirmed(String isDnSupplierConfirmed) {
        this.isDnSupplierConfirmed = isDnSupplierConfirmed;
    }

    public String getIsAccountConfirmed() {
        return isAccountConfirmed;
    }

    public void setIsAccountConfirmed(String isAccountConfirmed) {
        this.isAccountConfirmed = isAccountConfirmed;
    }

    public Long getConfirmAccountId() {
        return confirmAccountId;
    }

    public void setConfirmAccountId(Long confirmAccountId) {
        this.confirmAccountId = confirmAccountId;
    }

    public Date getAccountConfirmTime() {
        return accountConfirmTime;
    }

    public void setAccountConfirmTime(Date accountConfirmTime) {
        this.accountConfirmTime = accountConfirmTime;
    }

    public String getClearingCurrencyCode() {
        return clearingCurrencyCode;
    }

    public void setClearingCurrencyCode(String clearingCurrencyCode) {
        this.clearingCurrencyCode = clearingCurrencyCode;
    }

    public BigDecimal getDnCurrencyReceived() {
        return dnCurrencyReceived;
    }

    public void setDnCurrencyReceived(BigDecimal dnCurrencyReceived) {
        this.dnCurrencyReceived = dnCurrencyReceived;
    }

    public BigDecimal getDnCurrencyPaid() {
        return dnCurrencyPaid;
    }

    public void setDnCurrencyPaid(BigDecimal dnCurrencyPaid) {
        this.dnCurrencyPaid = dnCurrencyPaid;
    }

    public BigDecimal getDnCurrencyBalance() {
        return dnCurrencyBalance;
    }

    public void setDnCurrencyBalance(BigDecimal dnCurrencyBalance) {
        this.dnCurrencyBalance = dnCurrencyBalance;
    }

    public String getAccountReceivedIdList() {
        return accountReceivedIdList;
    }

    public void setAccountReceivedIdList(String accountReceivedIdList) {
        this.accountReceivedIdList = accountReceivedIdList;
    }

    public String getAccountPaidIdList() {
        return accountPaidIdList;
    }

    public void setAccountPaidIdList(String accountPaidIdList) {
        this.accountPaidIdList = accountPaidIdList;
    }

    public String getLogisticsInvoiceIdList() {
        return logisticsInvoiceIdList;
    }

    public void setLogisticsInvoiceIdList(String logisticsInvoiceIdList) {
        this.logisticsInvoiceIdList = logisticsInvoiceIdList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("chargeId", getChargeId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("relatedFreightId", getRelatedFreightId())
                .append("isRecievingOrPaying", getIsRecievingOrPaying())
                .append("clearingCompanyId", getClearingCompanyId())
                .append("clearingCompanySummary", getClearingCompanySummary())
                .append("quotationStrategyId", getQuotationStrategyId())
                .append("dnChargeNameId", getDnChargeNameId())
                .append("dnCurrencyCode", getDnCurrencyCode())
                .append("dnUnitRate", getDnUnitRate())
                .append("dnUnitCode", getDnUnitCode())
                .append("dnAmount", getDnAmount())
                .append("basicCurrencyRate", getBasicCurrencyRate())
                .append("dutyRate", getDutyRate())
                .append("subtotal", getSubtotal())
                .append("chargeRemark", getChargeRemark())
                .append("isDnSalesConfirmed", getIsDnSalesConfirmed())
                .append("isDnClientConfirmed", getIsDnClientConfirmed())
                .append("isDnOpConfirmed", getIsDnOpConfirmed())
                .append("isDnPsaConfirmed", getIsDnPsaConfirmed())
                .append("isDnSupplierConfirmed", getIsDnSupplierConfirmed())
                .append("isAccountConfirmed", getIsAccountConfirmed())
                .append("confirmAccountId", getConfirmAccountId())
                .append("accountConfirmTime", getAccountConfirmTime())
                .append("clearingCurrencyCode", getClearingCurrencyCode())
                .append("dnCurrencyReceived", getDnCurrencyReceived())
                .append("dnCurrencyPaid", getDnCurrencyPaid())
                .append("dnCurrencyBalance", getDnCurrencyBalance())
                .append("accountReceivedIdList", getAccountReceivedIdList())
                .append("accountPaidIdList", getAccountPaidIdList())
                .append("logisticsInvoiceIdList", getLogisticsInvoiceIdList())
                .toString();
    }
}
