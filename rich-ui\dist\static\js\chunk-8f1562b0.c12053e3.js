(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8f1562b0","chunk-5c8dffec"],{"18c9":function(e,t,s){},"33ad":function(e,t,s){"use strict";s("bc52")},38868:function(e,t,s){"use strict";s("18c9")},4678:function(e,t,s){var i={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function a(e){var t=n(e);return s(t)}function n(e){if(!s.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}a.keys=function(){return Object.keys(i)},a.resolve=n,e.exports=a,a.id="4678"},"50fe":function(e,t,s){"use strict";s.d(t,"a",(function(){return a}));var i=s("b775");function a(e){return Object(i["a"])({url:"/system/serviceinstances",method:"put",data:e})}},"788f":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-col",{style:{display:e.audit?"":"none"},attrs:{span:15}},[s("div",{class:{inactive:0==e.audit,active:e.audit}},[s("el-col",{staticStyle:{display:"flex","border-radius":"5px"}},[s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:opapproval","system:rct:opapproval"],expression:"['system:booking:opapproval','system:rct:opapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:opapproval"]),icon:e.basicInfo.isDnOpConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("op")}}},[e._v("操作确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.opConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.opConfirmedDate))])])],1),s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:psaapproval","system:rct:psaapproval"],expression:"['system:booking:psaapproval','system:rct:psaapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:psaapproval"]),icon:e.basicInfo.isDnPsaConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("psa")}}},[e._v("商务确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.psaConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.psaConfirmedDate))])])],1),s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:supplierapproval","system:rct:supplierapproval"],expression:"['system:booking:supplierapproval','system:rct:supplierapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:supplierapproval"]),icon:e.basicInfo.isDnSupplierConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("supplier")}}},[e._v("供应商确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.supplierConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.supplierConfirmedDate))])])],1),e.checkPermi(["system:booking:financeapproval","system:rct:financeapproval"])?s("div",{staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:financeapproval"]),icon:e.basicInfo.isAccountConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("account")}}},[e._v("财务确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.accountConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.accountConfirmedDate))])])],1):e._e()])],1)])},a=[],n=(s("4de4"),s("d3b7"),s("d81d"),s("fba1")),r=s("e350"),o=s("72f9"),c=s.n(o),l=s("fff5"),f=s("50fe"),d={name:"audit",props:["audit","basicInfo","audits","payable","disabled","rsChargeList"],data:function(){var e=this;return{opConfirmedName:this.basicInfo&&this.basicInfo.isDnOpConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName:null,opConfirmedDate:this.basicInfo&&this.basicInfo.opConfirmedTime?this.basicInfo.opConfirmedTime:null,accountConfirmedName:this.basicInfo&&this.basicInfo.isAccountConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName:null,accountConfirmedDate:this.basicInfo&&this.basicInfo.accountConfirmTime?this.basicInfo.accountConfirmTime:null,supplierConfirmedName:this.basicInfo&&this.basicInfo.isDnSupplierConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName:null,supplierConfirmedDate:this.basicInfo&&this.basicInfo.supplierConfirmedTime?this.basicInfo.supplierConfirmedTime:null,psaConfirmedName:this.basicInfo&&this.basicInfo.isDnPsaConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName:null,psaConfirmedDate:this.basicInfo&&this.basicInfo.psaConfirmedTime?this.basicInfo.psaConfirmedTime:null,salesConfirmedName:null,salesConfirmedDate:null}},watch:{basicInfo:function(e){this.$emit("return",e)}},methods:{currency:c.a,checkPermi:r["a"],confirmed:function(e){var t=this;"op"==e&&(this.basicInfo.isDnOpConfirmed?(this.basicInfo.isDnOpConfirmed=null,this.basicInfo.opConfirmedTime=null,this.opConfirmedName=null,this.opConfirmedDate=null):(this.basicInfo.isDnOpConfirmed=this.$store.state.user.sid,this.basicInfo.opConfirmedTime=Object(n["f"])(new Date,"{y}-{m}-{d}"),this.opConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName,this.opConfirmedDate=this.basicInfo.opConfirmedTime),this.updateServiceInstance(this.basicInfo)),"account"==e&&(this.basicInfo.isAccountConfirmed?(this.basicInfo.isAccountConfirmed=null,this.basicInfo.accountConfirmTime=null,this.accountConfirmedName=null,this.accountConfirmedDate=null):(this.basicInfo.isAccountConfirmed=this.$store.state.user.sid,this.basicInfo.accountConfirmTime=Object(n["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.accountConfirmTime,this.$emit("auditFee",this.rsChargeList.map((function(e){return 1!=e.isAccountConfirmed&&(e.isAccountConfirmed=1,Object(l["l"])(e)),e})))),this.updateServiceInstance(this.basicInfo)),"sales"==e&&(this.basicInfo.isDnSalesConfirmed?(this.basicInfo.isDnSalesConfirmed=null,this.basicInfo.salesConfirmedTime=null,this.salesConfirmedName=null,this.salesConfirmedDate=null):(this.basicInfo.isDnSalesConfirmed=this.$store.state.user.sid,this.basicInfo.salesConfirmedTime=Object(n["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.salesConfirmedTime),this.updateServiceInstance(this.basicInfo)),"psa"==e&&(this.basicInfo.isDnPsaConfirmed?(this.basicInfo.isDnPsaConfirmed=null,this.basicInfo.psaConfirmedTime=null,this.psaConfirmedName=null,this.psaConfirmedDate=null):(this.basicInfo.isDnPsaConfirmed=this.$store.state.user.sid,this.basicInfo.psaConfirmedTime=Object(n["f"])(new Date,"{y}-{m}-{d}"),this.psaConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName,this.psaConfirmedDate=this.basicInfo.psaConfirmedTime),this.updateServiceInstance(this.basicInfo)),"supplier"==e&&(this.basicInfo.isDnSupplierConfirmed?(this.basicInfo.isDnSupplierConfirmed=null,this.basicInfo.supplierConfirmedTime=null,this.supplierConfirmedName=null,this.supplierConfirmedDate=null):(this.basicInfo.isDnSupplierConfirmed=this.$store.state.user.sid,this.basicInfo.supplierConfirmedTime=Object(n["f"])(new Date,"{y}-{m}-{d}"),this.supplierConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName,this.supplierConfirmedDate=this.basicInfo.supplierConfirmedTime),this.updateServiceInstance(this.basicInfo))},updateServiceInstance:function(e){Object(f["a"])(e)}}},u=d,m=(s("38868"),s("2877")),p=Object(m["a"])(u,i,a,!1,null,"7e84911e",null);t["default"]=p.exports},b59f:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{attrs:{id:"app-container"}},[s("el-col",{staticStyle:{margin:"0",padding:"0"},style:{display:e.openLogisticsProgressList?"":"none"}},[s("div",{class:{inactive:0==e.openLogisticsProgressList,active:e.openLogisticsProgressList}},[s("el-table",{staticClass:"pd0",attrs:{data:e.logisticsProgressData,"row-class-name":e.rowIndex,border:""}},[s("el-table-column",{attrs:{label:"进度",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showProgress?e._e():s("div",{staticStyle:{width:"50px",height:"20px"},on:{click:function(e){t.row.showProgress=!0}}},[e._v(" "+e._s(t.row.basProcess.processShortName)+" ")]),t.row.showProgress?s("progress-name",{attrs:{disabled:e.disabled,pass:t.row.processId,placeholder:"进度","process-type":e.processType,"service-type":e.serviceType},on:{returnData:function(e){t.row.sqdProcessEnName=e.processEnName},progressName:function(e){t.row.processId=e}}}):e._e()]}}])}),s("el-table-column",{attrs:{align:"center",label:"状态",prop:"quotationChargeId"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showStatus?e._e():s("div",{on:{click:function(e){t.row.showStatus=!0}}},[e._v(" "+e._s(t.row.basProcessStatus.processStatusShortName)+" "+e._s(t.row.processStatusTime)+" ")]),t.row.showStatus?s("div",{staticStyle:{display:"flex"}},[s("progress-status",{staticStyle:{flex:"1"},attrs:{pass:t.row.processStatusId,placeholder:"物流进度"},on:{progressStatus:function(e){t.row.processStatusId=e}}}),t.row.showStatus?s("el-date-picker",{staticStyle:{flex:"2"},attrs:{placeholder:"选择日期时间",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.row.processStatusTime,callback:function(s){e.$set(t.row,"processStatusTime",s)},expression:"scope.row.processStatusTime"}}):e._e()],1):e._e()]}}])}),s("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger",disabled:e.disabled},on:{click:function(s){return e.deleteLogisticsProgress(t.row)}}},[e._v("删除 ")])]}}])})],1)],1),s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",disabled:e.disabled},on:{click:e.addLogisticsProgress}},[e._v("[＋] ")])],1)],1)},a=[],n=(s("14d9"),s("5bc0")),r=s("1dec"),o=s("c1df"),c=s.n(o),l={name:"logisticsProgress",components:{ProgressName:r["a"],ProgressStatus:n["a"]},props:["logisticsProgressData","openLogisticsProgressList","disabled","serviceType","processType"],watch:{logisticsProgressData:function(e){this.$emit("return",e)}},methods:{rowIndex:function(e){var t=e.row,s=e.rowIndex;t.id=s+1},addLogisticsProgress:function(){var e={showProgress:!0,showStatus:!0,processStatusId:7,processStatusTime:c()().format("yyyy-MM-DD HH:mm:ss"),opId:this.$store.state.user.sid};this.logisticsProgressData.push(e)},deleteLogisticsProgress:function(e){this.$emit("deleteItem",e)}}},f=l,d=s("2877"),u=Object(d["a"])(f,i,a,!1,null,"12005fc7",null);t["default"]=u.exports},bc52:function(e,t,s){},c48b:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"whs-component"},e._l(e.whsServices,(function(t,i){return s("div",{key:"whs-"+i,staticClass:"whs-item"},[s("el-row",[s("el-col",{attrs:{span:18}},[s("div",{staticClass:"service-bar"},[s("a",{class:["service-toggle-icon",e.getFold(t.serviceTypeId)?"el-icon-arrow-down":"el-icon-arrow-right"]}),s("h3",{staticClass:"service-title",on:{click:function(s){return e.changeFold(t.serviceTypeId)}}},[e._v(" 仓储-"+e._s(t.serviceShortName)+" ")]),e.auditInfo?s("audit",{attrs:{audit:!0,"basic-info":e.getServiceInstance(t.serviceTypeId),disabled:e.disabled,payable:e.getPayable(t.serviceTypeId),"rs-charge-list":t.rsChargeList},on:{auditFee:function(s){return e.auditCharge(t.serviceTypeId,s)},return:function(s){return e.changeServiceObject(t.serviceTypeId,s)}}}):e._e(),s("div",{staticClass:"outbound-plan-container"},[s("a",{staticClass:"outbound-plan-link",attrs:{target:"_blank"},on:{click:e.outboundPlan}},[e._v(" [出仓计划] ")])])],1)])],1),s("transition",{attrs:{name:"fade"}},[e.getFold(t.serviceTypeId)?s("el-row",{staticClass:"service-content-area",attrs:{gutter:10}},[s("transition",{attrs:{name:"fade"}},[e.branchInfo?s("el-col",{staticClass:"service-info-col",attrs:{span:3}},[s("el-form-item",{attrs:{label:"询价单号"}},[s("el-input",{attrs:{placeholder:"询价单号"},on:{focus:function(s){e.generateFreight(8,t.serviceTypeId,e.getServiceObject(t.serviceTypeId))}},model:{value:e.getServiceInstance(t.serviceTypeId).inquiryNo,callback:function(s){e.$set(e.getServiceInstance(t.serviceTypeId),"inquiryNo",s)},expression:"getServiceInstance(item.serviceTypeId).inquiryNo"}})],1),!e.booking&&e.branchInfo?s("el-form-item",{attrs:{label:"供应商"}},[s("el-popover",{attrs:{content:e.getSupplierEmail(),placement:"bottom",trigger:"hover",width:"200"},scopedSlots:e._u([{key:"reference",fn:function(){return[s("el-input",{staticClass:"disable-form",attrs:{value:e.getServiceInstance().supplierName,disabled:""}})]},proxy:!0}],null,!0)})],1):e._e(),s("el-form-item",{attrs:{label:"合约类型"}},[s("el-input",{staticClass:"disable-form",attrs:{value:e.getAgreementDisplay(t.serviceTypeId),disabled:"",placeholder:"合约类型"}})],1),s("el-form-item",{attrs:{label:"业务须知"}},[s("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"业务须知"},model:{value:e.getServiceInstance(t.serviceTypeId).inquiryNotice,callback:function(s){e.$set(e.getServiceInstance(t.serviceTypeId),"inquiryNotice",s)},expression:"getServiceInstance(item.serviceTypeId).inquiryNotice"}})],1)],1):e._e()],1),s("transition",{attrs:{name:"fade"}},[e.branchInfo?s("el-col",{attrs:{span:3}},[s("el-form-item",{attrs:{label:"商务单号"}},[s("el-row",[s("el-col",{attrs:{span:20}},[s("el-input",{staticClass:"disable-form",attrs:{value:e.form.psaNo,disabled:""}})],1),s("el-col",{attrs:{span:4}},[s("el-button",{staticStyle:{color:"red"},attrs:{size:"mini",type:"text"},on:{click:e.psaBookingCancel}},[e._v(" 取消 ")])],1)],1)],1)],1):e._e()],1),s("transition",{attrs:{name:"fade"}},[e.branchInfo?s("el-col",{attrs:{span:3}},[s("el-form-item",{attrs:{label:"入仓号"}},[s("el-input",{class:e.psaVerify||e.disabled?"disable-form":"",attrs:{disabled:e.psaVerify||e.disabled,placeholder:"入仓号"},model:{value:e.getServiceObject(t.serviceTypeId).warehousingNo,callback:function(s){e.$set(e.getServiceObject(t.serviceTypeId),"warehousingNo",s)},expression:"getServiceObject(item.serviceTypeId).warehousingNo"}})],1)],1):e._e()],1),e.branchInfo?s("el-col",{attrs:{span:9}}):e._e(),s("transition",{attrs:{name:"fade"}},[e.logisticsInfo?s("el-col",{attrs:{span:4}},[s("logistics-progress",{attrs:{disabled:e.getFormDisable()||e.disabled||e.psaVerify,"logistics-progress-data":e.getServiceObject().rsOpLogList||[],"open-logistics-progress-list":!0,"process-type":4,"service-type":80},on:{deleteItem:function(s){return e.deleteLogItem(t.serviceTypeId,s)},return:function(s){return e.updateLogList(t.serviceTypeId,s)}}})],1):e._e()],1),e.chargeInfo?s("el-col",{attrs:{span:10.3}},[s("charge-list",{attrs:{"a-t-d":e.form.podEta,"charge-data":e.getServiceObject().rsChargeList||[],"company-list":e.companyList,disabled:e.getFormDisable()||e.disabled,"is-receivable":!1,"open-charge-list":!0,"pay-detail-r-m-b":e.getServiceObject().payableRMB,"pay-detail-r-m-b-tax":e.getServiceObject().payableRMBTax,"pay-detail-u-s-d":e.getServiceObject().payableUSD,"pay-detail-u-s-d-tax":e.getServiceObject().payableUSDTax,"service-type-id":t.serviceTypeId},on:{copyFreight:function(t){return e.copyFreight(t)},deleteAll:function(s){return e.deleteAllCharge(t.serviceTypeId)},deleteItem:function(s){return e.deleteChargeItem(t.serviceTypeId,s)},return:function(s){e.calculateCharge(t.serviceTypeId,s,e.getServiceObject())}}})],1):e._e()],1):e._e()],1)],1)})),0)},a=[],n=(s("d3b7"),s("6062"),s("3ca3"),s("ddb0"),s("7db0"),s("4de4"),s("788f")),r=s("b59f"),o=s("4fbf"),c={name:"WhsComponent",components:{Audit:n["default"],LogisticsProgress:r["default"],ChargeList:o["default"]},props:{whsServices:{type:[Array,Set],default:function(){return[]}},form:{type:Object,default:function(){return{}}},branchInfo:{type:Boolean,default:!0},logisticsInfo:{type:Boolean,default:!0},chargeInfo:{type:Boolean,default:!0},auditInfo:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},booking:{type:Boolean,default:!1},psaVerify:{type:Boolean,default:!1},supplierList:{type:Array,default:function(){return[]}},companyList:{type:Array,default:function(){return[]}},foldState:{type:Boolean,default:!1},serviceInstance:{type:Object,default:function(){return{}}},serviceObject:{type:Object,default:function(){return{}}},formDisable:{type:Boolean,default:!1}},computed:{isDisabled:function(){return this.disabled||this.psaVerify}},methods:{getSupplierEmail:function(){var e=this.getServiceInstance();if(!e||!e.supplierId)return"";var t=this.supplierList.find((function(t){return t.companyId===e.supplierId}));return t?t.staffEmail:""},getAgreementDisplay:function(){var e=this.getServiceInstance();return e?e.agreementTypeCode+e.agreementNo:""},changeFold:function(e){this.$emit("changeFold",e)},getFold:function(){return this.foldState},getServiceInstance:function(){return this.serviceInstance||{}},getServiceObject:function(){return this.serviceObject||{}},getPayable:function(){return this.serviceObject&&this.serviceObject.payable||null},getFormDisable:function(){return this.formDisable},changeServiceObject:function(e,t){this.$emit("changeServiceObject",e,t)},auditCharge:function(e,t){this.$emit("auditCharge",e,t)},generateFreight:function(e,t,s){this.$emit("generateFreight",e,t,s)},psaBookingCancel:function(){this.$emit("psaBookingCancel")},copyFreight:function(e){this.$emit("copyFreight",e)},calculateCharge:function(e,t,s){this.$emit("calculateCharge",e,t,s)},outboundPlan:function(){this.$emit("outboundPlan")},deleteLogItem:function(e,t){var s=this.getServiceObject();s&&s.rsOpLogList&&(s.rsOpLogList=s.rsOpLogList.filter((function(e){return e!==t})))},updateLogList:function(e,t){var s=this.getServiceObject();s&&(s.rsOpLogList=t)},deleteAllCharge:function(e){var t=this.getServiceObject();t&&(t.rsChargeList=[])},deleteChargeItem:function(e,t){var s=this.getServiceObject();s&&s.rsChargeList&&(s.rsChargeList=s.rsChargeList.filter((function(e){return e!==t})))}}},l=c,f=(s("33ad"),s("2877")),d=Object(f["a"])(l,i,a,!1,null,"fcafe984",null);t["default"]=d.exports},e350:function(e,t,s){"use strict";s.d(t,"a",(function(){return a})),s.d(t,"b",(function(){return n}));s("d3b7"),s("caad"),s("2532");var i=s("4360");function a(e){if(e&&e instanceof Array&&e.length>0){var t=i["a"].getters&&i["a"].getters.permissions,s=e,a="*:*:*",n=t.some((function(e){return a==e||s.includes(e)}));return!!n}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function n(e){if(e&&e instanceof Array&&e.length>0){var t=i["a"].getters&&i["a"].getters.roles,s=e,a="admin",n=t.some((function(e){return a==e||s.includes(e)}));return!!n}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}},fff5:function(e,t,s){"use strict";s.d(t,"i",(function(){return a})),s.d(t,"b",(function(){return n})),s.d(t,"g",(function(){return r})),s.d(t,"a",(function(){return o})),s.d(t,"l",(function(){return c})),s.d(t,"e",(function(){return l})),s.d(t,"c",(function(){return f})),s.d(t,"h",(function(){return d})),s.d(t,"j",(function(){return u})),s.d(t,"f",(function(){return m})),s.d(t,"d",(function(){return p})),s.d(t,"m",(function(){return b})),s.d(t,"k",(function(){return h}));var i=s("b775");function a(e){return Object(i["a"])({url:"/system/rscharge/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/system/rscharge/aggregator",method:"post",data:e})}function r(e){return Object(i["a"])({url:"/system/rscharge/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/system/rscharge",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/system/rscharge",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/system/rscharge/"+e,method:"delete"})}function f(e,t){var s={chargeId:e,status:t};return Object(i["a"])({url:"/system/rscharge/changeStatus",method:"put",data:s})}function d(e){return Object(i["a"])({url:"/system/rscharge/charges",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/system/rscharge/selectList",method:"get",params:e})}function m(e){return Object(i["a"])({url:"/system/rscharge/findHedging",method:"get",params:e})}function p(e){return Object(i["a"])({url:"/system/rscharge/writeoff",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/system/rscharge/verify",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/system/rscharge/turnback",method:"post",data:e})}}}]);