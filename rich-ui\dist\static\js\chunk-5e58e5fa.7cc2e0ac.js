(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5e58e5fa","chunk-95a89c3c","chunk-2e28b918","chunk-2d0c1231","chunk-2d0cfc88","chunk-2d22252d","chunk-2d0d69a4"],{1369:function(e,t,a){"use strict";a("4071")},"18c9":function(e,t,a){},"20f5":function(e,t,a){"use strict";a.d(t,"f",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"d",(function(){return s})),a.d(t,"a",(function(){return l})),a.d(t,"k",(function(){return n})),a.d(t,"b",(function(){return c})),a.d(t,"i",(function(){return d})),a.d(t,"j",(function(){return p})),a.d(t,"g",(function(){return u})),a.d(t,"h",(function(){return f})),a.d(t,"e",(function(){return m}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/rct/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/rct/"+e,method:"get"})}function s(){return Object(r["a"])({url:"/system/rctold/mon",method:"get"})}function l(e){return Object(r["a"])({url:"/system/rct",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/system/rct",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/rct/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/rctold/saveRctLogistics",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/rctold/saveRctPreCarriage",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/rctold/saveRctExportDeclaration",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/rctold/saveRctImportClearance",method:"post",data:e})}function m(){return Object(r["a"])({url:"system/rctold/getRctStatistics",method:"get"})}},"34bb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.openOpHistory?"":"none"},attrs:{span:16}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("操作历史记录（文件管理）")])]),a("div",{class:{inactive:0==e.openOpHistory,active:e.openOpHistory}},[a("el-table",{staticClass:"pd0",attrs:{data:e.opHistory,border:""}},[a("el-table-column",{attrs:{align:"center",label:"操作进度流水",prop:"operationalProcessId","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"进度名称",prop:"processId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processId,placeholder:"进度状态",type:"process"},on:{return:function(t){e.row.processId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"进度状态",prop:"processStatusId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.row.processStatusId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发送方",prop:"senderId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{placeholder:"发送方",filterable:""},on:{change:function(a){return e.getSender(a,t.row)}},model:{value:t.row.senderId,callback:function(a){e.$set(t.row,"senderId",a)},expression:"scope.row.senderId"}},e._l(e.$store.state.data.companyList,(function(e){return a("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"接收方",prop:"receiverId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{filterable:"",placeholder:"接收方"},on:{change:function(a){return e.getReceiver(a,t.row)}},model:{value:t.row.receiverId,callback:function(a){e.$set(t.row,"receiverId",a)},expression:"scope.row.receiverId"}},e._l(e.$store.state.data.companyList,(function(e){return a("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),a("el-table-column",{attrs:{label:"随附文件列表"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.docList,(function(r){return a("el-button",{key:r.docDetailId,staticStyle:{padding:"0"},attrs:{size:"small",type:"text"},on:{click:function(a){return e.checkDoc(r,t.row)}}},[e._v(" "+e._s("["+r.flowNo+"]")+" ")])})),a("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleAddDocList(t.row)}}},[e._v(" [＋] ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"交互方式",prop:"releaseWayId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.releaseWayId,placeholder:"交互方式",type:"docReleaseWay"},on:{return:function(t){e.row.releaseWayId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发生时间",prop:"processStatusTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"进度发生时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.row.processStatusTime,callback:function(a){e.$set(t.row,"processStatusTime",a)},expression:"scope.row.processStatusTime"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:"备注"},model:{value:t.row.remark,callback:function(a){e.$set(t.row,"remark",a)},expression:"scope.row.remark"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发送",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{icon:"el-icon-s-promotion",size:"mini",type:"primary"},on:{click:e.handleSend}},[e._v("Send ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作员",prop:"opName",width:"50px"}}),a("el-table-column",{attrs:{align:"center",label:"录入时间",prop:"createTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.createTime,"{y}/{m}/{d}"))+" ")]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.delOpHistory(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addOpHistory}},[e._v("[＋] ")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"新增操作历史记录",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"docList",attrs:{model:e.doc,border:"","label-width":"68px"}},[a("el-form-item",{attrs:{label:"流向编号",prop:"flowNo"}},[a("el-input",{attrs:{placeholder:"文件名/流向编号"},model:{value:e.doc.flowNo,callback:function(t){e.$set(e.doc,"flowNo",t)},expression:"doc.flowNo"}})],1),a("el-form-item",{attrs:{label:"文件类型",prop:"docId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docId,placeholder:"文件类型",type:"doc"},on:{return:function(t){e.doc.docId=t}}})],1),a("el-form-item",{attrs:{label:"文件流向",prop:"docFlowDirectionId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docFlowDirectionId,placeholder:"文件流向",type:"docFlowDirection"},on:{return:function(t){e.doc.docFlowDirectionId=t}}})],1),a("el-form-item",{attrs:{label:"文件形式",prop:"issueTypeId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.issueTypeId,placeholder:"文件形式",type:"docIssueType"},on:{return:function(t){e.doc.issueTypeId=t}}})],1),a("el-form-item",{attrs:{label:"创建时间",prop:"createTime"}},[e._v(" "+e._s(e.parseTime(e.doc.createTime,"{y}/{m}/{d}"))+" ")]),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:1},maxlength:"300",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.doc.remark,callback:function(t){e.$set(e.doc,"remark",t)},expression:"doc.remark"}})],1),a("el-form-item",{attrs:{label:"创建人",prop:"createByName"}},[e._v(" "+e._s(e.doc.createByName)+" ")]),a("el-form-item",{attrs:{label:"录入时间",prop:"updateTime"}},[e._v(" "+e._s(e.parseTime(e.doc.updateTime,"{y}/{m}/{d}"))+" ")]),a("file-upload",{attrs:{"file-type":["xlsx","xls","docx","doc","pdf"],limit:3,value:e.doc.fileList},on:{input:function(t){e.doc.fileList=t}}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitDoc}},[e._v("确 定")]),a("el-button",{attrs:{type:"danger"},on:{click:e.delDoc}},[e._v("删 除")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},i=[],o=(a("b0c0"),a("14d9"),a("4de4"),a("d3b7"),a("fba1")),s=a("3a13"),l=a("b775");function n(e){return Object(l["a"])({url:"/system/docdetail/"+e,method:"get"})}function c(e){return Object(l["a"])({url:"/system/docdetail",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/system/docdetail",method:"put",data:e})}function p(e){return Object(l["a"])({url:"/system/docdetail/"+e,method:"delete"})}var u={name:"opHistory",props:["opHistory","openOpHistory","typeId","rctId","basicInfoId"],data:function(){return{open:!1,doc:{},dList:{},receive:!1,send:!1}},watch:{opHistory:function(e){this.$emit("return",e)}},methods:{handleAddDocList:function(e){this.open=!0,this.doc={operationalProcessId:e.operationalProcessId,docId:null,flowNo:null,docFlowDirectionId:null,issueTypeId:null,fileList:null,createBy:this.$store.state.user.sid,createByName:this.$store.state.user.name.split(" ")[1],createTime:Object(o["f"])(new Date),remark:null,updateTime:Object(o["f"])(new Date)},this.dList=e},checkDoc:function(e,t){var a=this;n(e.docDetailId).then((function(e){a.doc=e.data,a.open=!0})),this.dList=t},getSender:function(e,t){this.send=!0,this.receive=!1,this.receive||(t.senderId=e,t.receiverId=1)},getReceiver:function(e,t){this.receive=!0,this.send=!1,this.send||(t.receiverId=e,t.senderId=1)},handleSend:function(){},addOpHistory:function(){var e=this,t={docList:[],typeId:this.typeId,rctId:this.rctId,basicInfoId:this.basicInfoId,processId:null,processStatusId:null,senderId:null,receiverId:null,releaseWayId:null,processStatusTime:Object(o["f"])(new Date),remark:null,opId:this.$store.state.user.sid,opName:this.$store.state.user.name.split(" ")[1],createTime:Object(o["f"])(new Date)};Object(s["a"])(t).then((function(a){e.opHistory.push(t)}))},delOpHistory:function(e){var t=this,a={operationalProcessId:e.operationalProcessId,typeId:e.typeId,rctId:e.rctId,basicInfoId:e.basicInfoId};Object(s["c"])(a).then((function(a){t.$message.success(a.msg),t.opHistory=t.opHistory.filter((function(t){return t.operationalProcessId!=e.operationalProcessId}))}))},submitDoc:function(){var e=this;null==this.doc.docDetailId?c(this.doc).then((function(t){null==e.dList.docList&&(e.dList.docList=[]),e.dList.docList.push(e.doc),console.log(e.doc)})):d(this.doc).then((function(t){e.$message.success("修改成功"),console.log(e.doc)})),this.open=!1},delDoc:function(){var e=this;null!=this.doc.docDetailId&&p(this.doc.docDetailId).then((function(t){e.$message.success("删除成功"),e.dList.docList=e.dList.docList.filter((function(t){return t.docDetailId!=e.doc.docDetailId})),e.doc={}}))},cancel:function(){this.open=!1}}},f=u,m=a("2877"),b=Object(m["a"])(f,r,i,!1,null,"5f398ee3",null);t["default"]=b.exports},38868:function(e,t,a){"use strict";a("18c9")},"3a13":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return l})),a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return c}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/operationalprocess/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/operationalprocess/"+e,method:"get"})}function s(e){return Object(r["a"])({url:"/system/operationalprocess",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/system/operationalprocess",method:"put",data:e})}function n(e){return Object(r["a"])({url:"/system/operationalprocess/del",method:"post",data:e})}function c(e,t){var a={operationalProcessId:e,status:t};return Object(r["a"])({url:"/system/operationalprocess/changeStatus",method:"put",data:a})}},4071:function(e,t,a){},4582:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1500px"},on:{"update:visible":function(t){e.oopen=t}}},[a("div",{staticStyle:{display:"flex"}},[a("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),a("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.open=!0}}},[e._v("新增")])],1)]),a("el-table",{attrs:{border:"",data:e.logisticsNoInfo,"row-class-name":e.rowIndex}},[a("el-table-column",{attrs:{"header-align":"center",label:"SO号码",prop:"soNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"主提单号",prop:"mblNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"货代单号",prop:"hblNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"柜号信息",prop:"containersInfo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"发货人",prop:"shipper"}}),a("el-table-column",{attrs:{"header-align":"center",label:"收货人",prop:"consignee"}}),a("el-table-column",{attrs:{"header-align":"center",label:"通知人",prop:"notifyParty"}}),a("el-table-column",{attrs:{"header-align":"center",label:"启运港放舱代理",prop:"polBookingAgent"}}),a("el-table-column",{attrs:{"header-align":"center",label:"目的港换单代理",prop:"podHandleAgent"}}),a("el-table-column",{attrs:{"header-align":"center",label:"唛头",prop:"shippingMark"}}),a("el-table-column",{attrs:{"header-align":"center",label:"货描",prop:"goodsDescription"}}),a("el-table-column",{attrs:{"header-align":"center",label:"签单日期",prop:"blIssueDate"}}),a("el-table-column",{attrs:{"header-align":"center",label:"签单地点",prop:"blIssueLocation"}}),a("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[a("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(t){e.$set(e.form,"soNo",t)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"主提单号",prop:"mblNo"}},[a("el-input",{attrs:{placeholder:"主提单号"},model:{value:e.form.mblNo,callback:function(t){e.$set(e.form,"mblNo",t)},expression:"form.mblNo"}})],1),a("el-form-item",{attrs:{label:"货代单号",prop:"hblNo"}},[a("el-input",{attrs:{placeholder:"货代单号"},model:{value:e.form.hblNo,callback:function(t){e.$set(e.form,"hblNo",t)},expression:"form.hblNo"}})],1),a("el-form-item",{attrs:{label:"柜号信息",prop:"containersInfo"}},[a("el-input",{attrs:{placeholder:"柜号信息"},model:{value:e.form.containersInfo,callback:function(t){e.$set(e.form,"containersInfo",t)},expression:"form.containersInfo"}})],1),a("el-form-item",{attrs:{label:"发货人",prop:"shipper"}},[a("el-input",{attrs:{placeholder:"发货人"},model:{value:e.form.shipper,callback:function(t){e.$set(e.form,"shipper",t)},expression:"form.shipper"}})],1),a("el-form-item",{attrs:{label:"收货人",prop:"consignee"}},[a("el-input",{attrs:{placeholder:"收货人"},model:{value:e.form.consignee,callback:function(t){e.$set(e.form,"consignee",t)},expression:"form.consignee"}})],1),a("el-form-item",{attrs:{label:"通知人",prop:"notifyParty"}},[a("el-input",{attrs:{placeholder:"通知人"},model:{value:e.form.notifyParty,callback:function(t){e.$set(e.form,"notifyParty",t)},expression:"form.notifyParty"}})],1),a("el-form-item",{attrs:{label:"启运港放舱代理",prop:"polBookingAgent"}},[a("el-input",{attrs:{placeholder:"启运港放舱代理"},model:{value:e.form.polBookingAgent,callback:function(t){e.$set(e.form,"polBookingAgent",t)},expression:"form.polBookingAgent"}})],1),a("el-form-item",{attrs:{label:"目的港换单代理",prop:"podHandleAgent"}},[a("el-input",{attrs:{placeholder:"目的港换单代理"},model:{value:e.form.podHandleAgent,callback:function(t){e.$set(e.form,"podHandleAgent",t)},expression:"form.podHandleAgent"}})],1),a("el-form-item",{attrs:{label:"唛头",prop:"shippingMark"}},[a("el-input",{attrs:{placeholder:"唛头"},model:{value:e.form.shippingMark,callback:function(t){e.$set(e.form,"shippingMark",t)},expression:"form.shippingMark"}})],1),a("el-form-item",{attrs:{label:"货描",prop:"goodsDescription"}},[a("el-input",{attrs:{placeholder:"货描"},model:{value:e.form.goodsDescription,callback:function(t){e.$set(e.form,"goodsDescription",t)},expression:"form.goodsDescription"}})],1),a("el-form-item",{attrs:{label:"签单日期",prop:"blIssueDate"}},[a("el-input",{attrs:{placeholder:"签单日期"},model:{value:e.form.blIssueDate,callback:function(t){e.$set(e.form,"blIssueDate",t)},expression:"form.blIssueDate"}})],1),a("el-form-item",{attrs:{label:"签单地点",prop:"blIssueLocation"}},[a("el-input",{attrs:{placeholder:"签单地点"},model:{value:e.form.blIssueLocation,callback:function(t){e.$set(e.form,"blIssueLocation",t)},expression:"form.blIssueLocation"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],o=(a("4de4"),a("d3b7"),a("14d9"),{name:"logisticsNoInfo",props:["openLogisticsNoInfo"],watch:{logisticsNoInfo:function(){this.$emit("return",this.logisticsNoInfo)},openLogisticsNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,logisticsNoInfo:[],form:{}}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.logisticsNoInfo=this.logisticsNoInfo.filter((function(t){return t.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.logisticsNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,mblNo:null,hblNo:null,containersInfo:null,shipper:null,consignee:null,notifyParty:null,polBookingAgent:null,podHandleAgent:null,shippingMark:null,goodsDescription:null,blIssueDate:null,blIssueLocation:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),s=o,l=a("2877"),n=Object(l["a"])(s,r,i,!1,null,null,null);t["default"]=n.exports},"50fe":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/serviceinstances",method:"put",data:e})}},"5eaa":function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"e",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return l})),a.d(t,"j",(function(){return n})),a.d(t,"b",(function(){return c})),a.d(t,"h",(function(){return d})),a.d(t,"i",(function(){return p})),a.d(t,"f",(function(){return u})),a.d(t,"g",(function(){return f}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/booking/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/booking/psalist",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/system/booking/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/booking",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/system/booking",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/booking/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/booking/saveBookingLogistics",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/booking/saveBookingPreCarriage",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/booking/saveBookingExportDeclaration",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/booking/saveBookingImportClearance",method:"post",data:e})}},"64d8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1200px"},on:{"update:visible":function(t){e.oopen=t}}},[a("div",{staticStyle:{display:"flex"}},[a("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),a("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.open=!0}}},[e._v("新增")])],1)]),a("el-table",{attrs:{border:"",data:e.preCarriageNoInfo}},[a("el-table-column",{attrs:{label:"SO号码",prop:"soNo"}}),a("el-table-column",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}}),a("el-table-column",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}}),a("el-table-column",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}}),a("el-table-column",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}}),a("el-table-column",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}}),a("el-table-column",{attrs:{label:"到场时间",prop:"preCarriageTime"}}),a("el-table-column",{attrs:{label:"柜号",prop:"containerNo"}}),a("el-table-column",{attrs:{label:"柜型",prop:"containerType"}}),a("el-table-column",{attrs:{label:"封条",prop:"sealNo"}}),a("el-table-column",{attrs:{label:"磅单",prop:"weightPaper"}}),a("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[a("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(t){e.$set(e.form,"soNo",t)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}},[a("el-input",{attrs:{placeholder:"司机姓名"},model:{value:e.form.preCarriageDriverName,callback:function(t){e.$set(e.form,"preCarriageDriverName",t)},expression:"form.preCarriageDriverName"}})],1),a("el-form-item",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}},[a("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.form.preCarriageDriverTel,callback:function(t){e.$set(e.form,"preCarriageDriverTel",t)},expression:"form.preCarriageDriverTel"}})],1),a("el-form-item",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}},[a("el-input",{attrs:{placeholder:"司机车牌"},model:{value:e.form.preCarriageTruckNo,callback:function(t){e.$set(e.form,"preCarriageTruckNo",t)},expression:"form.preCarriageTruckNo"}})],1),a("el-form-item",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}},[a("el-input",{attrs:{placeholder:"司机备注"},model:{value:e.form.preCarriageTruckRemark,callback:function(t){e.$set(e.form,"preCarriageTruckRemark",t)},expression:"form.preCarriageTruckRemark"}})],1),a("el-form-item",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}},[a("el-input",{attrs:{placeholder:"装柜地址"},model:{value:e.form.preCarriageAddress,callback:function(t){e.$set(e.form,"preCarriageAddress",t)},expression:"form.preCarriageAddress"}})],1),a("el-form-item",{attrs:{label:"到场时间",prop:"preCarriageTime"}},[a("el-input",{attrs:{placeholder:"到场时间"},model:{value:e.form.preCarriageTime,callback:function(t){e.$set(e.form,"preCarriageTime",t)},expression:"form.preCarriageTime"}})],1),a("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[a("el-input",{attrs:{placeholder:"柜号"},model:{value:e.form.containerNo,callback:function(t){e.$set(e.form,"containerNo",t)},expression:"form.containerNo"}})],1),a("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[a("el-input",{attrs:{placeholder:"柜型"},model:{value:e.form.containerType,callback:function(t){e.$set(e.form,"containerType",t)},expression:"form.containerType"}})],1),a("el-form-item",{attrs:{label:"封条",prop:"sealNo"}},[a("el-input",{attrs:{placeholder:"封条"},model:{value:e.form.sealNo,callback:function(t){e.$set(e.form,"sealNo",t)},expression:"form.sealNo"}})],1),a("el-form-item",{attrs:{label:"磅单",prop:"weightPaper"}},[a("el-input",{attrs:{placeholder:"磅单"},model:{value:e.form.weightPaper,callback:function(t){e.$set(e.form,"weightPaper",t)},expression:"form.weightPaper"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],o=(a("4de4"),a("d3b7"),a("14d9"),{name:"PreCarriageNoInfo",props:["openPreCarriageNoInfo"],watch:{preCarriageNoInfo:function(){this.$emit("return",this.preCarriageNoInfo)},openPreCarriageNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,preCarriageNoInfo:[],form:{}}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.preCarriageNoInfo=this.preCarriageNoInfo.filter((function(t){return t.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.preCarriageNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,preCarriageDriverName:null,preCarriageDriverTel:null,preCarriageTruckNo:null,preCarriageTruckRemark:null,preCarriageAddress:null,preCarriageTime:null,containerNo:null,containerType:null,sealNo:null,weightPaper:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),s=o,l=a("2877"),n=Object(l["a"])(s,r,i,!1,null,null,null);t["default"]=n.exports},"72f9":function(e,t,a){(function(t,a){e.exports=a()})(0,(function(){function e(o,s){if(!(this instanceof e))return new e(o,s);s=Object.assign({},a,s);var l=Math.pow(10,s.precision);this.intValue=o=t(o,s),this.value=o/l,s.increment=s.increment||1/l,s.groups=s.useVedic?i:r,this.s=s,this.p=l}function t(t,a){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],i=a.decimal,o=a.errorOnInvalid,s=a.fromCents,l=Math.pow(10,a.precision),n=t instanceof e;if(n&&s)return t.intValue;if("number"===typeof t||n)i=n?t.value:t;else if("string"===typeof t)o=new RegExp("[^-\\d"+i+"]","g"),i=new RegExp("\\"+i,"g"),i=(i=t.replace(/\((.*)\)/,"-$1").replace(o,"").replace(i,"."))||0;else{if(o)throw Error("Invalid Input");i=0}return s||(i=(i*l).toFixed(4)),r?Math.round(i):i}var a={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var a=t.pattern,r=t.negativePattern,i=t.symbol,o=t.separator,s=t.decimal;t=t.groups;var l=(""+e).replace(/^-/,"").split("."),n=l[0];return l=l[1],(0<=e.value?a:r).replace("!",i).replace("#",n.replace(t,"$1"+o)+(l?s+l:""))},fromCents:!1},r=/(\d)(?=(\d{3})+\b)/g,i=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(a){var r=this.s,i=this.p;return e((this.intValue+t(a,r))/(r.fromCents?1:i),r)},subtract:function(a){var r=this.s,i=this.p;return e((this.intValue-t(a,r))/(r.fromCents?1:i),r)},multiply:function(t){var a=this.s;return e(this.intValue*t/(a.fromCents?1:Math.pow(10,a.precision)),a)},divide:function(a){var r=this.s;return e(this.intValue/t(a,r,!1),r)},distribute:function(t){var a=this.intValue,r=this.p,i=this.s,o=[],s=Math[0<=a?"floor":"ceil"](a/t),l=Math.abs(a-s*t);for(r=i.fromCents?1:r;0!==t;t--){var n=e(s/r,i);0<l--&&(n=n[0<=a?"add":"subtract"](1/r)),o.push(n)}return o},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"788f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.audit?"":"none"},attrs:{span:15}},[a("div",{class:{inactive:0==e.audit,active:e.audit}},[a("el-col",{staticStyle:{display:"flex","border-radius":"5px"}},[a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:opapproval","system:rct:opapproval"],expression:"['system:booking:opapproval','system:rct:opapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:opapproval"]),icon:e.basicInfo.isDnOpConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("op")}}},[e._v("操作确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.opConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.opConfirmedDate))])])],1),a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:psaapproval","system:rct:psaapproval"],expression:"['system:booking:psaapproval','system:rct:psaapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:psaapproval"]),icon:e.basicInfo.isDnPsaConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("psa")}}},[e._v("商务确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.psaConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.psaConfirmedDate))])])],1),a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:supplierapproval","system:rct:supplierapproval"],expression:"['system:booking:supplierapproval','system:rct:supplierapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:supplierapproval"]),icon:e.basicInfo.isDnSupplierConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("supplier")}}},[e._v("供应商确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.supplierConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.supplierConfirmedDate))])])],1),e.checkPermi(["system:booking:financeapproval","system:rct:financeapproval"])?a("div",{staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:financeapproval"]),icon:e.basicInfo.isAccountConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("account")}}},[e._v("财务确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.accountConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.accountConfirmedDate))])])],1):e._e()])],1)])},i=[],o=(a("4de4"),a("d3b7"),a("d81d"),a("fba1")),s=a("e350"),l=a("72f9"),n=a.n(l),c=a("fff5"),d=a("50fe"),p={name:"audit",props:["audit","basicInfo","audits","payable","disabled","rsChargeList"],data:function(){var e=this;return{opConfirmedName:this.basicInfo&&this.basicInfo.isDnOpConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName:null,opConfirmedDate:this.basicInfo&&this.basicInfo.opConfirmedTime?this.basicInfo.opConfirmedTime:null,accountConfirmedName:this.basicInfo&&this.basicInfo.isAccountConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName:null,accountConfirmedDate:this.basicInfo&&this.basicInfo.accountConfirmTime?this.basicInfo.accountConfirmTime:null,supplierConfirmedName:this.basicInfo&&this.basicInfo.isDnSupplierConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName:null,supplierConfirmedDate:this.basicInfo&&this.basicInfo.supplierConfirmedTime?this.basicInfo.supplierConfirmedTime:null,psaConfirmedName:this.basicInfo&&this.basicInfo.isDnPsaConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName:null,psaConfirmedDate:this.basicInfo&&this.basicInfo.psaConfirmedTime?this.basicInfo.psaConfirmedTime:null,salesConfirmedName:null,salesConfirmedDate:null}},watch:{basicInfo:function(e){this.$emit("return",e)}},methods:{currency:n.a,checkPermi:s["a"],confirmed:function(e){var t=this;"op"==e&&(this.basicInfo.isDnOpConfirmed?(this.basicInfo.isDnOpConfirmed=null,this.basicInfo.opConfirmedTime=null,this.opConfirmedName=null,this.opConfirmedDate=null):(this.basicInfo.isDnOpConfirmed=this.$store.state.user.sid,this.basicInfo.opConfirmedTime=Object(o["f"])(new Date,"{y}-{m}-{d}"),this.opConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName,this.opConfirmedDate=this.basicInfo.opConfirmedTime),this.updateServiceInstance(this.basicInfo)),"account"==e&&(this.basicInfo.isAccountConfirmed?(this.basicInfo.isAccountConfirmed=null,this.basicInfo.accountConfirmTime=null,this.accountConfirmedName=null,this.accountConfirmedDate=null):(this.basicInfo.isAccountConfirmed=this.$store.state.user.sid,this.basicInfo.accountConfirmTime=Object(o["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.accountConfirmTime,this.$emit("auditFee",this.rsChargeList.map((function(e){return 1!=e.isAccountConfirmed&&(e.isAccountConfirmed=1,Object(c["l"])(e)),e})))),this.updateServiceInstance(this.basicInfo)),"sales"==e&&(this.basicInfo.isDnSalesConfirmed?(this.basicInfo.isDnSalesConfirmed=null,this.basicInfo.salesConfirmedTime=null,this.salesConfirmedName=null,this.salesConfirmedDate=null):(this.basicInfo.isDnSalesConfirmed=this.$store.state.user.sid,this.basicInfo.salesConfirmedTime=Object(o["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.salesConfirmedTime),this.updateServiceInstance(this.basicInfo)),"psa"==e&&(this.basicInfo.isDnPsaConfirmed?(this.basicInfo.isDnPsaConfirmed=null,this.basicInfo.psaConfirmedTime=null,this.psaConfirmedName=null,this.psaConfirmedDate=null):(this.basicInfo.isDnPsaConfirmed=this.$store.state.user.sid,this.basicInfo.psaConfirmedTime=Object(o["f"])(new Date,"{y}-{m}-{d}"),this.psaConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName,this.psaConfirmedDate=this.basicInfo.psaConfirmedTime),this.updateServiceInstance(this.basicInfo)),"supplier"==e&&(this.basicInfo.isDnSupplierConfirmed?(this.basicInfo.isDnSupplierConfirmed=null,this.basicInfo.supplierConfirmedTime=null,this.supplierConfirmedName=null,this.supplierConfirmedDate=null):(this.basicInfo.isDnSupplierConfirmed=this.$store.state.user.sid,this.basicInfo.supplierConfirmedTime=Object(o["f"])(new Date,"{y}-{m}-{d}"),this.supplierConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName,this.supplierConfirmedDate=this.basicInfo.supplierConfirmedTime),this.updateServiceInstance(this.basicInfo))},updateServiceInstance:function(e){Object(d["a"])(e)}}},u=p,f=(a("38868"),a("2877")),m=Object(f["a"])(u,r,i,!1,null,"7e84911e",null);t["default"]=m.exports},b6f1:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{margin:"15px",width:"auto"}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"63px"}},[a("el-row",[a("el-col",{staticStyle:{width:"75%","margin-right":"10px"}},[a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{class:"booking"==e.type?"booking":"",attrs:{span:4}},[a("div",{staticStyle:{margin:"0 0 15px",padding:"0","font-size":"40px","text-align":"center",border:"1px solid #76933C"}},[e._v(" "+e._s("booking"===e.type?"委托申请单":"")+" "+e._s("op"===e.type?"操作单":"")+" ")]),a("el-form-item",{attrs:{label:"操作单号",prop:"rctNo"}},[a("el-input",{attrs:{placeholder:"操作单号",disabled:"booking"==e.type||e.psaVerify},on:{focus:function(t){return e.generateRct(!1)}},model:{value:e.form.rctNo,callback:function(t){e.$set(e.form,"rctNo",t)},expression:"form.rctNo"}}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"新增操作单号",visible:e.openGenerateRct,"append-to-body":"",width:"350px"},on:{"update:visible":function(t){e.openGenerateRct=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.rct,rules:e.rules,"label-width":"65px"}},[a("el-form-item",{attrs:{label:"单号规则"}},[a("el-input",{attrs:{placeholder:"前导字符+2位年份+2位月份+4位序列",disabled:""},model:{value:e.rct.rules,callback:function(t){e.$set(e.rct,"rules",t)},expression:"rct.rules"}})],1),a("el-form-item",{attrs:{label:"前导字符",prop:"leadingCharacter"}},[a("el-input",{attrs:{placeholder:"前导字符",disabled:""},model:{value:e.rct.leadingCharacter,callback:function(t){e.$set(e.rct,"leadingCharacter",t)},expression:"rct.leadingCharacter"}})],1),a("el-form-item",{attrs:{label:"所属月份"}},[a("el-radio-group",{model:{value:e.rct.month,callback:function(t){e.$set(e.rct,"month",t)},expression:"rct.month"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("本月单号")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("显示下月")])],1)],1),a("el-form-item",{attrs:{label:"单号序列"}},[a("el-radio-group",{model:{value:e.rct.noNum,callback:function(t){e.$set(e.rct,"noNum",t)},expression:"rct.noNum"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("自然序列")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("手动分配")])],1)],1),a("el-form-item",{attrs:{label:"单号预览"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{attrs:{disabled:"1"==e.rct.noNum},model:{value:e.rct.rctNo,callback:function(t){e.$set(e.rct,"rctNo",t)},expression:"rct.rctNo"}}),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(t){return e.generateRct(!0)}}},[e._v(" "+e._s("1"==e.rct.noNum?"生成":"")+" "+e._s("2"==e.rct.noNum?"校验":"")+" ")])],1)])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.confirmRct}},[e._v("确 定")]),a("el-button",{attrs:{size:"mini"},on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1),a("el-form-item",{attrs:{label:"操作日期",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:"booking"==e.type||e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"操作日期"},model:{value:e.form.rctOpDate,callback:function(t){e.$set(e.form,"rctOpDate",t)},expression:"form.rctOpDate"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{class:e.psaVerify?"booking":"",attrs:{label:"操作员",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员",disabled:e.psaVerify},on:{open:e.loadOp,select:function(t){e.form.opId=t.staffId},input:function(t){void 0==t&&(e.form.opId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"订舱员",prop:"bookingOpId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"订舱员"==e.role.roleLocalName})),"show-count":!0,placeholder:"订舱员",disabled:"booking"==e.type||e.psaVerify},on:{open:e.loadOp,select:function(t){e.form.bookingOpId=t.staffId},input:function(t){void 0==t&&(e.form.bookingOpId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.bookingOpId,callback:function(t){e.bookingOpId=t},expression:"bookingOpId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"单证员",prop:"docOpId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"单证员"==e.role.roleLocalName})),"show-count":!0,placeholder:"单证员",disabled:"booking"==e.type||e.psaVerify},on:{open:e.loadOp,select:function(t){e.form.docOpId=t.staffId},input:function(t){void 0==t&&(e.form.docOpId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.docOpId,callback:function(t){e.docOpId=t},expression:"docOpId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"协助操作",prop:"opObserverId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList,"show-count":!0,placeholder:"协助操作",disabled:"booking"==e.type||e.psaVerify},on:{open:e.loadOp,select:function(t){e.form.opObserverId=t.staffId},input:function(t){void 0==t&&(e.form.opObserverId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.opObserverId,callback:function(t){e.opObserverId=t},expression:"opObserverId"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"订舱单号",prop:"newBookingNo"}},[a("el-input",{attrs:{placeholder:"订舱申请单号",disabled:e.psaVerify},model:{value:e.form.newBookingNo,callback:function(t){e.$set(e.form,"newBookingNo",t)},expression:"form.newBookingNo"}})],1),a("el-form-item",{attrs:{label:"订舱日期",prop:"newBookingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"订舱申请单日期"},model:{value:e.form.newBookingTime,callback:function(t){e.$set(e.form,"newBookingTime",t)},expression:"form.newBookingTime"}})],1),a("el-form-item",{attrs:{label:"报价单号",prop:"quotationNo"}},[a("el-input",{attrs:{placeholder:"报价单号",disabled:e.psaVerify},model:{value:e.form.quotationNo,callback:function(t){e.$set(e.form,"quotationNo",t)},expression:"form.quotationNo"}})],1),a("el-form-item",{attrs:{label:"报价日期",prop:"quotationDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"报价日期"},model:{value:e.form.quotationDate,callback:function(t){e.$set(e.form,"quotationDate",t)},expression:"form.quotationDate"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务员",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,disabled:e.psaVerify,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{open:e.loadSales,select:function(t){e.form.salesId=t.staffId},input:function(t){void 0==t&&(e.form.salesId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"业务助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,disabled:e.psaVerify,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{open:e.loadSales,select:function(t){e.form.salesAssistantId=t.staffId},input:function(t){void 0==t&&(e.form.salesAssistantId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"协助业务",prop:"salesObserverId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,disabled:e.psaVerify,options:e.belongList,"show-count":!0,placeholder:"协助业务"},on:{open:e.loadSales,select:function(t){e.form.salesObserverId=t.staffId},input:function(t){void 0==t&&(e.form.salesObserverId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesObserverId,callback:function(t){e.salesObserverId=t},expression:"salesObserverId"}})],1)],1),a("el-col",{class:"booking"!=e.type||e.psaVerify?"":"booking",attrs:{span:4}},[a("el-form-item",{attrs:{label:"商务审核",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{open:e.loadBusinesses,select:function(t){e.form.verifyPsaId=t.staffId},input:function(t){void 0==t&&(e.form.verifyPsaId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"审核时间",prop:"psaVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"商务审核时间"},model:{value:e.form.psaVerifyTime,callback:function(t){e.$set(e.form,"psaVerifyTime",t)},expression:"form.psaVerifyTime"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"紧急程度",prop:"urgencyDegree"}},[a("el-input",{attrs:{placeholder:"紧急程度",disabled:e.psaVerify},model:{value:e.form.urgencyDegree,callback:function(t){e.$set(e.form,"urgencyDegree",t)},expression:"form.urgencyDegree"}})],1),a("el-form-item",{attrs:{label:"收付方式",prop:"paymentTypeId"}},[a("tree-select",{attrs:{type:"paymentType",flat:!1,multiple:!1,pass:e.form.paymentTypeId,disabled:e.psaVerify,placeholder:"收付方式"},on:{return:function(t){e.form.paymentTypeId=t}}})],1),a("el-form-item",{attrs:{label:"放货方式",prop:"releaseTypeId"}},[a("tree-select",{attrs:{type:"releaseType",flat:!1,multiple:!1,pass:e.form.releaseTypeId,disabled:e.psaVerify,placeholder:"放货方式"},on:{return:function(t){e.form.releaseTypeId=t}}})],1),a("el-form-item",{attrs:{label:"进度状态",prop:"processStatusId"}},[a("tree-select",{attrs:{type:"processStatus",flat:!1,multiple:!1,pass:e.form.processStatusId,placeholder:"进度状态"},on:{return:function(t){e.form.processStatusId=t}}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"委托单位",prop:"clientId"}},[e.$store.state.data.clientList.length>0?a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.clientId,disabled:e.psaVerify,placeholder:"委托单位",type:"client"},on:{return:function(t){e.form.clientId=t}}}):e._e()],1),a("el-form-item",{attrs:{label:"客户角色",prop:"clientRoleId"}},[a("tree-select",{attrs:{type:"companyRole",flat:!1,multiple:!1,pass:e.form.clientRoleId,disabled:e.psaVerify,placeholder:"客户角色"},on:{return:function(t){e.form.clientRoleId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"联系人",prop:"clientContactor"}},[a("el-input",{attrs:{placeholder:"联系人称谓",disabled:e.psaVerify},model:{value:e.form.clientContactor,callback:function(t){e.$set(e.form,"clientContactor",t)},expression:"form.clientContactor"}})],1),a("el-form-item",{attrs:{label:"电话",prop:"clientContactorTel"}},[a("el-input",{attrs:{placeholder:"联系人电话"},model:{value:e.form.clientContactorTel,callback:function(t){e.$set(e.form,"clientContactorTel",t)},expression:"form.clientContactorTel"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"邮箱",prop:"clientContactorEmail"}},[a("el-input",{attrs:{placeholder:"联系人邮箱",disabled:e.psaVerify},model:{value:e.form.clientContactorEmail,callback:function(t){e.$set(e.form,"clientContactorEmail",t)},expression:"form.clientContactorEmail"}})],1),a("el-form-item",{attrs:{label:"关联单位",prop:"relationClientIds"}},[e.$store.state.data.clientList.length>0?a("tree-select",{attrs:{disabled:e.psaVerify,flat:!0,multiple:!0,pass:e.relationClientIds,placeholder:"客户",type:"client"},on:{return:e.getRelationClientIds}}):e._e()],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"进出口",prop:"impExpTypeId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"进出口",disabled:e.psaVerify},model:{value:e.form.impExpTypeId,callback:function(t){e.$set(e.form,"impExpTypeId",t)},expression:"form.impExpTypeId"}},[a("el-option",{attrs:{value:"1",label:"出口"}},[e._v("出口")]),a("el-option",{attrs:{value:"2",label:"进口"}},[e._v("进口")])],1)],1),a("el-form-item",{attrs:{label:"收汇方式",prop:"tradingPaymentChannelId"}},[a("tree-select",{attrs:{type:"paymentChannels",flat:!1,disabled:e.psaVerify,multiple:!1,pass:e.form.tradingPaymentChannelId,placeholder:"贸易付款方式"},on:{returnData:function(t){e.form.tradingPaymentChannel=t.paymentChannelsShortName}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"贸易条款",prop:"tradingTermsId"}},[a("tree-select",{attrs:{type:"tradingTerms",flat:!1,multiple:!1,disabled:e.psaVerify,pass:e.form.tradingTermsId,placeholder:"贸易条款"},on:{return:function(t){e.form.tradingTermsId=t}}})],1),a("el-form-item",{attrs:{label:"运输条款",prop:"logisticsTermsId"}},[a("tree-select",{attrs:{type:"transportationTerms",flat:!1,multiple:!1,disabled:e.psaVerify,pass:e.form.logisticsTermsId,placeholder:"运输条款"},on:{return:function(t){e.form.logisticsTermsId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"合同号",prop:"clientContractNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"(委托单位)合同号"},model:{value:e.form.clientContractNo,callback:function(t){e.$set(e.form,"clientContractNo",t)},expression:"form.clientContractNo"}})],1),a("el-form-item",{attrs:{label:"发票号",prop:"clientInvoiceNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"(委托单位)发票号"},model:{value:e.form.clientInvoiceNo,callback:function(t){e.$set(e.form,"clientInvoiceNo",t)},expression:"form.clientInvoiceNo"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"货名概要",prop:"goodsNameSummary"}},[a("el-input",{attrs:{placeholder:"货名概要",disabled:e.psaVerify},model:{value:e.form.goodsNameSummary,callback:function(t){e.$set(e.form,"goodsNameSummary",t)},expression:"form.goodsNameSummary"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"件数",prop:"packageQuantity"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"总件数",disabled:e.psaVerify},model:{value:e.form.packageQuantity,callback:function(t){e.$set(e.form,"packageQuantity",t)},expression:"form.packageQuantity"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"毛重",prop:"grossWeight"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticStyle:{width:"64%"},attrs:{placeholder:"总毛重",disabled:e.psaVerify},nativeOn:{change:function(t){return e.autoCompletion("grossWeight")}},model:{value:e.grossWeight,callback:function(t){e.grossWeight=t},expression:"grossWeight"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{pass:e.form.weightUnitId,type:"unit",placeholder:"重量单位",disabled:e.psaVerify},on:{return:function(t){e.form.weightUnitId=t}}})],1)])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"总体积",prop:"volume"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input-number",{staticStyle:{width:"64%"},attrs:{controls:!1,precision:2,step:.01,placeholder:"总体积",disabled:e.psaVerify},model:{value:e.form.volume,callback:function(t){e.$set(e.form,"volume",t)},expression:"form.volume"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{pass:e.form.volumeUnitId,type:"unit",placeholder:"体积单位",disabled:null!=e.form.volumeUnitId&&!e.psaVerify},on:{return:function(t){e.form.volumeUnitId=t}}})],1)])],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[a("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,placeholder:"货物特征",disabled:e.psaVerify,type:"cargoType"},on:{return:function(t){e.form.cargoTypeIds=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"货值",prop:"goodsValue"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticStyle:{width:"64%"},attrs:{placeholder:"总货值",disabled:e.psaVerify},nativeOn:{change:function(t){return e.autoCompletion("goodsValue")}},model:{value:e.goodsValue,callback:function(t){e.goodsValue=t},expression:"goodsValue"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{pass:e.form.goodsCurrencyId,type:"currency",placeholder:"货值币种",disabled:e.psaVerify},on:{return:function(t){e.form.goodsCurrencyId=t}}})],1)])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"货物限重",prop:"maxWeight"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,step:.01,placeholder:"货物限重",disabled:e.psaVerify},model:{value:e.form.maxWeight,callback:function(t){e.$set(e.form,"maxWeight",t)},expression:"form.maxWeight"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"计费货量",prop:"revenueTons"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"计费货量",disabled:e.psaVerify},model:{value:e.form.revenueTons,callback:function(t){e.$set(e.form,"revenueTons",t)},expression:"form.revenueTons"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"物流类型",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.logisticsTypeId,placeholder:"物流类型",disabled:e.psaVerify,type:"serviceType",main:!0},on:{return:function(t){e.form.logisticsTypeId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[a("location-select",{attrs:{"check-port":e.logisticsType,multiple:!1,"no-parent":!0,"load-options":e.locationOptions,disabled:e.psaVerify,pass:e.form.polId,placeholder:"启运港"},on:{return:function(t){e.form.polId=t}}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[a("location-select",{attrs:{"check-port":e.logisticsType,disabled:e.psaVerify,en:!0,"load-options":e.locationOptions,multiple:!1,pass:e.form.destinationPortId,placeholder:"目的港"},on:{return:function(t){e.form.destinationPortId=t}}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"承运人",prop:"carrierIds"}},[a("treeselect",{attrs:{"disabled-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,disabled:e.psaVerify,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"选择承运人"},on:{deselect:e.handleDeselectCarrierIds,open:e.loadCarrier,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(null!=r.raw.carrier.carrierIntlCode?r.raw.carrier.carrierIntlCode:r.raw.carrier.carrierShortName)+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"船期"}},[a("el-input",{attrs:{placeholder:"内容",disabled:e.psaVerify},model:{value:e.form.schedule,callback:function(t){e.$set(e.form,"schedule",t)},expression:"form.schedule"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"内容"},model:{value:e.form.validTimeForm,callback:function(t){e.$set(e.form,"validTimeForm",t)},expression:"form.validTimeForm"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{staticClass:"spc",attrs:{prop:"isMblNeeded","label-width":"0"}},[a("el-checkbox-button",{staticStyle:{width:"65px"},attrs:{disabled:e.psaVerify,"false-label":"0","true-label":"1",label:"主提单"},model:{value:e.form.isMblNeeded,callback:function(t){e.$set(e.form,"isMblNeeded",t)},expression:"form.isMblNeeded"}},[e._v(" 主提单 ")]),e.form.isMblNeeded?a("el-input",{staticStyle:{"margin-left":"5px",width:"65%"},attrs:{placeholder:"主提单号",disabled:e.psaVerify},model:{value:e.form.mblNo,callback:function(t){e.$set(e.form,"mblNo",t)},expression:"form.mblNo"}}):e._e()],1)],1),e.form.isMblNeeded?a("el-col",{attrs:{span:4}},[a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"套约","true-label":"1"},model:{value:e.form.isUnderAgreementMbl,callback:function(t){e.$set(e.form,"isUnderAgreementMbl",t)},expression:"form.isUnderAgreementMbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"清关中转","true-label":"1"},model:{value:e.form.isCustomsIntransitMbl,callback:function(t){e.$set(e.form,"isCustomsIntransitMbl",t)},expression:"form.isCustomsIntransitMbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0","true-label":"1",label:"转单"},model:{value:e.form.isSwitchMbl,callback:function(t){e.$set(e.form,"isSwitchMbl",t)},expression:"form.isSwitchMbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0","true-label":"1",label:"拆单"},model:{value:e.form.isDividedMbl,callback:function(t){e.$set(e.form,"isDividedMbl",t)},expression:"form.isDividedMbl"}})],1):e._e(),e.form.isMblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"出单方式",prop:"mblIssueTypeId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.mblIssueTypeId,placeholder:"主单出单方式",type:"docIssueType"},on:{return:function(t){e.form.mblIssueTypeId=t}}})],1)],1):e._e(),e.form.isMblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"取单方式",prop:"mblGetWayId"}},[a("tree-select",{attrs:{type:"docReleaseWay",flat:!1,multiple:!1,pass:e.form.mblGetWayId,disabled:e.psaVerify,placeholder:"主单取单方式"},on:{return:function(t){e.form.mblGetWayId=t}}})],1)],1):e._e(),e.form.isMblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"交单方式",prop:"mblReleaseWayId"}},[a("tree-select",{attrs:{type:"docReleaseWay",flat:!1,multiple:!1,disabled:e.psaVerify,pass:e.form.mblReleaseWayId,placeholder:"主单交单方式"},on:{return:function(t){e.form.mblReleaseWayId=t}}})],1)],1):e._e()],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{staticClass:"spc",attrs:{prop:"isHblNeeded","label-width":"0"}},[a("el-checkbox-button",{staticStyle:{width:"65px"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"货代提单","true-label":"1"},model:{value:e.form.isHblNeeded,callback:function(t){e.$set(e.form,"isHblNeeded",t)},expression:"form.isHblNeeded"}},[e._v(" 货代提单 ")]),e.form.isHblNeeded?a("el-input",{staticStyle:{"margin-left":"5px",width:"65%"},attrs:{placeholder:"货代单号(list)",disabled:e.psaVerify},model:{value:e.form.hblNoList,callback:function(t){e.$set(e.form,"hblNoList",t)},expression:"form.hblNoList"}}):e._e()],1)],1),e.form.isHblNeeded?a("el-col",{attrs:{span:4}},[a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"套约","true-label":"1"},model:{value:e.form.isUnderAgreementHbl,callback:function(t){e.$set(e.form,"isUnderAgreementHbl",t)},expression:"form.isUnderAgreementHbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"清关中转","true-label":"1"},model:{value:e.form.isCustomsIntransitHbl,callback:function(t){e.$set(e.form,"isCustomsIntransitHbl",t)},expression:"form.isCustomsIntransitHbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"转单","true-label":"1"},model:{value:e.form.isSwitchHbl,callback:function(t){e.$set(e.form,"isSwitchHbl",t)},expression:"form.isSwitchHbl"}}),a("el-checkbox-button",{staticStyle:{width:"25%"},attrs:{disabled:e.psaVerify,"false-label":"0",label:"拆单","true-label":"1"},model:{value:e.form.isDividedHbl,callback:function(t){e.$set(e.form,"isDividedHbl",t)},expression:"form.isDividedHbl"}})],1):e._e(),e.form.isHblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"出单方式",prop:"hblIssueTypeId"}},[a("tree-select",{attrs:{type:"docIssueType",flat:!1,multiple:!1,disabled:e.psaVerify,pass:e.form.hblIssueTypeId,placeholder:"货代单出单方式"},on:{return:function(t){e.form.hblIssueTypeId=t}}})],1)],1):e._e(),e.form.isHblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"取单方式",prop:"hblGetWayId"}},[a("tree-select",{attrs:{type:"docReleaseWay",flat:!1,multiple:!1,pass:e.form.hblGetWayId,disabled:e.psaVerify,placeholder:"主单取单方式"},on:{return:function(t){e.form.hblGetWayId=t}}})],1)],1):e._e(),e.form.isHblNeeded?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"交单方式",prop:"hblReleaseWayId"}},[a("tree-select",{attrs:{type:"docReleaseWay",flat:!1,multiple:!1,disabled:e.psaVerify,pass:e.form.hblReleaseWayId,placeholder:"货代单交单方式"},on:{return:function(t){e.form.hblReleaseWayId=t}}})],1)],1):e._e()],1),a("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeIds"}},[a("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,disabled:e.psaVerify,placeholder:"服务类型",type:"serviceType"},on:{return:e.getServiceTypeList}})],1)],1),a("el-col",{staticStyle:{width:"24%"}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},["booking"==e.type?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.bookingList,border:""}},[a("el-table-column",{attrs:{width:"25px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini"},on:{click:function(a){return e.getBookingDetail(t.row.bookingId)}}},[e._v("选定 ")])]}}],null,!1,1371707745)}),a("el-table-column",{attrs:{align:"center",label:"订舱单号",prop:"newBookingNo","show-tooltip-when-overflow":"",width:"68px"}}),a("el-table-column",{attrs:{align:"center",label:"客户",prop:"clientName","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.clientName)+" "+e._s(t.row.clientContactor)+" ")]}}],null,!1,3795812394)})],1):e._e(),"op"==e.type?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rctList,border:""}},[a("el-table-column",{attrs:{width:"25px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini"},on:{click:function(a){return e.getRctDetail(t.row.rctId)}}},[e._v("选定 ")])]}}],null,!1,3100952289)}),a("el-table-column",{attrs:{align:"center",label:"操作单号",prop:"rctNo","show-tooltip-when-overflow":"",width:"68px"}}),a("el-table-column",{attrs:{align:"center",label:"客户",prop:"clientName","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.clientName)+" "+e._s(t.row.clientContactor)+" ")]}}],null,!1,3795812394)})],1):e._e(),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.pageNum,limit:e.pageSize,"auto-scroll":!1},on:{"update:page":function(t){e.pageNum=t},"update:limit":function(t){e.pageSize=t},pagination:function(t){"booking"==e.type?e.getBookingList:"op"==e.type&&e.getRctList}}})],1),a("el-row",["booking"==e.type?a("el-button",{attrs:{type:"primary",disabled:e.psaVerify},on:{click:function(t){return e.submitForm("saveCopy")}}},[e._v(" 另存为 ")]):e._e(),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.psaVerify?"确认审核":"保 存"))]),e.psaVerify?a("el-button",{attrs:{type:"warning"},on:{click:e.rejected}},[e._v("驳 回")]):e._e(),a("el-button",{attrs:{disabled:e.psaVerify},on:{click:e.cancel}},[e._v("重 置")])],1)],1)],1),a("div",{staticStyle:{"margin-top":"10px","margin-bottom":"10px"}},[a("el-checkbox-button",{staticStyle:{width:"100px"},attrs:{label:"基础信息",disabled:e.psaVerify},model:{value:e.basicInfo,callback:function(t){e.basicInfo=t},expression:"basicInfo"}}),a("el-checkbox-button",{staticStyle:{width:"100px"},attrs:{disabled:e.psaVerify,label:"应收应付"},model:{value:e.receivablePayable,callback:function(t){e.receivablePayable=t},expression:"receivablePayable"}}),a("el-checkbox-button",{staticStyle:{width:"100px"},attrs:{disabled:"booking"==e.type||e.psaVerify,label:"操作历史记录"},model:{value:e.opHistory,callback:function(t){e.opHistory=t},expression:"opHistory"}}),a("el-checkbox-button",{staticStyle:{width:"100px"},attrs:{label:"编号信息",disabled:"booking"==e.type||e.psaVerify},model:{value:e.noInfo,callback:function(t){e.noInfo=t},expression:"noInfo"}}),a("el-checkbox-button",{staticStyle:{width:"100px"},attrs:{disabled:"booking"==e.type||e.psaVerify,label:"审核信息"},model:{value:e.audit,callback:function(t){e.audit=t},expression:"audit"}})],1),e.list.has("-1")||e.list.has("1")||e.list.has("2")||e.list.has("3")?a("div",[a("div",{staticStyle:{display:"flex","margin-top":"10px","margin-bottom":"10px"}},[a("a",{class:{"el-icon-arrow-down":0==e.logistics,"el-icon-arrow-right":e.logistics}}),a("h3",{staticStyle:{margin:"0"},on:{click:function(t){e.logistics=!e.logistics}}},[e._v("基础物流")]),a("el-button",{staticStyle:{padding:"0","margin-left":"10px"},attrs:{type:"primary",disabled:e.psaVerify},on:{click:e.saveLogistics}},[e._v(" 保存 ")])],1),a("el-row",{staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("div",{staticClass:"show",class:{visible:0==e.logistics,invisible:e.logistics}},[a("el-col",{style:{display:e.basicInfo?"":"none"},attrs:{span:3}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("基础信息")])]),a("div",{class:{inactive:0==e.basicInfo,active:e.basicInfo}},[a("div",{staticClass:"titleStyle"},[a("div",{staticStyle:{width:"92px"}},[e._v("物流要素")]),a("div",{staticStyle:{width:"66%"}},[e._v("具体信息")])]),a("el-form-item",{attrs:{label:"承运人"}},[a("treeselect",{attrs:{"disabled-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!1,"flatten-search-results":!0,multiple:!1,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"选择承运人",disabled:e.psaVerify},on:{open:e.loadCarrier,input:function(t){void 0==t&&(e.carrierId=null)},select:function(t){e.logisticsBasicInfo.carrierId=t.carrier.carrierId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(null!=r.raw.carrier.carrierIntlCode?r.raw.carrier.carrierIntlCode:r.raw.carrier.carrierShortName)+" ")])}},{key:"option-label",fn:function(t){var r=t.node,i=t.shouldShowCount,o=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),i?a("span",{class:l},[e._v("("+e._s(o)+")")]):e._e()])}}],null,!1,1430648288),model:{value:e.carrierId,callback:function(t){e.carrierId=t},expression:"carrierId"}})],1),a("el-form-item",{attrs:{label:"启运港"}},[a("location-select",{attrs:{multiple:!1,pass:e.logisticsBasicInfo.polId,"check-port":e.logisticsType,"load-options":e.locationOptions,placeholder:"启运港",disabled:e.psaVerify},on:{return:function(t){e.logisticsBasicInfo.polId=t}}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程截关",prop:"firstCvClosingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"头程截关"},model:{value:e.logisticsBasicInfo.firstCvClosingTime,callback:function(t){e.$set(e.logisticsBasicInfo,"firstCvClosingTime",t)},expression:"logisticsBasicInfo.firstCvClosingTime"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程开舱",prop:"firstCyOpenTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"头程开舱"},model:{value:e.logisticsBasicInfo.firstCyOpenTime,callback:function(t){e.$set(e.logisticsBasicInfo,"firstCyOpenTime",t)},expression:"logisticsBasicInfo.firstCyOpenTime"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程截重",prop:"firstCyClosingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"头程截重"},model:{value:e.logisticsBasicInfo.firstCyClosingTime,callback:function(t){e.$set(e.logisticsBasicInfo,"firstCyClosingTime",t)},expression:"logisticsBasicInfo.firstCyClosingTime"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程装船",prop:"firstEtd"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"头程装船"},model:{value:e.logisticsBasicInfo.firstEtd,callback:function(t){e.$set(e.logisticsBasicInfo,"firstEtd",t)},expression:"logisticsBasicInfo.firstEtd"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程船名",prop:"firstVessel"}},[a("el-input",{attrs:{placeholder:"船名",disabled:e.psaVerify},model:{value:e.logisticsBasicInfo.firstVessel,callback:function(t){e.$set(e.logisticsBasicInfo,"firstVessel",t)},expression:"logisticsBasicInfo.firstVessel"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"头程航次",prop:"firstVoyage"}},[a("el-input",{attrs:{placeholder:"航次",disabled:e.psaVerify},model:{value:e.logisticsBasicInfo.firstVoyage,callback:function(t){e.$set(e.logisticsBasicInfo,"firstVoyage",t)},expression:"logisticsBasicInfo.firstVoyage"}})],1),a("el-form-item",{attrs:{label:"境内基港",prop:"localBasicPortId"}},[a("location-select",{attrs:{multiple:!1,pass:e.logisticsBasicInfo.localBasicPortId,"check-port":e.logisticsType,disabled:e.psaVerify,"load-options":e.locationOptions,placeholder:"境内基港"},on:{return:function(t){e.logisticsBasicInfo.localBasicPortId=t}}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"基港截关",prop:"basicClosingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"基港截关"},model:{value:e.logisticsBasicInfo.basicClosingTime,callback:function(t){e.$set(e.logisticsBasicInfo,"basicClosingTime",t)},expression:"logisticsBasicInfo.basicClosingTime"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"基港截重",prop:"basicFinalGateinTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"基港截重"},model:{value:e.logisticsBasicInfo.basicFinalGateinTime,callback:function(t){e.$set(e.logisticsBasicInfo,"basicFinalGateinTime",t)},expression:"logisticsBasicInfo.basicFinalGateinTime"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"基港装船",prop:"basicEtd"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"基港装船"},model:{value:e.logisticsBasicInfo.basicEtd,callback:function(t){e.$set(e.logisticsBasicInfo,"basicEtd",t)},expression:"logisticsBasicInfo.basicEtd"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"基港船名",prop:"basicVessel"}},[a("el-input",{attrs:{placeholder:"基港船名",disabled:e.psaVerify},model:{value:e.logisticsBasicInfo.basicVessel,callback:function(t){e.$set(e.logisticsBasicInfo,"basicVessel",t)},expression:"logisticsBasicInfo.basicVessel"}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"基港航次",prop:"basicVoyage"}},[a("el-input",{attrs:{placeholder:"基港航次",disabled:e.psaVerify},model:{value:e.logisticsBasicInfo.basicVoyage,callback:function(t){e.$set(e.logisticsBasicInfo,"basicVoyage",t)},expression:"logisticsBasicInfo.basicVoyage"}})],1),a("el-form-item",{attrs:{label:"中转港",prop:"transitPortId"}},[a("location-select",{attrs:{multiple:!1,pass:e.logisticsBasicInfo.transitPortId,"check-port":e.logisticsType,disabled:e.psaVerify,"load-options":e.locationOptions,placeholder:"中转港"},on:{return:function(t){e.logisticsBasicInfo.transitPortId=t}}})],1),a("el-form-item",{attrs:{label:"卸货港",prop:"podId"}},[a("location-select",{attrs:{multiple:!1,pass:e.logisticsBasicInfo.podId,"check-port":e.logisticsType,"load-options":e.locationOptions,placeholder:"卸货港",disabled:e.psaVerify},on:{return:function(t){e.logisticsBasicInfo.podId=t}}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"卸货港到达",prop:"podEta","label-width":"78px"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"卸货港到达"},model:{value:e.logisticsBasicInfo.podEta,callback:function(t){e.$set(e.logisticsBasicInfo,"podEta",t)},expression:"logisticsBasicInfo.podEta"}})],1),a("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[a("location-select",{attrs:{"check-port":e.logisticsType,"load-options":e.locationOptions,multiple:!1,pass:e.logisticsBasicInfo.destinationPortId,disabled:e.psaVerify,placeholder:"目的港",en:!0},on:{return:function(t){e.logisticsBasicInfo.destinationPortId=t}}})],1),a("el-form-item",{staticClass:"labelRight",attrs:{label:"目的港到达",prop:"destinationPortEta","label-width":"78px"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,clearable:"",placeholder:"目的港到达",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.logisticsBasicInfo.destinationPortEta,callback:function(t){e.$set(e.logisticsBasicInfo,"destinationPortEta",t)},expression:"logisticsBasicInfo.destinationPortEta"}})],1)],1)]),a("el-col",[a("el-row",[a("receivable-payable",{attrs:{"open-receivable-payable":e.receivablePayable,"receivable-payable":e.logisticsReceivablePayableList},on:{return:function(t){e.logisticsReceivablePayableList=t}}})],1),a("el-row",[a("op-history",{attrs:{"basic-info-id":e.logisticsBasicInfo.logisticsTypeInfoId,"op-history":e.logisticsOpHistory,"open-op-history":e.opHistory,"rct-id":e.form.rctId,"type-id":1},on:{return:function(t){e.logisticsOpHistory=t}}})],1),a("el-col",{style:{display:e.noInfo?"":"none"},attrs:{span:4}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("编号信息")])]),a("div",{class:{inactive:0==e.noInfo,active:e.noInfo}},[a("el-table",{attrs:{data:e.showLogisticsNoInfo,border:""}},[a("el-table-column",{attrs:{label:"物流号码",prop:"logisticsNo",width:"92px"}}),a("el-table-column",{attrs:{label:"具体信息",prop:"details"}})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(t){e.openLogisticsNoInfo=!0}}},[e._v("[＋]")])],1),a("audit",{attrs:{audit:e.audit,"basic-info":e.logisticsBasicInfo},on:{return:function(t){e.logisticsBasicInfo=t}}})],1)],1)]),a("logisticsNoInfo",{attrs:{"open-logistics-no-info":e.openLogisticsNoInfo},on:{return:function(t){return e.getNoInfo("logistics",t)},close:function(t){e.openLogisticsNoInfo=!1}}})],1):e._e(),e.list.has("-1")||e.list.has("4")?a("div",[a("div",{staticStyle:{display:"flex","margin-top":"10px","margin-bottom":"10px"}},[a("a",{class:{"el-icon-arrow-down":0==e.preCarriage,"el-icon-arrow-right":e.preCarriage}}),a("h3",{staticStyle:{margin:"0"},on:{click:function(t){e.preCarriage=!e.preCarriage}}},[e._v("前程运输")]),a("el-button",{staticStyle:{padding:"0","margin-left":"10px"},attrs:{type:"primary",disabled:e.psaVerify},on:{click:e.savePreCarriage}},[e._v(" 保存 ")])],1),a("el-row",{staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("div",{staticClass:"show",class:{visible:0==e.preCarriage,invisible:e.preCarriage}},[a("el-col",{style:{display:e.basicInfo?"":"none"},attrs:{span:3}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("基础信息")])]),a("div",{class:{inactive:0==e.basicInfo,active:e.basicInfo}},[a("div",{staticClass:"titleStyle",staticStyle:{"border-top":"0"}},[a("div",{staticStyle:{width:"92px"}},[e._v("物流要素")]),a("div",{staticStyle:{width:"66%"}},[e._v("具体信息")])]),a("el-form-item",{attrs:{label:"装运区域",prop:"preCarriageRegionId"}},[a("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,disabled:e.psaVerify,pass:e.preCarriageBasicInfo.preCarriageRegionId,placeholder:"装运区域"},on:{return:function(t){e.preCarriageBasicInfo.preCarriageRegionId=t}}})],1),a("el-form-item",{attrs:{label:"装运地址",prop:"preCarriageAddress"}},[a("el-input",{attrs:{placeholder:"装运地址",disabled:e.psaVerify},model:{value:e.preCarriageBasicInfo.preCarriageAddress,callback:function(t){e.$set(e.preCarriageBasicInfo,"preCarriageAddress",t)},expression:"preCarriageBasicInfo.preCarriageAddress"}})],1),a("el-form-item",{attrs:{label:"装运时间",prop:"preCarriageTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"装运时间"},model:{value:e.preCarriageBasicInfo.preCarriageTime,callback:function(t){e.$set(e.preCarriageBasicInfo,"preCarriageTime",t)},expression:"preCarriageBasicInfo.preCarriageTime"}})],1),a("el-form-item",{attrs:{label:"装运联系人",prop:"preCarriageContact","label-width":"78px"}},[a("el-input",{attrs:{placeholder:"装运联系人",disabled:e.psaVerify},model:{value:e.preCarriageBasicInfo.preCarriageContact,callback:function(t){e.$set(e.preCarriageBasicInfo,"preCarriageContact",t)},expression:"preCarriageBasicInfo.preCarriageContact"}})],1),a("el-form-item",{attrs:{label:"联系人电话",prop:"preCarriageTel","label-width":"78px"}},[a("el-input",{attrs:{placeholder:"装运联系人电话",disabled:e.psaVerify},model:{value:e.preCarriageBasicInfo.preCarriageTel,callback:function(t){e.$set(e.preCarriageBasicInfo,"preCarriageTel",t)},expression:"preCarriageBasicInfo.preCarriageTel"}})],1),a("el-form-item",{attrs:{label:"装运备注",prop:"preCarriageRemark"}},[a("el-input",{attrs:{placeholder:"装运备注",disabled:e.psaVerify},model:{value:e.preCarriageBasicInfo.preCarriageRemark,callback:function(t){e.$set(e.preCarriageBasicInfo,"preCarriageRemark",t)},expression:"preCarriageBasicInfo.preCarriageRemark"}})],1)],1)]),a("el-col",[a("el-row",[a("receivable-payable",{attrs:{"open-receivable-payable":e.receivablePayable,"receivable-payable":e.preCarriageReceivablePayableList},on:{return:function(t){e.preCarriageReceivablePayableList=t}}})],1),a("el-row",[a("op-history",{attrs:{"basic-info-id":e.preCarriageBasicInfo.preCarriageInfoId,"op-history":e.preCarriageOpHistory,"open-op-history":e.opHistory,"rct-id":e.form.rctId,"type-id":4},on:{return:function(t){e.preCarriageOpHistory=t}}})],1),a("el-col",{style:{display:e.noInfo?"":"none"},attrs:{span:4}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("编号信息")])]),a("div",{class:{inactive:0==e.noInfo,active:e.noInfo}},[a("el-table",{attrs:{data:e.showPreCarriageNoInfo,border:""}},[a("el-table-column",{attrs:{label:"物流号码",prop:"preCarriageNo",width:"92px"}}),a("el-table-column",{attrs:{label:"具体信息",prop:"details"}})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(t){e.openPreCarriageNoInfo=!0}}},[e._v("[＋]")])],1),a("audit",{attrs:{audit:e.audit,"basic-info":e.preCarriageBasicInfo},on:{return:function(t){e.preCarriageBasicInfo=t}}})],1)],1)]),a("PreCarriageNoInfo",{attrs:{"open-pre-carriage-no-info":e.openPreCarriageNoInfo},on:{return:function(t){return e.getNoInfo("preCarriage",t)},close:function(t){e.openPreCarriageNoInfo=!1}}})],1):e._e(),e.list.has("-1")||e.list.has("5")?a("div",[a("div",{staticStyle:{display:"flex","margin-top":"10px","margin-bottom":"10px"}},[a("a",{class:{"el-icon-arrow-down":0==e.exportDeclaration,"el-icon-arrow-right":e.exportDeclaration}}),a("h3",{staticStyle:{margin:"0"},on:{click:function(t){e.exportDeclaration=!e.exportDeclaration}}},[e._v("出口报关")]),a("el-button",{staticStyle:{padding:"0","margin-left":"10px"},attrs:{type:"primary",disabled:e.psaVerify},on:{click:e.saveExportDeclaration}},[e._v("保存 ")])],1),a("el-row",{staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("div",{staticClass:"show",class:{visible:0==e.exportDeclaration,invisible:e.exportDeclaration}},[a("el-col",{style:{display:e.basicInfo?"":"none"},attrs:{span:3}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("基础信息")])]),a("div",{class:{inactive:0==e.basicInfo,active:e.basicInfo}},[a("div",{staticClass:"titleStyle",staticStyle:{"border-top":"0"}},[a("div",{staticStyle:{width:"92px"}},[e._v("物流要素")]),a("div",{staticStyle:{width:"66%"}},[e._v("具体信息")])]),a("el-form-item",{attrs:{label:"派送区域",prop:"dispatchRegionId"}},[a("el-input",{attrs:{placeholder:"派送区域",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchRegionId,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchRegionId",t)},expression:"exportDeclarationBasicInfo.dispatchRegionId"}})],1),a("el-form-item",{attrs:{label:"派送详址",prop:"dispatchAddress"}},[a("el-input",{attrs:{placeholder:"派送详址",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchAddress,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchAddress",t)},expression:"exportDeclarationBasicInfo.dispatchAddress"}})],1),a("el-form-item",{attrs:{label:"派送时间",prop:"dispatchTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date",disabled:e.psaVerify,"value-format":"yyyy-MM-dd",placeholder:"派送时间"},model:{value:e.exportDeclarationBasicInfo.dispatchTime,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchTime",t)},expression:"exportDeclarationBasicInfo.dispatchTime"}})],1),a("el-form-item",{attrs:{label:"派送联系人",prop:"dispatchContact","label-width":"78px"}},[a("el-input",{attrs:{placeholder:"派送联系人",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchContact,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchContact",t)},expression:"exportDeclarationBasicInfo.dispatchContact"}})],1),a("el-form-item",{attrs:{label:"派送电话",prop:"dispatchTel"}},[a("el-input",{attrs:{placeholder:"派送电话",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchTel,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchTel",t)},expression:"exportDeclarationBasicInfo.dispatchTel"}})],1),a("el-form-item",{attrs:{label:"派送备注",prop:"dispatchRemark"}},[a("el-input",{attrs:{placeholder:"派送备注",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchRemark,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchRemark",t)},expression:"exportDeclarationBasicInfo.dispatchRemark"}})],1),a("el-form-item",{attrs:{label:"司机姓名",prop:"dispatchDriverName"}},[a("el-input",{attrs:{placeholder:"(派送)司机姓名",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchDriverName,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchDriverName",t)},expression:"exportDeclarationBasicInfo.dispatchDriverName"}})],1),a("el-form-item",{attrs:{label:"司机电话",prop:"dispatchDriverTel"}},[a("el-input",{attrs:{placeholder:"(派送)司机电话",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchDriverTel,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchDriverTel",t)},expression:"exportDeclarationBasicInfo.dispatchDriverTel"}})],1),a("el-form-item",{attrs:{label:"司机车牌",prop:"dispatchTruckNo"}},[a("el-input",{attrs:{placeholder:"(派送)司机车牌",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchTruckNo,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchTruckNo",t)},expression:"exportDeclarationBasicInfo.dispatchTruckNo"}})],1),a("el-form-item",{attrs:{label:"司机备注",prop:"dispatchTruckRemark"}},[a("el-input",{attrs:{placeholder:"(派送)司机备注",disabled:e.psaVerify},model:{value:e.exportDeclarationBasicInfo.dispatchTruckRemark,callback:function(t){e.$set(e.exportDeclarationBasicInfo,"dispatchTruckRemark",t)},expression:"exportDeclarationBasicInfo.dispatchTruckRemark"}})],1)],1)]),a("el-col",[a("el-row",[a("receivable-payable",{attrs:{"open-receivable-payable":e.receivablePayable,"receivable-payable":e.exportDeclarationReceivablePayableList},on:{return:function(t){e.exportDeclarationReceivablePayableList=t}}})],1),a("el-row",[a("op-history",{attrs:{"basic-info-id":e.exportDeclarationBasicInfo.exportDeclarationId,"op-history":e.exportDeclarationOpHistory,"open-op-history":e.opHistory,"rct-id":e.form.rctId,"type-id":5},on:{return:function(t){e.exportDeclarationOpHistory=t}}})],1),a("el-col",{style:{display:e.noInfo?"":"none"},attrs:{span:4}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("编号信息")])]),a("div",{class:{inactive:0==e.noInfo,active:e.noInfo}},[a("el-table",{attrs:{data:e.showExportDeclarationNoInfo,border:""}},[a("el-table-column",{attrs:{label:"物流号码",prop:"exportDeclarationNo",width:"92px"}}),a("el-table-column",{attrs:{label:"具体信息",prop:"details"}})],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(t){e.openExportDeclarationNoInfo=!0}}},[e._v("[＋]")])],1)]),a("audit",{attrs:{audit:e.audit,"basic-info":e.exportDeclarationBasicInfo},on:{return:function(t){e.exportDeclarationBasicInfo=t}}})],1)],1)])],1):e._e(),e.list.has("-1")||e.list.has("6")?a("div",[a("div",{staticStyle:{display:"flex","margin-top":"10px","margin-bottom":"10px"}},[a("a",{class:{"el-icon-arrow-down":0==e.importClearance,"el-icon-arrow-right":e.importClearance}}),a("h3",{staticStyle:{margin:"0"},on:{click:function(t){e.importClearance=!e.importClearance}}},[e._v("进口清关")]),a("el-button",{staticStyle:{padding:"0","margin-left":"10px"},attrs:{type:"primary",disabled:e.psaVerify},on:{click:e.saveImportClearance}},[e._v("保存 ")])],1),a("el-row",{staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("div",{staticClass:"show",class:{visible:0==e.importClearance,invisible:e.importClearance}},[a("el-col",{style:{display:e.basicInfo?"":"none"},attrs:{span:3}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("基础信息")])]),a("div",{class:{inactive:0==e.basicInfo,active:e.basicInfo}},[a("div",{staticClass:"titleStyle",staticStyle:{"border-top":"0"}},[a("div",{staticStyle:{width:"92px"}},[e._v("物流要素")]),a("div",{staticStyle:{width:"66%"}},[e._v("具体信息")])]),a("el-form-item",{attrs:{label:"报关方式",prop:"exportCustomsTypeId"}},[a("el-input",{attrs:{placeholder:"报关方式",disabled:e.psaVerify},model:{value:e.importClearanceBasicInfo.exportCustomsTypeId,callback:function(t){e.$set(e.importClearanceBasicInfo,"exportCustomsTypeId",t)},expression:"importClearanceBasicInfo.exportCustomsTypeId"}})],1),a("el-form-item",{attrs:{label:"清关方式",prop:"importCustomsTypeId"}},[a("el-input",{attrs:{placeholder:"清关方式",disabled:e.psaVerify},model:{value:e.importClearanceBasicInfo.importCustomsTypeId,callback:function(t){e.$set(e.importClearanceBasicInfo,"importCustomsTypeId",t)},expression:"importClearanceBasicInfo.importCustomsTypeId"}})],1)],1)]),a("el-col",[a("el-row",[a("receivable-payable",{attrs:{"open-receivable-payable":e.receivablePayable,"receivable-payable":e.importClearanceReceivablePayableList},on:{return:function(t){e.importClearanceReceivablePayableList=t}}})],1),a("el-row",[a("op-history",{attrs:{"basic-info-id":e.importClearanceBasicInfo.importClearanceId,"op-history":e.importClearanceOpHistory,"open-op-history":e.opHistory,"rct-id":e.form.rctId,"type-id":6},on:{return:function(t){e.importClearanceOpHistory=t}}})],1),a("el-col",{style:{display:e.noInfo?"":"none"},attrs:{span:4}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("编号信息")])]),a("div",{class:{inactive:0==e.noInfo,active:e.noInfo}},[a("el-table",{attrs:{data:e.showImportClearanceNoInfo,border:""}},[a("el-table-column",{attrs:{label:"物流号码",prop:"importClearanceNo",width:"92px"}}),a("el-table-column",{attrs:{label:"具体信息",prop:"details"}})],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(t){e.openImportPassNoInfo=!0}}},[e._v("[＋]")])],1)]),a("audit",{attrs:{audit:e.audit,"basic-info":e.importClearanceBasicInfo},on:{return:function(t){e.importClearanceBasicInfo=t}}})],1)],1)])],1):e._e(),a("el-row",{staticClass:"spc",staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务报价综述",prop:"quotationSummary","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",maxlength:"150",placeholder:"内容",disabled:e.psaVerify},model:{value:e.form.quotationSummary,callback:function(t){e.$set(e.form,"quotationSummary",t)},expression:"form.quotationSummary"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务订舱备注",prop:"newBookingRemark","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",maxlength:"150",placeholder:"业务订舱备注",disabled:e.psaVerify},model:{value:e.form.newBookingRemark,callback:function(t){e.$set(e.form,"newBookingRemark",t)},expression:"form.newBookingRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务须知",prop:"inquiryNotice","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",maxlength:"150",placeholder:"内容",disabled:e.psaVerify},model:{value:e.form.inquiryNotice,callback:function(t){e.$set(e.form,"inquiryNotice",t)},expression:"form.inquiryNotice"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"商务备注",prop:"inquiryInnerRemark","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",maxlength:"150",placeholder:"内容"},model:{value:e.form.inquiryInnerRemark,callback:function(t){e.$set(e.form,"inquiryInnerRemark",t)},expression:"form.inquiryInnerRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"操作主管备注",prop:"opLeaderRemark","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",maxlength:"150",placeholder:"内容",disabled:e.psaVerify},model:{value:e.form.opLeaderRemark,callback:function(t){e.$set(e.form,"opLeaderRemark",t)},expression:"form.opLeaderRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"操作备注",prop:"opInnerRemark","label-width":"100"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:10,maxRows:20},"show-word-limit":"",disabled:e.psaVerify,maxlength:"150",placeholder:"内容"},model:{value:e.form.opInnerRemark,callback:function(t){e.$set(e.form,"opInnerRemark",t)},expression:"form.opInnerRemark"}})],1)],1)],1),a("div",[a("el-form-item",{attrs:{label:"合约类型",prop:"agreementTypeId"}},[a("el-input",{attrs:{placeholder:"合约类型",disabled:e.psaVerify},model:{value:e.form.agreementTypeId,callback:function(t){e.$set(e.form,"agreementTypeId",t)},expression:"form.agreementTypeId"}})],1),a("el-form-item",{attrs:{label:"合约号",prop:"agreementNo"}},[a("el-input",{attrs:{placeholder:"合约号",disabled:e.psaVerify},model:{value:e.form.agreementNo,callback:function(t){e.$set(e.form,"agreementNo",t)},expression:"form.agreementNo"}})],1)],1)],1)],1)},i=[],o=a("c7eb"),s=a("b85c"),l=a("1da1"),n=(a("d3b7"),a("6062"),a("3ca3"),a("ddb0"),a("159b"),a("14d9"),a("caad"),a("2532"),a("a9e3"),a("ac1f"),a("00b4"),a("5319"),a("99af"),a("25f0"),a("d81d"),a("4de4"),a("06ee")),c=a("4360"),d=a("b0b8"),p=a.n(d),u=a("ca17"),f=a.n(u),m=(a("6f8d"),a("4582")),b=a("64d8"),h=a("34bb"),y=a("cdb8"),g=a("788f"),I=a("20f5"),v=a("5eaa"),C=a("fba1"),w=(a("5666"),a("c2aa")),k={name:"Document",dicts:["sys_yes_no"],props:["type"],components:{PreCarriageNoInfo:b["default"],LogisticsNoInfo:m["default"],opHistory:h["default"],receivablePayable:y["default"],audit:g["default"],Treeselect:f.a},data:function(){return{opList:[],businessList:[],belongList:[],carrierList:[],locationOptions:[],goodsValue:null,grossWeight:null,list:new Set,editOpHistory:{},size:this.$store.state.app.size||"mini",title:"",logisticsType:"1",carrierId:null,carrierIds:[],relationClientIds:[],verifyPsaId:null,salesId:null,salesAssistantId:null,salesObserverId:null,opId:null,bookingOpId:null,docOpId:null,opObserverId:null,openGenerateRct:!1,psaVerify:!1,logistics:!1,basicInfo:!0,noInfo:"booking"!=this.type,opHistory:"booking"!=this.type,receivablePayable:!0,audit:"booking"!=this.type,open:!1,loading:!1,preCarriage:!1,importClearance:!1,exportDeclaration:!1,logisticsProcess:[],logisticsNoInfo:[],showLogisticsNoInfo:[],openLogisticsNoInfo:!1,logisticsOpHistory:[],logisticsReceivablePayableList:[],preCarriageNoInfo:[],showPreCarriageNoInfo:[],openPreCarriageNoInfo:!1,preCarriageOpHistory:[],preCarriageReceivablePayableList:[],openExportDeclarationNoInfo:!1,exportDeclarationNoInfo:[],showExportDeclarationNoInfo:[],exportDeclarationOpHistory:[],exportDeclarationReceivablePayableList:[],openImportPassNoInfo:!1,importClearanceNoInfo:[],showImportClearanceNoInfo:[],importClearanceOpHistory:[],importClearanceReceivablePayableList:[],bookingList:[],rctList:[],form:{},logisticsBasicInfo:{},preCarriageBasicInfo:{},exportDeclarationBasicInfo:{},importClearanceBasicInfo:{},rct:{leadingCharacter:"RCT",month:1,noNum:1,rctNo:null},pageNum:1,pageSize:20,total:0,rules:{},chargeList:[]}},watch:{"form.logisticsTypeId":function(e){var t=this;0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?c["a"].dispatch("getServiceTypeList").then((function(){t.getType(e)})):this.getType(e)}},beforeMount:function(){var e=this;this.reset(),"booking"==this.type&&this.getBookingList().then((function(){e.loadSelection()})),"op"==this.type&&this.getRctList().then((function(){e.loadSelection()})),this.$route.query.id&&"booking"==this.type&&this.getQuotation(this.$route.query.id).then((function(){e.loadSelection()})),this.$route.query.bId&&this.getBookingDetail(this.$route.query.bId).then((function(){e.loadSelection()})),this.$route.query.rId&&this.getRctDetail(this.$route.query.rId).then((function(){e.loadSelection()})),this.$route.query.psaVerify&&(this.psaVerify=!0)},methods:{getQuotation:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(n["c"])(e).then((function(e){if(t.form.logisticsTypeId=e.data.logisticsTypeId,t.form.salesId=e.data.staffId,t.form.clientId=e.data.companyId,t.form.clientRoleId=e.data.companyRoleId,t.form.clientContactor=e.data.extStaffName,t.form.clientContactorTel=e.data.extStaffPhoneNum,t.form.clientContactorEmail=e.data.extStaffEmailEnterprise,t.form.quotationNo=e.data.richNo,t.form.quotationDate=new Date,t.form.impExpTypeId=e.data.imExPort,t.form.goodsNameSummary=e.data.cargoName,t.form.goodsValue=e.data.cargoPrice,t.form.goodsCurrencyId=e.data.cargoCurrencyId,t.form.grossWeight=e.data.grossWeight,t.form.weightUnitId=e.data.cargoUnitId,t.form.polId=e.data.departureId,t.form.destinationPortId=e.data.destinationId,t.form.transitPortId=e.data.transportationTermsId,t.form.revenueTons=e.data.revenueTons,t.form.newBookingRemark=e.data.remark,t.form.inquiryNo=e.data.richNo,void 0!=t.belongList){var a,r=Object(s["a"])(t.belongList);try{for(r.s();!(a=r.n()).done;){var i=a.value;if(void 0!=i.children){var o,l=Object(s["a"])(i.children);try{for(l.s();!(o=l.n()).done;){var n=o.value;if(void 0!=n.children){var c,d=Object(s["a"])(n.children);try{for(d.s();!(c=d.n()).done;){var p=c.value;p.staffId==e.data.staffId&&(t.salesId=p.deptId)}}catch(R){d.e(R)}finally{d.f()}}}}catch(R){l.e(R)}finally{l.f()}}}}catch(R){r.e(R)}finally{r.f()}}var u,f=new Set,m=Object(s["a"])(t.carrierList);try{for(m.s();!(u=m.n()).done;){var b=u.value;if(void 0!=b.children&&b.children.length>0){var h,y=Object(s["a"])(b.children);try{for(y.s();!(h=y.n()).done;){var g=h.value;if(null!=g.carrier&&null!=g.carrier.carrierId&&void 0!=g.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(g.carrier.carrierId)&&f.add(g.serviceTypeId),void 0!=g.children&&g.children.length>0){var I,v=Object(s["a"])(g.children);try{for(v.s();!(I=v.n()).done;){var C=I.value;null!=C.carrier&&null!=C.carrier.carrierId&&void 0!=C.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(C.carrier.carrierId)&&f.add(C.serviceTypeId)}}catch(R){v.e(R)}finally{v.f()}}}}catch(R){y.e(R)}finally{y.f()}}}}catch(R){m.e(R)}finally{m.f()}f.size>0&&f.forEach((function(e){t.carrierIds.push(e)}));var w,k="",x=Object(s["a"])(e.quotationFreight);try{for(x.s();!(w=x.n()).done;){var N=w.value;k+=(null!=N.charge?N.charge+":":"")+((null!=N.quotationCurrency?N.quotationCurrency.toLowerCase():"")+Number(N.quotationRate))+(null!=N.unit?"/"+N.unit:"")+"\n",N.showClient=!1,N.showSupplier=!1,N.showQuotationCharge=!1,N.showCostCharge=!1,N.showQuotationCurrency=!1,N.showCostCurrency=!1,N.showQuotationUnit=!1,N.showCostUnit=!1,N.quotationStrategyId=N.strategyId,N.quotationChargeId=N.chargeId,N.quotationCharge=N.charge,N.costChargeId=N.chargeId,N.inquiryChargeId=N.chargeId,N.costCharge=N.charge,N.inquiryCharge=N.charge,N.quotationUnitId=N.unitId,N.quotationUnitCode=N.unitCode,N.quotationUnit=N.unit,N.costUnitId=N.unitId,N.inquiryUnitCode=N.unit,N.inquiryRate=N.inquiryRate,N.costUnit=N.unit,N.inquiryExchangeRate=N.exchangeRate,N.inquiryCurrencyCode=N.costCurrency,N.quotationExchangeRate=N.exchangeRate,N.inquiryStrategyId=N.strategyId,N.quotationTaxRate=N.taxRate,N.inquiryTaxRate=N.taxRate,N.clientId=e.data.companyId,N.client=e.data.company,t.form.supplierSummary+=N.supplierId+",",N.supplierId=N.companyId,N.supplier=N.company,N.quotationTotal=Number(N.quotationRate)*Number(N.quotationAmount)*Number(N.quotationExchangeRate)*(1+Number(N.quotationTaxRate)/100),N.costTotal=Number(N.inquiryRate)*Number(N.inquiryAmount)*Number(N.inquiryExchangeRate)*(1+Number(N.inquiryTaxRate)/100),"1"!=N.typeId&&"2"!=N.typeId&&"3"!=N.typeId||t.logisticsReceivablePayableList.push(N),"4"==N.typeId&&t.preCarriageReceivablePayableList.push(N),"5"==N.typeId&&t.exportDeclarationReceivablePayableList.push(N)}}catch(R){x.e(R)}finally{x.f()}t.form.quotationSummary=k;var S="";if(e.characteristics){var T,O=Object(s["a"])(e.characteristics);try{for(O.s();!(T=O.n()).done;){var L=T.value;S+=(null!=L.serviceType?L.serviceType:"")+(null!=L.cargoType?L.cargoType:"")+(null!=L.company?L.company:"")+(null!=L.locationDeparture?L.locationDeparture:"")+(null!=L.locationDestination?L.locationDestination:"")+(null!=L.info?L.info:"")+(null!=L.essentialDetail?L.essentialDetail:"")+"\n"}}catch(R){O.e(R)}finally{O.f()}}t.form.inquiryNotice=S,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.locationOptions=e.locationOptions,t.form.preCarriageRegionIds=e.locationLoadingIds,t.form.clientRoleId=e.roleIds[0]}));case 3:case"end":return a.stop()}}),a)})))()},getBookingList:function(){var e=this;return Object(l["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(v["d"])({pageNum:e.pageNum,pageSize:e.pageSize}).then((function(t){""!=t&&(e.bookingList=t.rows,e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getBookingDetail:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(v["c"])(e).then((function(e){var a=[];e.data.relationClientIds&&e.data.relationClientIds.split(",").forEach((function(e){a.push(Number(e))})),t.relationClientIds=a,t.grossWeight=e.data.grossWeight,t.goodsValue=e.data.goodsValue,t.form=e.data,t.form.relationClientIds=a;var r=new Set;if(e.data.carrierIds){var i,o=Object(s["a"])(t.carrierList);try{for(o.s();!(i=o.n()).done;){var l=i.value;if(void 0!=l.children&&l.children.length>0){var n,c=Object(s["a"])(l.children);try{for(c.s();!(n=c.n()).done;){var d=n.value;if(null!=d.carrier&&null!=d.carrier.carrierId&&void 0!=d.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(d.carrier.carrierId)&&r.add(d.serviceTypeId),void 0!=d.children&&d.children.length>0){var p,u=Object(s["a"])(d.children);try{for(u.s();!(p=u.n()).done;){var f=p.value;null!=f.carrier&&null!=f.carrier.carrierId&&void 0!=f.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(f.carrier.carrierId)&&r.add(f.serviceTypeId)}}catch($){u.e($)}finally{u.f()}}}}catch($){c.e($)}finally{c.f()}}}}catch($){o.e($)}finally{o.f()}}if(r.size>0&&r.forEach((function(e){t.carrierIds.push(e)})),void 0!=t.belongList){var m,b=Object(s["a"])(t.belongList);try{for(b.s();!(m=b.n()).done;){var h=m.value;if(void 0!=h.children){var y,g=Object(s["a"])(h.children);try{for(g.s();!(y=g.n()).done;){var I=y.value;if(void 0!=I.children){var v,C=Object(s["a"])(I.children);try{for(C.s();!(v=C.n()).done;){var w=v.value;w.staffId==e.data.salesId&&(t.salesId=w.deptId),w.staffId==e.data.salesAssistantId&&(t.salesAssistantId=w.deptId),w.staffId==e.data.salesObserverId&&(t.salesObserverId=w.deptId)}}catch($){C.e($)}finally{C.f()}}}}catch($){g.e($)}finally{g.f()}}}}catch($){b.e($)}finally{b.f()}}if(void 0!=t.opList){var k,x=Object(s["a"])(t.opList);try{for(x.s();!(k=x.n()).done;){var N=k.value;if(void 0!=N.children){var S,T=Object(s["a"])(N.children);try{for(T.s();!(S=T.n()).done;){var O=S.value;"操作员"==N.role.roleLocalName&&O.staffId==e.data.opId&&(t.opId=O.roleId),"订舱员"==N.role.roleLocalName&&O.staffId==e.data.bookingOpId&&(t.bookingOpId=O.roleId),"单证员"==N.role.roleLocalName&&O.staffId==e.data.docOpId&&(t.docOpId=O.roleId),O.staffId==e.data.opObserverId&&(t.opObserverId=O.roleId)}}catch($){T.e($)}finally{T.f()}}}}catch($){x.e($)}finally{x.f()}}if(void 0!=t.businessList){var L,R=Object(s["a"])(t.businessList);try{for(R.s();!(L=R.n()).done;){var D=L.value;if(void 0!=D.children){var B,_=Object(s["a"])(D.children);try{for(_.s();!(B=_.n()).done;){var P=B.value;P.staffId==e.data.verifyPsaId&&(t.verifyPsaId=P.roleId)}}catch($){_.e($)}finally{_.f()}}}}catch($){R.e($)}finally{R.f()}}null!=e.data.rsBookingLogisticsTypeBasicInfo&&(t.logisticsBasicInfo=e.data.rsBookingLogisticsTypeBasicInfo,t.logisticsReceivablePayableList=e.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingPreCarriageBasicInfo&&(t.preCarriageBasicInfo=e.data.rsBookingPreCarriageBasicInfo,t.preCarriageReceivablePayableList=e.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingExportDeclarationBasicInfo&&(t.exportDeclarationBasicInfo=e.data.rsBookingExportDeclarationBasicInfo,t.exportDeclarationReceivablePayableList=e.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingImportClearanceBasicInfo&&(t.importClearanceBasicInfo=e.data.rsBookingImportClearanceBasicInfo,t.importClearanceReceivablePayableList=e.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList),t.locationOptions=e.locationOptions}));case 3:case"end":return a.stop()}}),a)})))()},getRctList:function(){var e=this;return Object(l["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(I["f"])({pageNum:e.pageNum,pageSize:e.pageSize}).then((function(t){""!=t&&(e.rctList=t.rows,e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getRctDetail:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(I["c"])(e).then((function(e){t.grossWeight=e.data.grossWeight,t.goodsValue=e.data.goodsValue;var a=new Set;if(e.data.carrierIds){var r,i=Object(s["a"])(t.carrierList);try{for(i.s();!(r=i.n()).done;){var o=r.value;if(void 0!=o.children&&o.children.length>0){var l,n=Object(s["a"])(o.children);try{for(n.s();!(l=n.n()).done;){var c=l.value;if(null!=c.carrier&&null!=c.carrier.carrierId&&void 0!=c.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(c.carrier.carrierId)&&a.add(c.serviceTypeId),void 0!=c.children&&c.children.length>0){var d,p=Object(s["a"])(c.children);try{for(p.s();!(d=p.n()).done;){var u=d.value;null!=u.carrier&&null!=u.carrier.carrierId&&void 0!=u.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(u.carrier.carrierId)&&a.add(u.serviceTypeId)}}catch($){p.e($)}finally{p.f()}}}}catch($){n.e($)}finally{n.f()}}}}catch($){i.e($)}finally{i.f()}}a.size>0&&a.forEach((function(e){t.carrierIds.push(e)}));var f=[];if(e.data.relationClientIds&&e.data.relationClientIds.split(",").forEach((function(e){f.push(Number(e))})),t.relationClientIds=f,t.form=e.data,t.form.relationClientIds=f,void 0!=t.belongList){var m,b=Object(s["a"])(t.belongList);try{for(b.s();!(m=b.n()).done;){var h=m.value;if(void 0!=h.children){var y,g=Object(s["a"])(h.children);try{for(g.s();!(y=g.n()).done;){var I=y.value;if(void 0!=I.children){var v,C=Object(s["a"])(I.children);try{for(C.s();!(v=C.n()).done;){var w=v.value;w.staffId==e.data.salesId&&(t.salesId=w.deptId),w.staffId==e.data.salesAssistantId&&(t.salesAssistantId=w.deptId),w.staffId==e.data.salesObserverId&&(t.salesObserverId=w.deptId)}}catch($){C.e($)}finally{C.f()}}}}catch($){g.e($)}finally{g.f()}}}}catch($){b.e($)}finally{b.f()}}if(void 0!=t.opList){var k,x=Object(s["a"])(t.opList);try{for(x.s();!(k=x.n()).done;){var N=k.value;if(void 0!=N.children){var S,T=Object(s["a"])(N.children);try{for(T.s();!(S=T.n()).done;){var O=S.value;"操作员"==N.role.roleLocalName&&O.staffId==e.data.opId&&(t.opId=O.roleId),"订舱员"==N.role.roleLocalName&&O.staffId==e.data.bookingOpId&&(t.bookingOpId=O.roleId),"单证员"==N.role.roleLocalName&&O.staffId==e.data.docOpId&&(t.docOpId=O.roleId),O.staffId==e.data.opObserverId&&(t.opObserverId=O.roleId)}}catch($){T.e($)}finally{T.f()}}}}catch($){x.e($)}finally{x.f()}}if(void 0!=t.businessList){var L,R=Object(s["a"])(t.businessList);try{for(R.s();!(L=R.n()).done;){var D=L.value;if(void 0!=D.children){var B,_=Object(s["a"])(D.children);try{for(_.s();!(B=_.n()).done;){var P=B.value;P.staffId==e.data.verifyPsaId&&(t.verifyPsaId=P.roleId)}}catch($){_.e($)}finally{_.f()}}}}catch($){R.e($)}finally{R.f()}}null!=e.data.rsRctLogisticsTypeBasicInfo&&(t.logisticsBasicInfo=e.data.rsRctLogisticsTypeBasicInfo,t.logisticsReceivablePayableList=e.data.rsRctLogisticsTypeBasicInfo.rsRctReceivablePayableList,t.logisticsOpHistory=e.data.rsRctLogisticsTypeBasicInfo.rsOperationalProcessList),null!=e.data.rsRctPreCarriageBasicInfo&&(t.preCarriageBasicInfo=e.data.rsRctPreCarriageBasicInfo,t.preCarriageReceivablePayableList=e.data.rsRctPreCarriageBasicInfo.rsRctReceivablePayableList,t.preCarriageOpHistory=e.data.rsRctPreCarriageBasicInfo.rsOperationalProcessList),null!=e.data.rsRctExportDeclarationBasicInfo&&(t.exportDeclarationBasicInfo=e.data.rsRctExportDeclarationBasicInfo,t.exportDeclarationReceivablePayableList=e.data.rsRctExportDeclarationBasicInfo.rsRctReceivablePayableList,t.exportDeclarationOpHistory=e.data.rsRctExportDeclarationBasicInfo.rsOperationalProcessList),null!=e.data.rsRctImportClearanceBasicInfo&&(t.importClearanceBasicInfo=e.data.rsRctImportClearanceBasicInfo,t.importClearanceReceivablePayableList=e.data.rsRctImportClearanceBasicInfo.rsRctReceivablePayableList,t.importClearanceOpHistory=e.data.rsRctImportClearanceBasicInfo.rsOperationalProcessList),t.locationOptions=e.locationOptions}));case 3:case"end":return a.stop()}}),a)})))()},getServiceTypeList:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){var r,i,l,n,c,d;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(0!=t.$store.state.data.serviceTypeList.length){a.next=3;break}return a.next=3,t.$store.dispatch("getServiceTypeList");case 3:t.list.clear(),t.form.serviceTypeIds=e,r=Object(s["a"])(t.$store.state.data.serviceTypeList[0].children);try{for(r.s();!(i=r.n()).done;)if(l=i.value,e.includes(l.serviceTypeId)&&null!=l.typeId&&t.list.add(l.typeId),l.children){n=Object(s["a"])(l.children);try{for(n.s();!(c=n.n()).done;)d=c.value,e.includes(d.serviceTypeId)&&(null!=l.typeId&&t.list.add(l.typeId),null!=d.typeId&&t.list.add(d.typeId))}catch(o){n.e(o)}finally{n.f()}}}catch(o){r.e(o)}finally{r.f()}e.includes(-1)&&t.list.add("-1"),t.$forceUpdate();case 9:case"end":return a.stop()}}),a)})))()},getType:function(e){var t,a=Object(s["a"])(this.$store.state.data.serviceTypeList[0].children);try{for(a.s();!(t=a.n()).done;){var r=t.value;if(r.serviceTypeId==e&&(this.logisticsType=r.typeId),r.children){var i,o=Object(s["a"])(r.children);try{for(o.s();!(i=o.n()).done;){var l=i.value;l.serviceTypeId==e&&(this.logisticsType=r.typeId)}}catch(n){o.e(n)}finally{o.f()}}}}catch(n){a.e(n)}finally{a.f()}},getRelationClientIds:function(e){this.form.relationClientIds=e,this.relationClientIds=e},autoCompletion:function(e){var t=/\d{1,3}(?=(\d{3})+$)/g,a=/[0-9]+/g;if("grossWeight"==e)if(a.test(this.grossWeight)){this.grossWeight=this.grossWeight.replace(/\b(0+)/gi,""),this.form.grossWeight=this.grossWeight;var r=this.grossWeight.split("."),i=r[0].replace(t,"$&,");this.grossWeight=r.length>1&&r[1]?"".concat(i,".").concat(r[1]):"".concat(i,".00")}else this.$message.warning("请输入数字");if("goodsValue"==e)if(a.test(this.goodsValue)){this.goodsValue=this.goodsValue.replace(/\b(0+)/gi,""),this.form.goodsValue=this.goodsValue;var o=this.goodsValue.split("."),s=o[0].replace(t,"$&,");this.goodsValue=o.length>1&&o[1]?"".concat(s,".").concat(o[1]):"".concat(s,".00")}else this.$message.warning("请输入数字")},getNoInfo:function(e,t){var a;"logistics"==e&&(this.logisticsNoInfo=t,a=this.showLogisticsNoInfo=[{type:"soNo",logisticsNo:"SO号码",details:""},{type:"mblNo",logisticsNo:"主提单号",details:""},{type:"hblNo",logisticsNo:"货代单号",details:""},{type:"containersInfo",logisticsNo:"柜号信息",details:""},{type:"shipper",logisticsNo:"发货人",details:""},{type:"consignee",logisticsNo:"收货人",details:""},{type:"notifyParty",logisticsNo:"通知人",details:""},{type:"polBookingAgent",logisticsNo:"启运港放舱代理",details:""},{type:"podHandleAgent",logisticsNo:"目的港换单代理",details:""},{type:"shippingMark",logisticsNo:"唛头",details:""},{type:"goodsDescription",logisticsNo:"货描",details:""},{type:"blIssueDate",logisticsNo:"签单日期",details:""},{type:"blIssueLocation",logisticsNo:"签单地点",details:""}]),"preCarriage"==e&&(this.preCarriageNoInfo=t,a=this.showPreCarriageNoInfo=[{type:"soNo",preCarriageNo:"SO号码",details:""},{type:"preCarriageDriverName",preCarriageNo:"司机姓名",details:""},{type:"preCarriageDriverTel",preCarriageNo:"司机电话",details:""},{type:"preCarriageTruckNo",preCarriageNo:"司机车牌",details:""},{type:"preCarriageTruckRemark",preCarriageNo:"司机备注",details:""},{type:"preCarriageAddress",preCarriageNo:"装柜地址",details:""},{type:"preCarriageTime",preCarriageNo:"到场时间",details:""},{type:"containerNo",preCarriageNo:"柜号",details:""},{type:"containerType",preCarriageNo:"柜型",details:""},{type:"sealNo",preCarriageNo:"封条",details:""},{type:"weightPaper",preCarriageNo:"磅单",details:""}]);var r,i=Object(s["a"])(a);try{var o=function(){var e=r.value;t.forEach((function(t){for(var a in t)a==e.type&&(e.details=""==e.details?null!=t[a]?t[a]:"":e.details+(null!=t[a]?","+t[a]:""))}))};for(i.s();!(r=i.n()).done;)o()}catch(l){i.e(l)}finally{i.f()}},submitForm:function(e){var t=this;this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,this.psaVerify&&(this.form.isPsaVerified=1,this.form.processStatusId=2,this.form.psaVerifyTime=Object(C["f"])(new Date)),this.$refs["form"].validate((function(a){if(("op"==t.type||"booking"==t.type&&t.psaVerify)&&("saveCopy"==e&&(t.form.rctId=null),a)){var r={};r.clientId=t.form.clientId,r.clientRoleId=t.form.clientRoleId,r.clientContactor=t.form.clientContactor,r.clientContactorTel=t.form.clientContactorTel,r.clientContactorEmail=t.form.clientContactorEmail,r.emergencyLevel=t.form.urgencyDegree,r.difficultyLevel=null,r.releaseType=t.form.releaseTypeId,r.paymentTitleCode=t.form.paymentTitleCode,r.impExpType=t.form.impExpTypeId,r.tradingTerms=t.form.tradingTermsId,r.logisiticsTerms=t.form.logisiticsTerms,r.tradingPaymentChannel=t.form.tradingPaymentChannel,r.clientContractNo=t.form.clientContractNo,r.clientInvoiceNo=t.form.clientInvoiceNo,r.cargoTypeIdSum=t.form.cargoTypeIds.toString(),r.goodsNameSummary=t.form.goodsNameSummary,r.packageQuantity=t.form.packageQuantity,r.goodsVolume=t.form.volume,r.grossWeight=t.grossWeight,r.weightUnitCode=t.form.weightUnitId,r.goodsCurrencyCode=t.form.goodsCurrencyId,r.goodsValue=t.form.goodsValue,r.logisticsTypeId=t.form.logisticsTypeId,r.revenueTon=t.form.revenueTons,r.polId=t.form.polId,r.localBasicPortId=t.form.localBasicPortId,r.transitPortId=t.form.transitPortId,r.podId=t.form.podId,r.destinationPortId=t.form.destinationPortId,r.cvClosingTime=t.form.cvClosingTime,r.siClosingTime=t.form.siClosingTime,r.firstVessel=t.form.firstVessel,r.firstVoyage=t.form.firstVoyage,r.firstCyOpenTime=t.form.firstCyOpenTime,r.firstCyClosingTime=t.form.firstCyClosingTime,r.firstEtd=t.form.firstEtd,r.basicVessel=t.form.basicVessel,r.basicVoyage=t.form.basicVoyage,r.basicFinalGateinTime=t.form.basicFinalGateinTime,r.basicEtd=t.form.basicEtd,r.podEta=t.form.podEta,r.destinationPortEta=t.form.destinationPortEta,r.inquiryScheduleSummary=t.form.inquiryScheduleSummary,r.polBookingAgent=t.form.polBookingAgent,r.podHandleAgent=t.form.podHandleAgent,r.serviceTypeIdList=t.form.serviceTypeIdList,r.sqdSoNoSum=t.form.sqdSoNoSum,r.sqdMblNoSum=t.form.sqdMblNoSum,r.sqdContainersSealsSum=t.form.sqdContainersSealsSum,r.sqdIssueType=t.form.sqdIssueType,r.sqdExportCustomsType=t.form.sqdExportCustomsType,r.sqdTrailerType=t.form.sqdTrailerType,r.rctProcessStatusSummary=t.form.rctProcessStatusSummary,r.transportStatusSummary=t.form.transportStatusSummary,r.docStatusSummary=t.form.docStatusSummary,r.paymentReceivingStatusSummary=t.form.paymentReceivingStatusSummary,r.paymentPayingStatusSummary=t.form.paymentPayingStatusSummary,r.transportStatus=t.form.transportStatus,r.docStatus=t.form.docStatus,r.paymentPayingStatus=t.form.paymentPayingStatus,r.rctProcessId=t.form.rctProcessId,r.porcessId=t.form.porcessId,r.porcessStatusId=t.form.porcessStatusId,r.processStatusTime=t.form.processStatusTime,r.processRemark=t.form.processRemark,r.qoutationNo=t.form.qoutationNo,r.qoutationSketch=t.form.qoutationSketch,r.salesId=t.form.salesId,r.qoutationTime=t.form.qoutationTime,r.newBookingNo=t.form.newBookingNo,r.newBookingRemark=t.form.newBookingRemark,r.salesAssistantId=t.form.salesAssistantId,r.salesObserverId=t.form.salesObserverId,r.newBookingTime=t.form.newBookingTime,r.inquiryNoticeSum=t.form.inquiryNoticeSum,r.inquiryInnerRemarkSum=t.form.inquiryInnerRemarkSum,r.verifyPsaId=t.form.verifyPsaId,r.psaVerifyTime=t.form.psaVerifyTime,r.opLeaderNotice=t.form.opLeaderNotice,r.opInnerRemark=t.form.opInnerRemark,r.opId=t.form.opId,r.bookingOpId=t.form.bookingOpId,r.docOpId=t.form.docOpId,r.opObserverId=t.form.opObserverId,null!=t.form.rctId?(t.form.processStatusId=3,Object(w["s"])(r).then((function(e){t.saveAll(t.form.rctId),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getRctList()}))):(t.form.processStatusId=1,Object(w["c"])(r).then((function(e){t.form.rctId=e.data,t.saveAll(e.data),t.$modal.msgSuccess("新增成功"),t.open=!1,t.getRctList()})))}"booking"==t.type&&("saveCopy"==e&&(t.form.bookingId=null),a&&(null!=t.form.bookingId?(t.form.processStatusId=3,Object(v["j"])(t.form).then((function(e){t.psaVerify||(t.saveAll(t.form.bookingId),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getBookingList())}))):(t.form.processStatusId=1,Object(v["a"])(t.form).then((function(e){t.form.bookingId=e.data,t.saveAll(e.data),t.$modal.msgSuccess("新增成功"),t.open=!1,t.getBookingList()})))))}))},rejected:function(){var e=this;this.$confirm("确认单据后不可更改，是否确认？","",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",customClass:"modal-confirm"}).then((function(t){"confirm"==t&&(e.form.relationClientIds=null!=e.form.relationClientIds&&e.form.relationClientIds.length>0?e.form.relationClientIds.toString():null,e.$refs["form"].validate((function(t){e.form.isPsaVerified=1,e.form.processStatusId=9,t&&Object(v["j"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getBookingList()}))})))}))},saveAll:function(e){this.saveLogistics(e),this.savePreCarriage(e),this.saveExportDeclaration(e),this.saveImportClearance(e)},saveLogistics:function(e){var t=this;if(null==this.form.bookingId&&null==this.form.rctId)this.$message.error("请先确定单据");else if(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.logisticsReceivablePayableList.length>0&&(this.logisticsBasicInfo.rsBookingReceivablePayableList=this.logisticsReceivablePayableList),this.logisticsBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.logisticsBasicInfo.typeId=1,this.form.rsBookingLogisticsTypeBasicInfo=this.logisticsBasicInfo,Object(v["h"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),"op"==this.type||"booking"==this.type&&this.psaVerify){this.logisticsReceivablePayableList.length>0&&(this.logisticsBasicInfo.rsRctReceivablePayableList=this.logisticsReceivablePayableList),this.logisticsNoInfo.length>0&&(this.logisticsBasicInfo.rsRctLogisticsNoInfos=this.logisticsNoInfo),this.logisticsOpHistory.length>0&&(this.logisticsBasicInfo.rsOperationalProcessList=this.logisticsOpHistory),this.logisticsBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.logisticsBasicInfo.typeId=1,this.form.rsRctLogisticsTypeBasicInfo=this.logisticsBasicInfo,this.form.rsBasicLogistics=this.logisticsBasicInfo;var a={};a.serviceTypeId=this.logisticsBasicInfo.typeId,a.rctId=e,a.rctNo=this.form.rctNo,a.supplierSummary=this.form.supplierSummary,a.inquiryLeatestUpdatedTime=this.form.createTime,a.inquiryNo=this.form.inquiryNo,a.maxWeight=this.form.grossWeight,a.serviceBelongTo="logistics",this.form.rsServiceInstances=a,Object(w["addBasiclogistics"])(this.form).then((function(a){"number"!=typeof e&&t.$message.success("保存成功")}))}},savePreCarriage:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.preCarriageReceivablePayableList.length>0&&(this.preCarriageBasicInfo.rsBookingReceivablePayableList=this.preCarriageReceivablePayableList),this.preCarriageBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.preCarriageBasicInfo.typeId=4,this.form.rsBookingPreCarriageBasicInfo=this.preCarriageBasicInfo,Object(v["i"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.preCarriageReceivablePayableList.length>0&&(this.preCarriageBasicInfo.rsRctReceivablePayableList=this.preCarriageReceivablePayableList),this.preCarriageNoInfo.length>0&&(this.preCarriageBasicInfo.rsRctPreCarriageNoInfos=this.preCarriageNoInfo),this.preCarriageOpHistory.length>0&&(this.preCarriageBasicInfo.rsOperationalProcessList=this.preCarriageOpHistory),this.preCarriageBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.preCarriageBasicInfo.typeId=4,this.form.rsRctPreCarriageBasicInfo=this.preCarriageBasicInfo,Object(I["j"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},saveExportDeclaration:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.exportDeclarationReceivablePayableList.length>0&&(this.exportDeclarationBasicInfo.rsBookingReceivablePayableList=this.exportDeclarationReceivablePayableList),this.exportDeclarationBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.exportDeclarationBasicInfo.typeId=5,this.form.rsBookingExportDeclarationBasicInfo=this.exportDeclarationBasicInfo,Object(v["f"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.exportDeclarationReceivablePayableList.length>0&&(this.exportDeclarationBasicInfo.rsRctReceivablePayableList=this.exportDeclarationReceivablePayableList),this.exportDeclarationOpHistory.length>0&&(this.exportDeclarationBasicInfo.rsOperationalProcessList=this.exportDeclarationOpHistory),this.exportDeclarationBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.exportDeclarationBasicInfo.typeId=5,this.form.rsRctExportDeclarationBasicInfo=this.exportDeclarationBasicInfo,Object(I["g"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},saveImportClearance:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.importClearanceReceivablePayableList.length>0&&(this.importClearanceBasicInfo.rsBookingReceivablePayableList=this.importClearanceReceivablePayableList),this.importClearanceBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.importClearanceBasicInfo.typeId=6,this.form.rsBookingImportClearanceBasicInfo=this.importClearanceBasicInfo,Object(v["g"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.importClearanceReceivablePayableList.length>0&&(this.importClearanceBasicInfo.rsRctReceivablePayableList=this.importClearanceReceivablePayableList),this.importClearanceOpHistory.length>0&&(this.importClearanceBasicInfo.rsOperationalProcessList=this.importClearanceOpHistory),this.importClearanceBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.importClearanceBasicInfo.typeId=6,this.form.rsRctImportClearanceBasicInfo=this.importClearanceBasicInfo,Object(I["h"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},transformToCharge:function(e,t){var a=this;this.logisticsBasicInfo.rsRctReceivablePayableList.map((function(e){var t={};t.serviceId=response.data,t.sqdServiceTypeId=e.typeId,t.sqdRctNo=a.form.rctNo,t.isRecievingOrPaying="0",t.clearingCompanyId=e.clientId,t.clearingCompanySummary="",t.quotationStrategyId=e.quotationStrategyId,t.dnChargeNameId=e.quotationChargeId,t.dnCurrencyCode=e.quotationCurrencyCode,t.dnUnitRate=e.quotationRate,t.dnUnitCode=e.quotationUnitCode,t.dnAmount=e.quotationAmount,t.basicCurrencyRate=e.quotationExchangeRate,t.dutyRate=e.quotationTaxRate,a.chargeList.push(t);var r={};r.serviceId=response.data,r.sqdServiceTypeId=e.typeId,r.sqdRctNo=a.form.rctNo,r.isRecievingOrPaying="1",r.clearingCompanyId=e.supplierId,r.clearingCompanySummary="",r.quotationStrategyId=e.inquiryStrategyId,r.dnChargeNameId=e.inquiryChargeId,r.dnCurrencyCode=e.inquiryCurrencyCode,r.dnUnitRate=e.inquiryRate,r.dnUnitCode=e.inquiryUnitCode,r.dnAmount=e.inquiryAmount,r.basicCurrencyRate=e.inquiryExchangeRate,r.dutyRate=e.inquiryTaxRate,a.chargeList.push(r)}))},loadSelection:function(){(0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType)&&this.$store.dispatch("getServiceTypeList"),(0==this.$store.state.data.clientList.length||this.$store.state.data.redisList.client)&&this.$store.dispatch("getClientList"),(0==this.$store.state.data.supplierList.length||this.$store.state.data.redisList.supplier)&&this.$store.dispatch("getSupplierList"),this.loadOp(),this.loadCarrier(),this.loadSales(),this.loadBusinesses()},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?c["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?c["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?c["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?c["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+p.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+p.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+p.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+p.a.getFullChars(void 0!=e.serviceLocalName?e.serviceLocalName:""):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+p.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},cancel:function(){this.reset(),this.loading=!1,this.open=!1,this.openGenerateRct=!1},reset:function(){this.form={rctId:null,rctNo:null,rctOpDate:null,opId:null,bookingOpId:null,docOpId:null,opObserverId:null,newBookingNo:null,newBookingTime:null,quotationNo:null,quotationDate:null,salesId:null,salesAssistantId:null,salesObserverId:null,verifyPsaId:null,psaVerifyTime:null,urgencyDegree:null,paymentTypeId:null,releaseTypeId:null,processStatusId:null,clientId:null,clientRoleId:null,clientContactor:null,clientContactorTel:null,clientContactorEmail:null,relationClientIds:[],impExpTypeId:null,tradingPaymentChannelId:null,tradingTermsId:null,logisticsTermsId:null,clientContractNo:null,clientInvoiceNo:null,goodsNameSummary:null,packageQuantity:null,grossWeight:null,weightUnitId:null,volume:null,volumeUnitId:null,cargoTypeIds:[],goodsValue:null,goodsCurrencyId:null,maxWeight:null,revenueTons:null,logisticsTypeId:null,polId:null,destinationPortId:null,carrierIds:[],schedule:null,validTimeForm:null,isMblNeeded:0,mblNo:null,isUnderAgreementMbl:0,isCustomsIntransitMbl:0,isSwitchMbl:0,isDividedMbl:0,mblIssueTypeId:null,mblGetWayId:null,mblReleaseWayId:null,isHblNeeded:0,hblNoList:null,isUnderAgreementHbl:0,isCustomsIntransitHbl:0,isSwitchHbl:0,isDividedHbl:0,hblIssueTypeId:null,hblGetWayId:null,hblReleaseWayId:null,serviceTypeIds:[],quotationSummary:null,newBookingRemark:null,inquiryNotice:null,inquiryInnerRemark:null,opLeaderRemark:null,opInnerRemark:null,agreementTypeId:null,agreementNo:null,readOnly:null,createTime:null,updateTime:null,deleteTime:null,deleteStatus:"0",deleteBy:null,updateBy:null,createBy:null,soNo:null,containerNo:null,sealNo:null,bookingDetail:null,precarriageTime:null,cvClosingTime:null,cvDeclaringTime:null,vgm:null,siClosingTime:null,trailer:null,shipName:null,shipTime:null,podETA:null,telexReleaseType:null,isReleasable:null,sendToAgent:null,boatId:null,remark:null,supplierSummary:""},this.logisticsBasicInfo={logisticsTypeInfoId:null,rctId:null,logisticsTypeId:null,carrierId:null,polId:null,firstCvClosingTime:null,firstCyOpenTime:null,firstCyClosingTime:null,firstEtd:null,firstVessel:null,firstVoyage:null,localBasicPortId:null,basicClosingTime:null,basicFinalGateinTime:null,basicEtd:null,basicVessel:null,basicVoyage:null,transitPortId:null,podId:null,podEta:null,destinationPortId:null,destinationPortEta:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.preCarriageBasicInfo={preCarriageInfoId:null,rctId:null,logisticsTypeId:null,preCarriageRegionId:null,preCarriageAddress:null,preCarriageTime:null,preCarriageContact:null,preCarriageTel:null,preCarriageRemark:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.exportDeclarationBasicInfo={exportDeclarationId:null,rctId:null,logisticsTypeId:null,dispatchRegionId:null,dispatchAddress:null,dispatchTime:null,dispatchContact:null,dispatchTel:null,dispatchRemark:null,dispatchDriverName:null,dispatchDriverTel:null,dispatchTruckNo:null,dispatchTruckRemark:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.importClearanceBasicInfo={importClearanceId:null,rctId:null,exportCustomsTypeId:null,importCustomsTypeId:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.grossWeight=0,this.goodsValue=0,this.carrierId=null,this.relationClientIds=[],this.verifyPsaId=null,this.salesId=null,this.salesAssistantId=null,this.salesObserverId=null,this.opId=null,this.bookingOpId=null,this.docOpId=null,this.opObserverId=null,this.carrierIds=[],this.preCarriage=!1,this.importClearance=!1,this.exportDeclaration=!1,this.logisticsProcess=[],this.logisticsNoInfo=[],this.openLogisticsNoInfo=!1,this.logisticsOpHistory=[],this.logisticsReceivablePayableList=[],this.preCarriageNoInfo=[],this.openPreCarriageNoInfo=!1,this.preCarriageOpHistory=[],this.preCarriageReceivablePayableList=[],this.openExportDeclarationNoInfo=!1,this.exportDeclarationNoInfo=[],this.exportDeclarationOpHistory=[],this.exportDeclarationReceivablePayableList=[],this.openImportPassNoInfo=!1,this.importClearanceNoInfo=[],this.importClearanceOpHistory=[],this.importClearanceReceivablePayableList=[],this.chargeList=[],this.resetForm("form")},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},generateRct:function(e){var t=this;e?Object(I["d"])().then((function(e){var a=e.data;if(a.toString().length<4)for(var r=4-a.toString().length,i=0;i<r;i++)a="0"+a;var o=new Date,s=(o.getMonth()+Number(t.rct.month)).toString(),l=(o.getFullYear()+(s/12>1?1:0)).toString().substring(2,4);t.rct.rctNo=t.rct.leadingCharacter+l+(1==s.length?"0"+s:s)+a.toString()})):this.openGenerateRct=!0},confirmRct:function(){this.form.rctNo=this.rct.rctNo,this.openGenerateRct=!1}}},x=k,N=(a("1369"),a("2877")),S=Object(N["a"])(x,r,i,!1,null,"8e65af3e",null);t["default"]=S.exports},c2aa:function(e,t,a){"use strict";a.d(t,"k",(function(){return i})),a.d(t,"j",(function(){return o})),a.d(t,"l",(function(){return s})),a.d(t,"n",(function(){return l})),a.d(t,"m",(function(){return n})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"r",(function(){return p})),a.d(t,"s",(function(){return u})),a.d(t,"d",(function(){return f})),a.d(t,"b",(function(){return m})),a.d(t,"g",(function(){return b})),a.d(t,"f",(function(){return h})),a.d(t,"i",(function(){return y})),a.d(t,"p",(function(){return g})),a.d(t,"q",(function(){return I})),a.d(t,"h",(function(){return v})),a.d(t,"o",(function(){return C}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/rct/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/system/rct/op",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function c(e){return Object(r["a"])({url:"/system/rct/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/system/rct",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/rct",method:"put",data:e})}function f(e){return Object(r["a"])({url:"/system/rct/"+e,method:"delete"})}function m(e){return Object(r["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function b(){return Object(r["a"])({url:"/system/rct/mon",method:"get"})}function h(){return Object(r["a"])({url:"/system/rct/CFmon",method:"get"})}function y(){return Object(r["a"])({url:"/system/rct/RSWHMon",method:"get"})}function g(e){return Object(r["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function I(e){return Object(r["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function v(e){return Object(r["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function C(e){return Object(r["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},cdb8:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.openReceivablePayable?"":"none"},attrs:{span:21.5}},[a("div",{class:{inactive:0==e.openReceivablePayable,active:e.openReceivablePayable}},[a("el-table",{staticClass:"pd0",attrs:{data:e.receivablePayable,"row-class-name":e.rowIndex,border:""}},[a("el-table-column",{attrs:{label:"应收明细"}},[a("el-table-column",{attrs:{label:"客户关联单位"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showClient?e._e():a("div",{on:{click:function(e){t.row.showClient=!0}}},[e._v(" "+e._s(t.row.client)+" ")]),e.$store.state.data.clientList.length>0&&t.row.showClient?a("tree-select",{attrs:{flat:!1,multiple:!1,pass:t.row.clientId,placeholder:"客户",type:"client"},on:{return:function(e){t.row.clientId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"费用",prop:"quotationChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCharge?e._e():a("div",{on:{click:function(e){t.row.showQuotationCharge=!0}}},[e._v(" "+e._s(t.row.quotationCharge)+" ")]),t.row.showQuotationCharge?a("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.quotationChargeId,placeholder:"运费",type:"charge"},on:{return:function(e){t.row.quotationChargeId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"策略",prop:"quotationStrategyId",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(e){1==t.row.quotationStrategyId&&(t.row.quotationRate=0)}},model:{value:t.row.quotationStrategyId,callback:function(a){e.$set(t.row,"quotationStrategyId",a)},expression:"scope.row.quotationStrategyId"}},[a("el-option",{attrs:{label:"已含",value:"1"}},[e._v("已含")]),a("el-option",{attrs:{label:"未含",value:"0"}},[e._v("未含")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"货币",prop:"quotationCurrencyId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCurrency?e._e():a("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(e){t.row.showQuotationCurrency=!0}}},[e._v(" "+e._s(t.row.quotationCurrency)+" ")]),t.row.showQuotationCurrency?a("tree-select",{attrs:{pass:t.row.quotationCurrencyCode,type:"currency"},on:{return:function(e){t.row.currencyId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"单价",prop:"quotationRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationRate,callback:function(a){e.$set(t.row,"quotationRate",a)},expression:"scope.row.quotationRate"}})]}}])}),a("el-table-column",{attrs:{label:"单位",lign:"center",prop:"quotationUnitId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationUnit?e._e():a("div",{on:{click:function(e){t.row.showQuotationUnit=!0}}},[e._v(" "+e._s(t.row.quotationUnitCode)+" ")]),t.row.showQuotationUnit?a("tree-select",{attrs:{pass:t.row.quotationUnitId,type:"unit"},on:{return:function(e){t.row.quotationUnitCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"数量",prop:"quotationAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationAmount,callback:function(a){e.$set(t.row,"quotationAmount",a)},expression:"scope.row.quotationAmount"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"汇率",prop:"quotationExchangeRate",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationExchangeRate,callback:function(a){e.$set(t.row,"quotationExchangeRate",a)},expression:"scope.row.quotationExchangeRate"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"税率",prop:"quotationTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationTaxRate,callback:function(a){e.$set(t.row,"quotationTaxRate",a)},expression:"scope.row.quotationTaxRate"}}),e._v(" % ")]}}])}),a("el-table-column",{attrs:{label:"小计",prop:"quotationTotal",width:"48"}})],1),a("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),a("el-table-column",{attrs:{label:"应付明细"}},[a("el-table-column",{attrs:{label:"供应商关联单位",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showSupplier?e._e():a("div",{on:{click:function(e){t.row.showSupplier=!0}}},[e._v(" "+e._s(t.row.supplier)+" ")]),e.$store.state.data.supplierList.length>0&&t.row.showSupplier?a("tree-select",{attrs:{flat:!1,multiple:!1,pass:t.row.supplierId,placeholder:"供应商",type:"supplier"},on:{return:function(e){t.row.supplierId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"费用",prop:"costChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostCharge?e._e():a("div",{on:{click:function(e){t.row.showCostCharge=!0}}},[e._v(" "+e._s(t.row.costCharge)+" ")]),t.row.showCostCharge?a("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.inquiryChargeId,placeholder:"运费",type:"charge"},on:{return:function(e){t.row.inquiryChargeId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"策略",prop:"costStrategyId",width:"58px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(e){1==t.row.inquiryStrategyId&&(t.row.inquiryRate=0)}},model:{value:t.row.inquiryStrategyId,callback:function(a){e.$set(t.row,"inquiryStrategyId",a)},expression:"scope.row.inquiryStrategyId"}},[a("el-option",{attrs:{label:"已含",value:"1"}},[e._v("已含")]),a("el-option",{attrs:{label:"未含",value:"0"}},[e._v("未含")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"货币",prop:"costCurrencyId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostCurrency?e._e():a("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(e){t.row.showCostCurrency=!0}}},[e._v(" "+e._s(t.row.inquiryCurrencyCode)+" ")]),t.row.showCostCurrency?a("tree-select",{attrs:{pass:t.row.inquiryCurrencyCode,type:"currency"},on:{return:function(e){t.row.inquiryCurrencyCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"单价",prop:"inquiryRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryRate,callback:function(a){e.$set(t.row,"inquiryRate",a)},expression:"scope.row.inquiryRate"}})]}}])}),a("el-table-column",{attrs:{label:"单位",lign:"center",prop:"costUnitId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostUnit?e._e():a("div",{on:{click:function(e){t.row.showCostUnit=!0}}},[e._v(" "+e._s(t.row.inquiryUnitCode)+" ")]),t.row.showCostUnit?a("tree-select",{attrs:{pass:t.row.inquiryUnitCode,type:"unit"},on:{return:function(e){t.row.inquiryUnitCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"数量",prop:"costAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryAmount,callback:function(a){e.$set(t.row,"inquiryAmount",a)},expression:"scope.row.inquiryAmount"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"汇率",prop:"costExchangeRate",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryExchangeRate,callback:function(a){e.$set(t.row,"inquiryExchangeRate",a)},expression:"scope.row.inquiryExchangeRate"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"税率",prop:"costTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryTaxRate,callback:function(a){e.$set(t.row,"inquiryTaxRate",a)},expression:"scope.row.inquiryTaxRate"}}),e._v(" % ")]}}])}),a("el-table-column",{attrs:{label:"小计",prop:"costTotal",width:"48"}})],1),a("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),a("el-table-column",{attrs:{label:"辅助决策"}},[a("el-table-column",{attrs:{align:"center",label:"单项利润",prop:"profit"}})],1),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){e.receivablePayable=e.receivablePayable.filter((function(e){return e!=t.row}))}}},[e._v("删除 ")])]}}])})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addReceivablePayable}},[e._v("[＋] ")])],1)},i=[],o=(a("14d9"),a("b680"),a("a9e3"),{name:"receivablePayable",props:["receivablePayable","openReceivablePayable"],watch:{receivablePayable:function(e){this.$emit("return",e)}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},addReceivablePayable:function(){var e={showClient:!0,showSupplier:!0,showQuotationCharge:!0,showCostCharge:!0,showQuotationCurrency:!0,showCostCurrency:!0,showQuotationUnit:!0,showCostUnit:!0};this.receivablePayable.push(e)},countProfit:function(e){e.profit=(Number(e.quotationRate)*Number(e.quotationAmount)-Number(e.inquiryRate)*Number(e.costAmount)).toFixed(2)}}}),s=o,l=a("2877"),n=Object(l["a"])(s,r,i,!1,null,"2a2d1ebc",null);t["default"]=n.exports},e350:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return o}));a("d3b7"),a("caad"),a("2532");var r=a("4360");function i(e){if(e&&e instanceof Array&&e.length>0){var t=r["a"].getters&&r["a"].getters.permissions,a=e,i="*:*:*",o=t.some((function(e){return i==e||a.includes(e)}));return!!o}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function o(e){if(e&&e instanceof Array&&e.length>0){var t=r["a"].getters&&r["a"].getters.roles,a=e,i="admin",o=t.some((function(e){return i==e||a.includes(e)}));return!!o}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}},fff5:function(e,t,a){"use strict";a.d(t,"i",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"g",(function(){return s})),a.d(t,"a",(function(){return l})),a.d(t,"l",(function(){return n})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"h",(function(){return p})),a.d(t,"j",(function(){return u})),a.d(t,"f",(function(){return f})),a.d(t,"d",(function(){return m})),a.d(t,"m",(function(){return b})),a.d(t,"k",(function(){return h}));var r=a("b775");function i(e){return Object(r["a"])({url:"/system/rscharge/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/rscharge/aggregator",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/rscharge",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/system/rscharge",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"delete"})}function d(e,t){var a={chargeId:e,status:t};return Object(r["a"])({url:"/system/rscharge/changeStatus",method:"put",data:a})}function p(e){return Object(r["a"])({url:"/system/rscharge/charges",method:"get",params:e})}function u(e){return Object(r["a"])({url:"/system/rscharge/selectList",method:"get",params:e})}function f(e){return Object(r["a"])({url:"/system/rscharge/findHedging",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/system/rscharge/writeoff",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/system/rscharge/verify",method:"post",data:e})}function h(e){return Object(r["a"])({url:"/system/rscharge/turnback",method:"post",data:e})}}}]);