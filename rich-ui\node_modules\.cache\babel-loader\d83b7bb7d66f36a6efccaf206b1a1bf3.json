{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeList.vue", "mtime": 1752832104318}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_currency", "_interopRequireDefault", "require", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_index", "_rich", "name", "components", "CompanySelect", "Treeselect", "props", "watch", "chargeData", "handler", "newVal", "oldVal", "$emit", "for<PERSON>ach", "item", "index", "oldItem", "currency", "amount", "exchangeRate", "inverseRate", "precision", "divide", "value", "subtotal", "dnUnitRate", "multiply", "add", "dutyRate", "error", "console", "deep", "immediate", "mounted", "_this", "length", "map", "$refs", "chargeTable", "toggleRowSelection", "computed", "hasConfirmRow", "result", "isAccountConfirmed", "data", "payTotalRMB", "payTotalUSD", "showClientName", "services", "label", "service", "chargeRemark", "methods", "auditStatus", "status", "selectCharge", "target", "row", "dnChargeNameId", "chargeId", "chargeName", "chargeLocalName", "handleSelectionChange", "val", "_this2", "isRecievingOrPaying", "dnCurrencyCode", "getServiceName", "id", "serviceName", "obj", "copyFreight", "companyList", "payClearingCompanyId", "companyId", "payCompanyName", "companyShortName", "_", "cloneDeep", "_objectSpread2", "default", "copyAllFreight", "_this3", "$modal", "alertWarning", "charge", "changeUnitCost", "unit", "dnUnitCode", "$nextTick", "showCostUnit", "changeUnit", "showQuotationUnit", "handleChargeSelect", "showQuotationCharge", "currencyCode", "changeCurrency", "showQuotationCurrency", "rowIndex", "_ref", "addReceivablePayable", "showClient", "showSupplier", "showCostCharge", "showCostCurrency", "showStrategy", "showUnitRate", "showAmount", "showCurrencyRate", "showDutyRate", "basicCurrencyRate", "dnAmount", "isReceivable", "clearingCompanyId", "serviceTypeId", "sqdServiceTypeId", "push", "countProfit", "category", "unitRate", "currencyRate", "sqdDnCurrencyBalance", "$message", "deleteItem", "deleteAllItem", "companyNormalizer", "node", "companyLocalName", "pinyin", "getFullChars", "exports", "_default"], "sources": ["src/views/system/document/chargeList.vue"], "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table ref=\"chargeTable\" :data=\"chargeData\" :row-class-name=\"rowIndex\" border class=\"pd0\"\r\n                @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column v-if=\"isReceivable\" label=\"应收明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"11\">\r\n                <el-row>\r\n                  <el-col :span=\"4\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"11\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(rsClientMessageReceivableTaxUSD, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(rsClientMessageReceivableTaxRMB, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"客户\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showClient\" style=\"width: 50px;height: 20px;\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showClient = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <tree-select v-if=\"(companyList&&companyList.length>0)&&scope.row.showClient\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :multiple=\"false\" :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                           :custom-options=\"companyList\" :flat=\"false\"\r\n                           :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                           @close=\" showClientName==scope.row.companyName ? scope.row.showClient = false:null\"\r\n                           @returnData=\"showClientName=(($event.companyShortName&&$event.companyShortName!=='')?$event.companyShortName:$event.companyEnShortName)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate)\r\n                }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                               :min=\"0.0001\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                               @blur=\"scope.row.showUnitRate=false\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               @focusout.native=\"scope.row.showUnitRate=false\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationUnit\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                           :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                               :controls=\"false\"\r\n                               :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\"\r\n                     :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none;width: 100%;height: 100%;\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"所属服务\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <!--{{ getServiceName(scope.row.sqd_service_type_id) }}-->\r\n                {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n              </div>\r\n              <!-- <el-select v-else v-model=\"scope.row.sqdServiceTypeId\" filterable placeholder=\"所属服务\">\r\n                 <el-option\r\n                   v-for=\"item in services\"\r\n                   :key=\"item.value\"\r\n                   :label=\"item.label\"\r\n                   :value=\"item.value\"\r\n                 >\r\n                 </el-option>\r\n               </el-select>-->\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <!--应付-->\r\n        <el-table-column v-else label=\"应付明细\" width=\"20px\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"4\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">{{ currency(payTotalUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :span=\"12\">{{ currency(payTotalRMB, {separator: \",\", symbol: \"￥\"}).format() }}</el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row>\r\n                  <el-col :span=\"5\">\r\n                    不含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{ currency(payDetailUSD, {separator: \",\", symbol: \"$\"}).format() }}</el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMB, {separator: \",\", symbol: \"￥\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-row class=\"unHighlight-text\">\r\n                  <el-col :span=\"4\">\r\n                    含税小计：\r\n                  </el-col>\r\n                  <el-col :span=\"6\"> USD {{\r\n                      currency(payDetailUSDTax, {separator: \",\", symbol: \"$\"}).format()\r\n                    }}\r\n                  </el-col>\r\n                  <el-col :offset=\"2\" :span=\"6\"> RMB {{\r\n                      currency(payDetailRMBTax, {\r\n                        separator: \",\",\r\n                        symbol: \"￥\"\r\n                      }).format()\r\n                    }}\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <el-table-column\r\n            align=\"center\"\r\n            type=\"selection\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column v-if=\"!hiddenSupplier\" align=\"center\" label=\"供应商\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showSupplier\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showSupplier = true\"\r\n              >\r\n                {{ scope.row.companyName }}\r\n              </div>\r\n              <company-select v-if=\"(companyList && companyList.length>0)&&scope.row.showSupplier\"\r\n                              :class=\"disabled || scope.row.isAccountConfirmed == '1'?'disable-form':''\"\r\n                              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :load-options=\"companyList\"\r\n                              :multiple=\"false\"\r\n                              :no-parent=\"true\"\r\n                              :pass=\"scope.row.clearingCompanyId\"\r\n                              :placeholder=\"'供应商'\"\r\n                              @return=\"scope.row.clearingCompanyId=$event\"\r\n                              @returnData=\"$event.companyShortName==scope.row.companyName?scope.row.showSupplier = false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCharge\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCostCharge = true\"\r\n              >\r\n                {{ scope.row.chargeName }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           @return=\"scope.row.dnChargeNameId = $event\"\r\n                           @returnData=\"$event.chargeLocalName == scope.row.chargeName ? scope.row.showCostCharge=false:null\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.dnCurrencyCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showQuotationCurrency\" :pass=\"scope.row.dnCurrencyCode\"\r\n                           :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\" :type=\"'currency'\"\r\n                           @return=\"changeCurrency(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showUnitRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n              >\r\n                {{\r\n                  scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"¥\"\r\n                  }).format() : scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: \"$\"\r\n                  }).format() : scope.row.dnUnitRate\r\n                }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @blur=\"scope.row.showUnitRate=false\"\r\n                               :precision=\"4\"\r\n                               @change=\"countProfit(scope.row,'unitRate')\"\r\n                               @input=\"countProfit(scope.row,'unitRate')\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单位\" prop=\"costUnitId\" width=\"50px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostUnit\"\r\n                   @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showCostUnit = true\"\r\n              >\r\n                {{ scope.row.dnUnitCode }}\r\n              </div>\r\n              <tree-select v-show=\"scope.row.showCostUnit\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                           :pass=\"scope.row.dnUnitCode\"\r\n                           :type=\"'unit'\" @return=\"changeUnitCost(scope.row,$event)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showAmount\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n              >\r\n                {{ scope.row.dnAmount }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               :min=\"0.00\" @blur=\"scope.row.showAmount=false\"\r\n                               @change=\"countProfit(scope.row,'amount')\"\r\n                               @input=\"countProfit(scope.row,'amount')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <div v-if=\"!scope.row.showCurrencyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n              >\r\n                {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               :min=\"0.0001\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                               @change=\"countProfit(scope.row,'currencyRate')\"\r\n                               @input=\"countProfit(scope.row,'currencyRate')\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex;justify-content: center\">\r\n                <div v-if=\"!scope.row.showDutyRate\"\r\n                     @click=\"(disabled || scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n                >\r\n                  {{ scope.row.dutyRate }}\r\n                </div>\r\n                <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                                 :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                                 :min=\"0\" style=\"width: 75%\"\r\n                                 @blur=\"scope.row.showDutyRate=false\"\r\n                                 @change=\"countProfit(scope.row,'dutyRate')\"\r\n                                 @input=\"countProfit(scope.row,'dutyRate')\"\r\n                />\r\n                <div>%</div>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"小计\" prop=\"costTotal\" width=\"65\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                {{\r\n                  currency(scope.row.subtotal, {\r\n                    separator: \",\",\r\n                    precision: 2,\r\n                    symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                  }).format()\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用备注\">\r\n            <template slot-scope=\"scope\">\r\n              <input v-model=\"scope.row.chargeRemark\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                     style=\"border: none\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核状态\">\r\n            <template slot-scope=\"scope\">\r\n              {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已付金额\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                scope.row.sqdDnCurrencyPaid\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未付余额\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdDnCurrencyBalance }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"生成应收\" prop=\"costTotal\" width=\"65\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyAllFreight()\"\r\n              >生成应收\r\n              </el-button>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"copyFreight(scope.row)\"\r\n              >复制到应收\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n              style=\"color: red\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button style=\"padding: 0\" type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n               :disabled=\"disabled\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\n\r\nexport default {\r\n  name: \"charges\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\",\"debitNote\"],\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => this.$refs.chargeTable.toggleRowSelection(item, true)) : null\r\n\r\n  },\r\n  computed: {\r\n\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({ row, rowIndex }) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n      this.chargeData.push(obj)\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n        // 触发数据更新\r\n        this.$emit(\"return\", this.chargeData)\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAihBA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA,8EACA,qDACA,mGACA,6FACA,oGACA,8GACA;EACAC,KAAA;IACAC,UAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAA,MAAA;UACA,KAAAC,KAAA,WAAAF,MAAA;UACA;QACA;;QAEA;QACAA,MAAA,GAAAA,MAAA,CAAAG,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,OAAA,GAAAL,MAAA,CAAAI,KAAA;;UAEA;UACA,IAAAD,IAAA,CAAAG,QAAA,IAAAH,IAAA,CAAAI,MAAA;YACA;YACA,IAAAF,OAAA,IAAAA,OAAA,CAAAC,QAAA,cAAAH,IAAA,CAAAG,QAAA;cACA,IAAAH,IAAA,CAAAK,YAAA,IAAAL,IAAA,CAAAK,YAAA;gBACA;kBACA;kBACA,IAAAC,WAAA,OAAAH,iBAAA;oBAAAI,SAAA;kBAAA,GAAAC,MAAA,CAAAR,IAAA,CAAAK,YAAA,EAAAI,KAAA;;kBAEA;kBACAT,IAAA,CAAAU,QAAA,OAAAP,iBAAA,EAAAH,IAAA,CAAAW,UAAA;oBAAAJ,SAAA;kBAAA,GACAK,QAAA,CAAAZ,IAAA,CAAAI,MAAA,EACAQ,QAAA,CAAAN,WAAA,EACAM,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAH,IAAA,CAAAc,QAAA,OAAAN,MAAA,QACAC,KAAA;gBACA,SAAAM,KAAA;kBACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;kBACAf,IAAA,CAAAU,QAAA;gBACA;cACA;YACA;UACA;QACA;QAEA,KAAAZ,KAAA,WAAAF,MAAA,GAAAA,MAAA;MACA;MACAqB,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAA1B,UAAA,SAAAA,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,CAAA4B,GAAA,WAAAtB,IAAA;MAAA,OAAAoB,KAAA,CAAAG,KAAA,CAAAC,WAAA,CAAAC,kBAAA,CAAAzB,IAAA;IAAA;EAEA;EACA0B,QAAA;IAEAC,aAAA,WAAAA,cAAA;MACA,IAAAC,MAAA;MACA,KAAAlC,UAAA,SAAAA,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,CAAA4B,GAAA,WAAAtB,IAAA;QACA,IAAAA,IAAA,CAAA6B,kBAAA;UACAD,MAAA;QACA;MACA;MACA,OAAAA,MAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,QAAA;QACAzB,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;MACAC,OAAA;QACA3B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QACA1B,KAAA;QACA0B,KAAA;MACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,GACA;QAAA1B,KAAA;QAAA0B,KAAA;MAAA,EACA;MACAE,YAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,MAAA;MACA,OAAAA,MAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,MAAA,EAAAC,GAAA;MACAA,GAAA,CAAAC,cAAA,GAAAF,MAAA,CAAAG,QAAA;MACAF,GAAA,CAAAG,UAAA,GAAAJ,MAAA,CAAAK,eAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAApD,KAAA,cAAAmD,GAAA;MAEA,KAAAjB,WAAA;MACA,KAAAD,WAAA;MACAkB,GAAA,GAAAA,GAAA,CAAA3B,GAAA,WAAAtB,IAAA;QACA,IAAAA,IAAA,CAAAmD,mBAAA;UACA,IAAAnD,IAAA,CAAAoD,cAAA;YACAF,MAAA,CAAAlB,WAAA,OAAA7B,iBAAA,EAAA+C,MAAA,CAAAlB,WAAA,EAAAnB,GAAA,CAAAb,IAAA,CAAAU,QAAA;UACA;YACAwC,MAAA,CAAAnB,WAAA,OAAA5B,iBAAA,EAAA+C,MAAA,CAAAnB,WAAA,EAAAlB,GAAA,CAAAb,IAAA,CAAAU,QAAA;UACA;QAEA;MACA;IAEA;IACAP,QAAA,EAAAA,iBAAA;IACAkD,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAC,WAAA;MACA,KAAArB,QAAA,CAAAZ,GAAA,WAAAkC,GAAA;QACAA,GAAA,CAAA/C,KAAA,KAAA6C,EAAA,GAAAC,WAAA,GAAAC,GAAA,CAAArB,KAAA;MACA;MACA,OAAAoB,WAAA;IACA;IACAE,WAAA,WAAAA,YAAAd,GAAA;MACA,SAAAe,WAAA,CAAArC,MAAA;QACAsB,GAAA,CAAAgB,oBAAA,QAAAD,WAAA,IAAAE,SAAA;QACAjB,GAAA,CAAAkB,cAAA,QAAAH,WAAA,IAAAI,gBAAA;MACA;MACAnB,GAAA,CAAAd,kBAAA;MACA;MACA,IAAAC,IAAA,QAAAiC,CAAA,CAAAC,SAAA,CAAArB,GAAA;MAEA,KAAA7C,KAAA,oBAAAmE,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAApC,IAAA;QAAAe,QAAA;MAAA;IACA;IACAsB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAAV,WAAA,CAAArC,MAAA;QACA,KAAAgD,MAAA,CAAAC,YAAA;QACA;MACA;MAEA,KAAA5E,UAAA,CAAA4B,GAAA,WAAAiD,MAAA;QACAA,MAAA,CAAAZ,oBAAA,GAAAS,MAAA,CAAAV,WAAA,IAAAE,SAAA;QACAW,MAAA,CAAAV,cAAA,GAAAO,MAAA,CAAAV,WAAA,IAAAI,gBAAA;QACAS,MAAA,CAAApB,mBAAA;QACAoB,MAAA,CAAA1C,kBAAA;QACA0C,MAAA,CAAA1B,QAAA;QACAuB,MAAA,CAAAtE,KAAA,gBAAAsE,MAAA,CAAAL,CAAA,CAAAC,SAAA,CAAAO,MAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA7B,GAAA,EAAA8B,IAAA;MACA9B,GAAA,CAAA+B,UAAA,GAAAD,IAAA;MACA,KAAAE,SAAA;QACAhC,GAAA,CAAAiC,YAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAlC,GAAA,EAAA8B,IAAA;MACA9B,GAAA,CAAA+B,UAAA,GAAAD,IAAA;MACA,KAAAE,SAAA;QACAhC,GAAA,CAAAmC,iBAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAApC,GAAA,EAAAb,IAAA;MACA,IAAAa,GAAA,CAAAI,eAAA,KAAAjB,IAAA,CAAAgB,UAAA;QACAH,GAAA,CAAAG,UAAA,GAAAhB,IAAA,CAAAiB,eAAA;QACAJ,GAAA,CAAAqC,mBAAA;MACA;MACA,IAAArC,GAAA,CAAAsC,YAAA,YAAAnD,IAAA,CAAAmD,YAAA;QACAtC,GAAA,CAAAS,cAAA,GAAAtB,IAAA,CAAAmD,YAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAvC,GAAA,EAAAsC,YAAA;MACAtC,GAAA,CAAAS,cAAA,GAAA6B,YAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA,KAAAN,SAAA;QACA;QACAhC,GAAA,CAAAwC,qBAAA;MACA;IACA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAA1C,GAAA,GAAA0C,IAAA,CAAA1C,GAAA;QAAAyC,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAzC,GAAA,CAAAW,EAAA,GAAA8B,QAAA;IACA;IACAE,oBAAA,WAAAA,qBAAA;MACA,IAAA9B,GAAA;QACA+B,UAAA;QACAC,YAAA;QACAR,mBAAA;QACAS,cAAA;QACAN,qBAAA;QACAO,gBAAA;QACAZ,iBAAA;QACAF,YAAA;QACAe,YAAA;QACAC,YAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,iBAAA;QACAlF,QAAA;QACAmF,QAAA;QACA;QACA9C,mBAAA,OAAA+C,YAAA;QACAC,iBAAA,OAAAzG,UAAA,CAAA2B,MAAA,YAAA3B,UAAA,MAAAA,UAAA,CAAA2B,MAAA,MAAA8E,iBAAA;MACA;MACA,SAAAC,aAAA,QAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,SAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,UAAA5C,GAAA,CAAA6C,gBAAA;MACA,SAAAD,aAAA,UAAA5C,GAAA,CAAA6C,gBAAA;MACA,KAAA3G,UAAA,CAAA4G,IAAA,CAAA9C,GAAA;IACA;IACA+C,WAAA,WAAAA,YAAA5D,GAAA,EAAA6D,QAAA;MACA;MACA,KAAA7D,GAAA;;MAEA;MACA,IAAA8D,QAAA,GAAA9D,GAAA,CAAAhC,UAAA;MACA,IAAAP,MAAA,GAAAuC,GAAA,CAAAsD,QAAA;MACA,IAAAS,YAAA,OAAAvG,iBAAA,EAAAwC,GAAA,CAAAqD,iBAAA;QAAAzF,SAAA;MAAA,GAAAE,KAAA;MACA,IAAAK,QAAA,OAAAX,iBAAA,EAAAwC,GAAA,CAAA7B,QAAA,OAAAL,KAAA;MAEA;QACA;QACA,IAAAC,QAAA,OAAAP,iBAAA,EAAAsG,QAAA;UAAAlG,SAAA;QAAA,GACAK,QAAA,CAAAR,MAAA,EACAQ,QAAA,CAAA8F,YAAA,EACA9F,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAW,QAAA,EAAAN,MAAA,QACAC,KAAA;;QAEA;QACAkC,GAAA,CAAAjC,QAAA,OAAAP,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA;QACAkC,GAAA,CAAAgE,oBAAA,GAAAhE,GAAA,CAAAd,kBAAA,eAAA1B,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA,GAAAkC,GAAA,CAAAgE,oBAAA;;QAEA;QACA,QAAAH,QAAA;UACA;YACA7D,GAAA,CAAAgD,YAAA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;QACA;;QAEA;QACA,KAAA7F,KAAA,gBAAAJ,UAAA;MAEA,SAAAqB,KAAA;QACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;QACA,KAAA6F,QAAA,CAAA7F,KAAA;MACA;IACA;IACA8F,UAAA,WAAAA,WAAAlE,GAAA;MACA,KAAA7C,KAAA,eAAA6C,GAAA;IACA;IACAmE,aAAA,WAAAA,cAAAnE,GAAA;MACA,KAAA7C,KAAA;IACA;IACAiH,iBAAA,WAAAA,kBAAAC,IAAA;MACA;QACA1D,EAAA,EAAA0D,IAAA,CAAApD,SAAA;QACAzB,KAAA,GAAA6E,IAAA,CAAAlD,gBAAA,WAAAkD,IAAA,CAAAlD,gBAAA,gBAAAkD,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA,eAAAC,iBAAA,CAAAC,YAAA,EAAAH,IAAA,CAAAlD,gBAAA,WAAAkD,IAAA,CAAAlD,gBAAA,gBAAAkD,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA;MACA;IACA;EACA;AACA;AAAAG,OAAA,CAAAlD,OAAA,GAAAmD,QAAA"}]}