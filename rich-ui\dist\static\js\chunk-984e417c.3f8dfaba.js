(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-984e417c","chunk-2d0d69a4"],{"242e":function(t,e,r){"use strict";r.r(e);r("d3b7"),r("159b"),r("14d9"),r("99af");var n=r("72f9"),a=r.n(n),i=r("2ef0"),s=r.n(i);e["default"]={data:function(){return{defaultChargeTemplate:{chargeOrderNum:0,chargeTypeOrderNum:0,sqdServiceTypeId:null,typeId:null,inquiryAmount:1,quotationAmount:1,dnAmount:1,dnUnitCode:null,dnUnitRate:0,costCurrencyCode:null,costCurrency:null,quotationRate:0,charge:"",chargeName:"",chargeEn:"",freightId:null,localChargeId:null,profit:0,taxRate:0,quotationCurrencyCode:null,quotationCurrency:null,dnCurrencyCode:null,clearingCompanyId:null,companyName:"",dnChargeNameId:null,dutyRate:0,basicCurrencyRate:1,subtotal:0,sqdDnCurrencyBalance:0,showAmount:!1,showClient:!1,showCostCharge:!1,showCostCurrency:!1,showCostUnit:!1,showCurrencyRate:!1,showDutyRate:!1,showQuotationCharge:!1,showQuotationCurrency:!1,showQuotationUnit:!1,showStrategy:!0,showSupplier:!1,showUnitRate:!1}}},methods:{resetCharge:function(){this.rsCharge=s.a.cloneDeep(this.defaultChargeTemplate)},calculateSubtotal:function(t){if(!t)return 0;var e=a()(t.dnUnitRate||0).multiply(t.dnAmount||0);if(t.dutyRate){var r=e.multiply(a()(t.dutyRate).divide(100));e=e.add(r)}return e.value},convertCurrency:function(t,e,r,n){if(e===r)return t;var i=n||("USD"===e&&"RMB"===r?6.5:.15);return a()(t).multiply(i).value},calculateChargeTotal:function(t){var e=this;if(!t||!t.length)return{RMB:0,USD:0,totalRMB:0};var r={RMB:0,USD:0,totalRMB:0};return t.forEach((function(t){var n=e.calculateSubtotal(t);if("RMB"===t.dnCurrencyCode)r.RMB=a()(r.RMB).add(n).value,r.totalRMB=a()(r.totalRMB).add(n).value;else if("USD"===t.dnCurrencyCode&&(r.USD=a()(r.USD).add(n).value,t.basicCurrencyRate)){var i=a()(n).multiply(t.basicCurrencyRate).value;r.totalRMB=a()(r.totalRMB).add(i).value}})),r},calculateProfit:function(t,e){var r=this.calculateChargeTotal(t),n=this.calculateChargeTotal(e);return{RMB:a()(r.RMB).subtract(n.RMB).value,USD:a()(r.USD).subtract(n.USD).value,totalRMB:a()(r.totalRMB).subtract(n.totalRMB).value}},addChargeToService:function(t,e){t&&(t.rsChargeList||(t.rsChargeList=[]),e.subtotal=this.calculateSubtotal(e),t.rsChargeList.push(s.a.cloneDeep(e)))},collectAllCharges:function(){var t=[];this.rsClientMessage&&this.rsClientMessage.rsChargeList&&(t=t.concat(this.rsClientMessage.rsChargeList));var e=[this.form.rsOpSeaFclList,this.form.rsOpSeaLclList,this.form.rsOpAirList,this.form.rsOpCtnrTruckList,this.form.rsOpBulkTruckList,this.form.rsOpDocDeclareList,this.form.rsOpFreeDeclareList];return e.forEach((function(e){e&&e.length&&e.forEach((function(e){e.rsChargeList&&e.rsChargeList.length&&(t=t.concat(e.rsChargeList))}))})),t}}}},"72f9":function(t,e,r){(function(e,r){t.exports=r()})(0,(function(){function t(i,s){if(!(this instanceof t))return new t(i,s);s=Object.assign({},r,s);var u=Math.pow(10,s.precision);this.intValue=i=e(i,s),this.value=i/u,s.increment=s.increment||1/u,s.groups=s.useVedic?a:n,this.s=s,this.p=u}function e(e,r){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],a=r.decimal,i=r.errorOnInvalid,s=r.fromCents,u=Math.pow(10,r.precision),o=e instanceof t;if(o&&s)return e.intValue;if("number"===typeof e||o)a=o?e.value:e;else if("string"===typeof e)i=new RegExp("[^-\\d"+a+"]","g"),a=new RegExp("\\"+a,"g"),a=(a=e.replace(/\((.*)\)/,"-$1").replace(i,"").replace(a,"."))||0;else{if(i)throw Error("Invalid Input");a=0}return s||(a=(a*u).toFixed(4)),n?Math.round(a):a}var r={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(t,e){var r=e.pattern,n=e.negativePattern,a=e.symbol,i=e.separator,s=e.decimal;e=e.groups;var u=(""+t).replace(/^-/,"").split("."),o=u[0];return u=u[1],(0<=t.value?r:n).replace("!",a).replace("#",o.replace(e,"$1"+i)+(u?s+u:""))},fromCents:!1},n=/(\d)(?=(\d{3})+\b)/g,a=/(\d)(?=(\d\d)+\d\b)/g;return t.prototype={add:function(r){var n=this.s,a=this.p;return t((this.intValue+e(r,n))/(n.fromCents?1:a),n)},subtract:function(r){var n=this.s,a=this.p;return t((this.intValue-e(r,n))/(n.fromCents?1:a),n)},multiply:function(e){var r=this.s;return t(this.intValue*e/(r.fromCents?1:Math.pow(10,r.precision)),r)},divide:function(r){var n=this.s;return t(this.intValue/e(r,n,!1),n)},distribute:function(e){var r=this.intValue,n=this.p,a=this.s,i=[],s=Math[0<=r?"floor":"ceil"](r/e),u=Math.abs(r-s*e);for(n=a.fromCents?1:n;0!==e;e--){var o=t(s/n,a);0<u--&&(o=o[0<=r?"add":"subtract"](1/n)),i.push(o)}return i},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(t){var e=this.s;return"function"===typeof t?t(this,e):e.format(this,Object.assign({},e,t))},toString:function(){var t=this.s,e=t.increment;return(Math.round(this.intValue/this.p/e)*e).toFixed(t.precision)},toJSON:function(){return this.value}},t}))}}]);