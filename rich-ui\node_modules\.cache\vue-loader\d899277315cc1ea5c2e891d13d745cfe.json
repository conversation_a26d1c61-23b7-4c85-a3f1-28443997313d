{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue", "mtime": 1752832104318}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgQ2hhcmdlcyBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9jaGFyZ2VMaXN0LnZ1ZSINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiZGViaXROb3RlTGlzdCIsDQogIGNvbXBvbmVudHM6IHtDaGFyZ2VzfSwNCiAgcHJvcHM6IFsNCiAgICAiY29tcGFueUxpc3QiLA0KICAgICJkaXNhYmxlZCIsDQogICAgImhpZGRlblN1cHBsaWVyIiwNCiAgICAicmN0SWQiLA0KICAgICJkZWJpdE5vdGVMaXN0IiwNCiAgICAiaXNSZWNlaXZhYmxlIg0KICBdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGV4cGFuZGVkUm93czogW10sDQogICAgICBsb2NhbERlYml0Tm90ZUxpc3Q6IFtdDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIGRlYml0Tm90ZUxpc3Q6IHsNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTpkZWJpdE5vdGVMaXN0JywgbmV3VmFsKQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFwcGx5VW5sb2NrKHJvdykgew0KICAgICAgdGhpcy4kZW1pdCgiYXBwbHlVbmxvY2siLCByb3cpDQogICAgfSwNCiAgICBzZXRDb21wbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJGVtaXQoInNldENvbXBsZXRlIiwgcm93KQ0KICAgIH0sDQogICAgY2hhbmdlQ3VycmVuY3kocm93LCBjdXJyZW5jeSkgew0KICAgICAgcm93LmRuQ3VycmVuY3lDb2RlID0gY3VycmVuY3kNCiAgICB9LA0KICAgIHNlbGVjdEJhbmtBY2NvdW50KHJvdywgYmFua0FjY291bnQpIHsNCiAgICAgIHJvdy5iYW5rQWNjb3VudENvZGUgPSBiYW5rQWNjb3VudC5iYW5rQWNjb3VudENvZGUNCiAgICAgIHJvdy5iYW5rQWNjb3VudE5hbWUgPSBiYW5rQWNjb3VudC5iYW5rQWNjb3VudE5hbWUNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdENvbXBhbnkocm93LCBjb21wYW55KSB7DQogICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSBjb21wYW55LmNvbXBhbnlJZA0KICAgICAgcm93LmNsZWFyaW5nQ29tcGFueU5hbWUgPSBjb21wYW55LmNvbXBhbnlTaG9ydE5hbWUNCiAgICB9LA0KICAgIGFkZERlYml0Tm90ZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoImFkZERlYml0Tm90ZSIpDQogICAgfSwNCiAgICBjdXJyZW5jeSwNCg0KICAgIC8vIOWxleW8gC/mlLbotbfooYwNCiAgICBoYW5kbGVFeHBhbmRDaGFuZ2Uocm93LCBleHBhbmRlZFJvd3MpIHsNCiAgICAgIHRoaXMuZXhwYW5kZWRSb3dzID0gZXhwYW5kZWRSb3dzDQogICAgfSwNCg0KICAgIC8vIOWIm+W7uuWIhui0puWNlQ0KICAgIGFzeW5jIGNyZWF0ZURlYml0Tm90ZShyb3cpIHsNCiAgICAgIHRyeSB7DQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIm+W7uuWIhui0puWNleWksei0pToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yib5bu65YiG6LSm5Y2V5aSx6LSlIikNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g57yW6L6R5YiG6LSm5Y2VDQogICAgZWRpdERlYml0Tm90ZShyb3cpIHsNCiAgICAgIHRoaXMuJGVtaXQoImVkaXREZWJpdE5vdGUiLCByb3cpDQogICAgfSwNCg0KICAgIC8vIOWIoOmZpOWIhui0puWNlQ0KICAgIGFzeW5jIGRlbGV0ZURlYml0Tm90ZShyb3cpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oIuehruWumuimgeWIoOmZpOivpeWIhui0puWNleWQl++8nyIsICLmj5DnpLoiLCB7DQogICAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICAgIH0pDQogICAgICAgIHRoaXMuJGVtaXQoImRlbGV0ZURlYml0Tm90ZSIsIHJvdykNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGlmIChlcnJvciAhPT0gImNhbmNlbCIpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLliKDpmaTliIbotKbljZXlpLHotKU6IiwgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yig6Zmk5YiG6LSm5Y2V5aSx6LSlIikNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbotLnnlKjmlbDmja7lj5jljJYNCiAgICBoYW5kbGVDaGFyZ2VEYXRhQ2hhbmdlKHJvdywgY2hhcmdlRGF0YSkgew0KICAgICAgbGV0IGJpbGxSZWNlaXZhYmxlID0gMA0KICAgICAgbGV0IGJpbGxQYXlhYmxlID0gMA0KDQogICAgICAvLyDnu5/orqFjaGFyZ2VEYXRh55qE6LS555SoDQogICAgICBpZiAodGhpcy5pc1JlY2VpdmFibGUpIHsNCiAgICAgICAgLy8g5bqU5pS2DQogICAgICAgIGNoYXJnZURhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAvLyDkvb/nlKhjdXJyZW5jeS5qc+iuoeeulw0KICAgICAgICAgIGJpbGxSZWNlaXZhYmxlID0gY3VycmVuY3koYmlsbFJlY2VpdmFibGUpLmFkZChpdGVtLnN1YnRvdGFsKS50b1N0cmluZygpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlupTku5gNCiAgICAgICAgY2hhcmdlRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGJpbGxQYXlhYmxlID0gY3VycmVuY3koYmlsbFBheWFibGUpLmFkZChpdGVtLnN1YnRvdGFsKS50b1N0cmluZygpDQogICAgICAgIH0pDQogICAgICB9DQogICAgICByb3cuYmlsbFJlY2VpdmFibGUgPSBiaWxsUmVjZWl2YWJsZQ0KICAgICAgcm93LmJpbGxQYXlhYmxlID0gYmlsbFBheWFibGUNCiAgICB9LA0KDQogICAgLy8g5aSE55CG6LS555So6YCJ5oupDQogICAgaGFuZGxlQ2hhcmdlU2VsZWN0aW9uKHJvdywgc2VsZWN0ZWRDaGFyZ2VzKSB7DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMubG9jYWxEZWJpdE5vdGVMaXN0LmZpbmRJbmRleChpdGVtID0+IGl0ZW0gPT09IHJvdykNCiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy5sb2NhbERlYml0Tm90ZUxpc3RbaW5kZXhdLnNlbGVjdGVkQ2hhcmdlcyA9IHNlbGVjdGVkQ2hhcmdlcw0KICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bmlbDmja7lj5jljJYNCiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOmRlYml0Tm90ZUxpc3QnLCB0aGlzLmxvY2FsRGViaXROb3RlTGlzdCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSN5Yi26LS555SoDQogICAgaGFuZGxlQ29weUZyZWlnaHQoY2hhcmdlKSB7DQogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIGNoYXJnZSkNCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk6LS555So6aG5DQogICAgaGFuZGxlRGVsZXRlSXRlbShjaGFyZ2UpIHsNCiAgICAgIHRoaXMuJGVtaXQoImRlbGV0ZUl0ZW0iLCBjaGFyZ2UpDQogICAgfSwNCg0KICAgIC8vIOWIoOmZpOaJgOaciei0ueeUqA0KICAgIGhhbmRsZURlbGV0ZUFsbCgpIHsNCiAgICAgIHRoaXMuJGVtaXQoImRlbGV0ZUFsbCIpDQogICAgfSwNCg0KICAgIC8vIOiOt+WPlui0puWNleeKtuaAgeexu+Weiw0KICAgIGdldEJpbGxTdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAiZHJhZnQiOiAiaW5mbyIsDQogICAgICAgICJjb25maXJtZWQiOiAic3VjY2VzcyIsDQogICAgICAgICJjbG9zZWQiOiAiZGFuZ2VyIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICJpbmZvIg0KICAgIH0sDQoNCiAgICAvLyDojrflj5botKbljZXnirbmgIHmlofmnKwNCiAgICBnZXRCaWxsU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgImRyYWZ0IjogIuiNieeovyIsDQogICAgICAgICJjb25maXJtZWQiOiAi5bey56Gu6K6kIiwNCiAgICAgICAgImNsb3NlZCI6ICLlt7LlhbPpl60iDQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgIuacquefpSINCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Y+R56Wo54q25oCB57G75Z6LDQogICAgZ2V0SW52b2ljZVN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bmlzc3VlZCI6ICJpbmZvIiwNCiAgICAgICAgImlzc3VlZCI6ICJzdWNjZXNzIiwNCiAgICAgICAgImNhbmNlbGVkIjogImRhbmdlciINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAiaW5mbyINCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Y+R56Wo54q25oCB5paH5pysDQogICAgZ2V0SW52b2ljZVN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bmlzc3VlZCI6ICLmnKrlvIDnpagiLA0KICAgICAgICAiaXNzdWVkIjogIuW3suW8gOelqCIsDQogICAgICAgICJjYW5jZWxlZCI6ICLlt7LkvZzlup8iDQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgIuacquefpSINCiAgICB9LA0KDQogICAgLy8g6I635Y+W6ZSA6LSm54q25oCB57G75Z6LDQogICAgZ2V0V3JpdGVvZmZTdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAidW53cml0dGVuIjogImluZm8iLA0KICAgICAgICAicGFydGlhbCI6ICJ3YXJuaW5nIiwNCiAgICAgICAgIndyaXR0ZW4iOiAic3VjY2VzcyINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAiaW5mbyINCiAgICB9LA0KDQogICAgLy8g6I635Y+W6ZSA6LSm54q25oCB5paH5pysDQogICAgZ2V0V3JpdGVvZmZTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAidW53cml0dGVuIjogIuacqumUgOi0piIsDQogICAgICAgICJwYXJ0aWFsIjogIumDqOWIhumUgOi0piIsDQogICAgICAgICJ3cml0dGVuIjogIuW3sumUgOi0piINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAi5pyq55+lIg0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["debitNodeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8LA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debitNodeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <div class=\"debit-note-list\">\r\n    <el-table\r\n      ref=\"debitNoteTable\"\r\n      :data=\"debitNoteList\"\r\n      border\r\n      style=\"width: 100%\"\r\n      @expand-change=\"handleExpandChange\"\r\n    >\r\n      <!-- 可展开列 -->\r\n      <el-table-column type=\"expand\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 嵌套 chargeList 组件 -->\r\n          <charges\r\n              :charge-data=\"scope.row.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled\"\r\n              :hidden-supplier=\"hiddenSupplier\"\r\n              :is-receivable=\"isReceivable\"\r\n              :open-charge-list=\"true\"\r\n              :debit-note=\"scope.row\"\r\n              @copyFreight=\"handleCopyFreight\"\r\n              @deleteAll=\"handleDeleteAll\"\r\n              @deleteItem=\"handleDeleteItem\"\r\n              @return=\"handleChargeDataChange(scope.row, $event)\"\r\n              @selectRow=\"handleChargeSelection(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- 分账单基本信息列 -->\r\n      <el-table-column label=\"所属公司\" prop=\"sqdRctNo\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.companyBelongsTo\" :placeholder=\"'收付路径'\"\r\n                       :type=\"'rsPaymentTitle'\" @return=\"scope.row.companyBelongsTo=$event\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"我司账户\" prop=\"companyName\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.clearingCompanyBankAccount\" :placeholder=\"'我司账户'\"\r\n                       :type=\"'companyAccount'\"\r\n                       @return=\"scope.row.clearingCompanyBankAccount=$event\" @returnData=\"selectBankAccount(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"收付标志\" prop=\"isRecievingOrPaying\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n              :type=\"getBillStatusType(scope.row.billStatus)\"\r\n              size=\"mini\"\r\n          >\r\n            {{ scope.row.isRecievingOrPaying == 0 ? \"收\" : \"付\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算单位\" prop=\"dnCurrencyCode\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :custom-options=\"companyList\" :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                       :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                       @returnData=\"handleSelectCompany(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"对方账户\" prop=\"billReceivable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input v-model=\"scope.row.clearingCompanyBankAccount\"/>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算币种\" prop=\"billPayable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :pass=\"scope.row.dnCurrencyCode\" :type=\"'currency'\"\r\n                       @return=\"changeCurrency(scope.row,$event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"right\" label=\"账单应收\" v-if=\"isReceivable\" prop=\"billReceivable\" width=\"80\"/>\r\n      <el-table-column align=\"right\" label=\"账单应付\" v-if=\"!isReceivable\" prop=\"billPayable\" width=\"80\"/>\r\n\r\n      <el-table-column align=\"center\" label=\"账单状态\" prop=\"billStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getBillStatusText(scope.row.billStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getInvoiceStatusType(scope.row.invoiceStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"申请支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n              v-model=\"scope.row.requestPaymentDate\"\r\n              placeholder=\"选择日期\"\r\n              type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n              v-model=\"scope.row.expectedPaymentDate\"\r\n              placeholder=\"选择日期\"\r\n              type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n              v-model=\"scope.row.actualPaymentDate\"\r\n              placeholder=\"选择日期\"\r\n              type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"销账状态\" prop=\"writeoffStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getWriteoffStatusType(scope.row.writeoffStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column class-name=\"small-padding fixed-width\" fixed=\"right\" label=\"操作\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <div style=\"display: flex; gap: 4px;\">\r\n            <el-button\r\n                v-if=\"scope.row.billStatus==='confirmed'\"\r\n                icon=\"el-icon-unlock\"\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"applyUnlock(scope.row)\"\r\n            >\r\n              申请解锁\r\n            </el-button>\r\n            <el-button\r\n                v-if=\"scope.row.billStatus==='draft'\"\r\n                icon=\"el-icon-check\"\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"setComplete(scope.row)\"\r\n            >\r\n              设置完成\r\n            </el-button>\r\n            <el-button\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"deleteDebitNote(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addDebitNote\"\r\n    >[＋]\r\n    </el-button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Charges from \"@/views/system/document/chargeList.vue\"\r\n\r\nexport default {\r\n  name: \"debitNoteList\",\r\n  components: {Charges},\r\n  props: [\r\n    \"companyList\",\r\n    \"disabled\",\r\n    \"hiddenSupplier\",\r\n    \"rctId\",\r\n    \"debitNoteList\",\r\n    \"isReceivable\"\r\n  ],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      expandedRows: [],\r\n      localDebitNoteList: []\r\n    }\r\n  },\r\n  watch: {\r\n    debitNoteList: {\r\n      immediate: true,\r\n      handler(newVal) {\r\n        this.$emit('update:debitNoteList', newVal)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    applyUnlock(row) {\r\n      this.$emit(\"applyUnlock\", row)\r\n    },\r\n    setComplete(row) {\r\n      this.$emit(\"setComplete\", row)\r\n    },\r\n    changeCurrency(row, currency) {\r\n      row.dnCurrencyCode = currency\r\n    },\r\n    selectBankAccount(row, bankAccount) {\r\n      row.bankAccountCode = bankAccount.bankAccountCode\r\n      row.bankAccountName = bankAccount.bankAccountName\r\n    },\r\n    handleSelectCompany(row, company) {\r\n      row.clearingCompanyId = company.companyId\r\n      row.clearingCompanyName = company.companyShortName\r\n    },\r\n    addDebitNote() {\r\n      this.$emit(\"addDebitNote\")\r\n    },\r\n    currency,\r\n\r\n    // 展开/收起行\r\n    handleExpandChange(row, expandedRows) {\r\n      this.expandedRows = expandedRows\r\n    },\r\n\r\n    // 创建分账单\r\n    async createDebitNote(row) {\r\n      try {\r\n\r\n      } catch (error) {\r\n        console.error(\"创建分账单失败:\", error)\r\n        this.$message.error(\"创建分账单失败\")\r\n      }\r\n    },\r\n\r\n    // 编辑分账单\r\n    editDebitNote(row) {\r\n      this.$emit(\"editDebitNote\", row)\r\n    },\r\n\r\n    // 删除分账单\r\n    async deleteDebitNote(row) {\r\n      try {\r\n        await this.$confirm(\"确定要删除该分账单吗？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n        this.$emit(\"deleteDebitNote\", row)\r\n      } catch (error) {\r\n        if (error !== \"cancel\") {\r\n          console.error(\"删除分账单失败:\", error)\r\n          this.$message.error(\"删除分账单失败\")\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理费用数据变化\r\n    handleChargeDataChange(row, chargeData) {\r\n      let billReceivable = 0\r\n      let billPayable = 0\r\n\r\n      // 统计chargeData的费用\r\n      if (this.isReceivable) {\r\n        // 应收\r\n        chargeData.forEach(item => {\r\n          // 使用currency.js计算\r\n          billReceivable = currency(billReceivable).add(item.subtotal).toString()\r\n        })\r\n      } else {\r\n        // 应付\r\n        chargeData.forEach(item => {\r\n          billPayable = currency(billPayable).add(item.subtotal).toString()\r\n        })\r\n      }\r\n      row.billReceivable = billReceivable\r\n      row.billPayable = billPayable\r\n    },\r\n\r\n    // 处理费用选择\r\n    handleChargeSelection(row, selectedCharges) {\r\n      const index = this.localDebitNoteList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.localDebitNoteList[index].selectedCharges = selectedCharges\r\n        // 通知父组件数据变化\r\n        this.$emit('update:debitNoteList', this.localDebitNoteList)\r\n      }\r\n    },\r\n\r\n    // 复制费用\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n\r\n    // 删除费用项\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    },\r\n\r\n    // 删除所有费用\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n\r\n    // 获取账单状态类型\r\n    getBillStatusType(status) {\r\n      const statusMap = {\r\n        \"draft\": \"info\",\r\n        \"confirmed\": \"success\",\r\n        \"closed\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取账单状态文本\r\n    getBillStatusText(status) {\r\n      const statusMap = {\r\n        \"draft\": \"草稿\",\r\n        \"confirmed\": \"已确认\",\r\n        \"closed\": \"已关闭\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取销账状态类型\r\n    getWriteoffStatusType(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"info\",\r\n        \"partial\": \"warning\",\r\n        \"written\": \"success\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取销账状态文本\r\n    getWriteoffStatusText(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"未销账\",\r\n        \"partial\": \"部分销账\",\r\n        \"written\": \"已销账\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.charge-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n\r\n  span {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 覆盖 Element UI 表格样式\r\n:deep(.el-table) {\r\n  .el-table__expanded-cell {\r\n    padding: 0;\r\n\r\n    .expand-content {\r\n      margin: 0;\r\n      border: none;\r\n      background-color: transparent;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}