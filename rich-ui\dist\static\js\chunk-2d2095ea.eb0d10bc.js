(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2095ea"],{a984:function(e,r,g){"use strict";g.r(r);g("d9e2");r["default"]={data:function(){var e=function(e,r,g){r?g():g(new Error("请输入所属公司"))};return{rules:{clientId:[{required:!0,message:"请选择客户",trigger:"blur"}],goodsNameSummary:[{required:!0,message:"请填写货名",trigger:"blur"}],grossWeight:[{required:!0,message:"请填写货名",trigger:"blur"}],logisticsTypeId:[{required:!0,message:"请选择物流类型",trigger:"blur"}],polId:[{required:!0,message:"请选择启运港",trigger:"blur"}],destinationPortId:[{required:!0,message:"请选择目的港",trigger:"blur"}],serviceTypeIds:[{required:!0,message:"请选择服务列表",trigger:"blur"}],blFormCode:[{required:!0,message:"请选择出单方式",trigger:"blur"}],revenueTon:[{required:!0,message:"请选择计费货量",trigger:"blur"}],salesId:[{required:!0,message:"请选择计费货量",trigger:"blur"}],orderBelongsTo:[{validator:e,trigger:"blur"}]}}}}}}]);