{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue?vue&type=template&id=4fcf148f&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue", "mtime": 1752832104327}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}