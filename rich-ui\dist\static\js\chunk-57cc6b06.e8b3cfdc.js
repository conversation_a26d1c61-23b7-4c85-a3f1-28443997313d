(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-57cc6b06","chunk-5c8dffec"],{"332a":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-row",[s("el-col",{attrs:{span:18}},[s("div",{staticClass:"service-bar",staticStyle:{display:"flex","margin-top":"10px","margin-bottom":"10px",width:"100%"}},[s("a",{class:{"el-icon-arrow-down":e.visible,"el-icon-arrow-right":!e.visible}}),s("div",{staticStyle:{width:"150px",display:"flex"}},[s("h3",{staticStyle:{margin:"0",width:"250px","text-align":"left"},on:{click:e.toggleVisible}},[e._v("提单信息")]),s("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(t){return e.$emit("openChargeSelect",e.rsClientMessage)}}},[e._v(" [DN...] ")])],1),e.auditInfo?s("el-col",{staticStyle:{display:"flex"},attrs:{span:15}},[s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:opapproval","system:rct:opapproval"],expression:"['system:booking:opapproval','system:rct:opapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{icon:e.rsClientServiceInstance.isDnOpConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.$emit("confirmed","op")}}},[e._v("操作确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.opConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.opConfirmedDate))])])],1),s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:salesapproval","system:rct:salesapproval"],expression:"['system:booking:salesapproval','system:rct:salesapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{icon:e.rsClientServiceInstance.isDnSalesConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.$emit("confirmed","sales")}}},[e._v("业务确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.salesConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.salesConfirmedDate))])])],1),s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:clientapproval","system:rct:clientapproval"],expression:"['system:booking:clientapproval','system:rct:clientapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{icon:e.rsClientServiceInstance.isDnClientConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.$emit("confirmed","client")}}},[e._v("客户确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.clientConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.clientConfirmedDate))])])],1),s("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:financeapproval","system:rct:financeapproval"],expression:"['system:booking:financeapproval','system:rct:financeapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[s("el-button",{staticStyle:{padding:"0"},attrs:{icon:e.rsClientServiceInstance.isAccountConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.$emit("confirmed","account",e.rsClientMessage.rsChargeList)}}},[e._v("财务确认 ")]),s("div",{staticStyle:{"text-align":"left",width:"120px"}},[s("div",[s("i",{staticClass:"el-icon-user"}),e._v(e._s(e.accountConfirmedName))]),s("div",[s("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.accountConfirmedDate))])])],1)]):e._e(),s("div",{staticStyle:{"margin-left":"auto"}},e._l(e.fileOptions,(function(t,a){return s("el-popover",{key:a,attrs:{placement:"top",trigger:"click",width:"100"}},[e._l(t.templateList,(function(a,o){return s("el-button",{key:o,on:{click:function(s){return e.handleFileAction(t.link,a)}}},[e._v(e._s(a)+" ")])})),s("a",{staticStyle:{color:"blue",padding:"0","margin-left":"10px"},attrs:{slot:"reference",target:"_blank"},slot:"reference"},[e._v("["+e._s(t.file)+"]")])],2)})),1)],1)])],1),s("transition",{attrs:{name:"fade"}},[e.visible?s("el-row",{staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[s("transition",{attrs:{name:"fade"}},[e.branchInfo?s("el-col",{attrs:{span:18}},[s("el-table",{attrs:{data:e.bookingMessageList,border:""},on:{"selection-change":e.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"55"}}),s("el-table-column",{attrs:{label:"序号",type:"index",width:"50"}}),s("el-table-column",{attrs:{label:"MB/L No",prop:"mBlNo"}}),s("el-table-column",{attrs:{label:"HB/L No",prop:"hBlNo"}}),s("el-table-column",{attrs:{label:"发货人",prop:"bookingShipper"}}),s("el-table-column",{attrs:{label:"收货人",prop:"bookingConsignee"}}),s("el-table-column",{attrs:{label:"通知人",prop:"bookingNotifyParty"}}),s("el-table-column",{attrs:{label:"代理",prop:"bookingAgent"}}),s("el-table-column",{attrs:{label:"柜号",prop:"containerNo"}}),s("el-table-column",{attrs:{label:"封号",prop:"sealNo"}}),s("el-table-column",{attrs:{label:"柜型",prop:"containerType"}}),s("el-table-column",{attrs:{label:"唛头",prop:"shippingMark"}}),s("el-table-column",{attrs:{label:"件数",prop:"packageQuantity"}}),s("el-table-column",{attrs:{label:"货描",prop:"goodsDescription"}}),s("el-table-column",{attrs:{label:"体积",prop:"goodsVolume"}}),s("el-table-column",{attrs:{label:"重量",prop:"grossWeight"}}),s("el-table-column",{attrs:{label:"提单类型",prop:"blTypeCode"}}),s("el-table-column",{attrs:{label:"出单方式",prop:"blFormCode"}}),s("el-table-column",{attrs:{label:"交单方式",prop:"sqdDocDeliveryWay"},scopedSlots:e._u([{key:"default",fn:function(e){return[s("tree-select",{class:"disable-form",attrs:{disabled:!0,flat:!1,multiple:!1,pass:e.row.sqdDocDeliveryWay,placeholder:"货代单交单方式",type:"docReleaseWay"},on:{return:function(t){e.row.sqdDocDeliveryWay=t}}})]}}],null,!1,16477961)}),s("el-table-column",{attrs:{label:"操作",prop:"sqdDocDeliveryWay"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{type:"text"},on:{click:function(s){return e.handleBookingMessageUpdate(t.row)}}},[e._v("修改")]),s("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(s){return e.deleteBookingMessage(t.row)}}},[e._v("删除 ")])]}}],null,!1,3431747246)})],1),s("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,"show-close":!1,title:e.bookingMessageTitle,visible:e.openBookingMessage,"append-to-body":"",width:"30%"},on:{close:e.closeBookingMessage,"update:visible":function(t){e.openBookingMessage=t}}},[s("el-form",{ref:"bookingMessageForm",staticClass:"edit",attrs:{model:e.bookingMessageForm,"label-width":"80px"}},["MBL"===e.bookingMessageForm.blTypeCode?s("div",[s("el-form-item",{attrs:{label:"提单号码"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},model:{value:e.bookingMessageForm.mBlNo,callback:function(t){e.$set(e.bookingMessageForm,"mBlNo",t)},expression:"bookingMessageForm.mBlNo"}})],1)],1):s("div",[s("el-form-item",{attrs:{label:"MB/L No"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},model:{value:e.bookingMessageForm.mBlNo,callback:function(t){e.$set(e.bookingMessageForm,"mBlNo",t)},expression:"bookingMessageForm.mBlNo"}})],1),s("el-form-item",{attrs:{label:"HB/L No"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},model:{value:e.bookingMessageForm.hBlNo,callback:function(t){e.$set(e.bookingMessageForm,"hBlNo",t)},expression:"bookingMessageForm.hBlNo"}})],1)],1),s("el-form-item",{attrs:{label:"发货人"}},[s("template",{slot:"label"},[s("div",[e._v("发货人")]),s("el-button",{staticStyle:{color:"blue"},attrs:{type:"text"},on:{click:function(t){return e.$emit("handleAddCommon","release")}}},[e._v("[↗] ")]),s("el-button",{staticStyle:{color:"blue"},attrs:{type:"text"},on:{click:function(t){return e.$emit("openReleaseUsed")}}},[e._v("[...]")])],1),s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.bookingShipper,callback:function(t){e.$set(e.bookingMessageForm,"bookingShipper",t)},expression:"bookingMessageForm.bookingShipper"}})],2),s("el-form-item",{attrs:{label:"收货人"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.bookingConsignee,callback:function(t){e.$set(e.bookingMessageForm,"bookingConsignee",t)},expression:"bookingMessageForm.bookingConsignee"}})],1),s("el-form-item",{attrs:{label:"通知人"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.bookingNotifyParty,callback:function(t){e.$set(e.bookingMessageForm,"bookingNotifyParty",t)},expression:"bookingMessageForm.bookingNotifyParty"}})],1),s("el-form-item",{attrs:{label:"代理"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.bookingAgent,callback:function(t){e.$set(e.bookingMessageForm,"bookingAgent",t)},expression:"bookingMessageForm.bookingAgent"}})],1),s("el-form-item",{attrs:{label:"启运港"}},[s("el-input",{model:{value:e.bookingMessageForm.polName,callback:function(t){e.$set(e.bookingMessageForm,"polName",t)},expression:"bookingMessageForm.polName"}})],1),s("el-form-item",{attrs:{label:"卸货港"}},[s("el-input",{model:{value:e.bookingMessageForm.podName,callback:function(t){e.$set(e.bookingMessageForm,"podName",t)},expression:"bookingMessageForm.podName"}})],1),s("el-form-item",{attrs:{label:"目的港"}},[s("el-input",{model:{value:e.bookingMessageForm.destinationPort,callback:function(t){e.$set(e.bookingMessageForm,"destinationPort",t)},expression:"bookingMessageForm.destinationPort"}})],1),s("el-form-item",{attrs:{label:"柜号"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.containerNo,callback:function(t){e.$set(e.bookingMessageForm,"containerNo",t)},expression:"bookingMessageForm.containerNo"}})],1),s("el-form-item",{attrs:{label:"封号"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.sealNo,callback:function(t){e.$set(e.bookingMessageForm,"sealNo",t)},expression:"bookingMessageForm.sealNo"}})],1),s("el-form-item",{attrs:{label:"柜型"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.containerType,callback:function(t){e.$set(e.bookingMessageForm,"containerType",t)},expression:"bookingMessageForm.containerType"}})],1),s("el-form-item",{attrs:{label:"唛头"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.shippingMark,callback:function(t){e.$set(e.bookingMessageForm,"shippingMark",t)},expression:"bookingMessageForm.shippingMark"}})],1),s("el-form-item",{attrs:{label:"件数"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500",placeholder:"件数","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.packageQuantity,callback:function(t){e.$set(e.bookingMessageForm,"packageQuantity",t)},expression:"bookingMessageForm.packageQuantity"}})],1),s("el-form-item",{attrs:{label:"货描"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.goodsDescription,callback:function(t){e.$set(e.bookingMessageForm,"goodsDescription",t)},expression:"bookingMessageForm.goodsDescription"}})],1),s("el-form-item",{attrs:{label:"重量"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500",placeholder:"重量","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.grossWeight,callback:function(t){e.$set(e.bookingMessageForm,"grossWeight",t)},expression:"bookingMessageForm.grossWeight"}})],1),s("el-form-item",{attrs:{label:"体积"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500",placeholder:"体积","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.goodsVolume,callback:function(t){e.$set(e.bookingMessageForm,"goodsVolume",t)},expression:"bookingMessageForm.goodsVolume"}})],1),s("el-form-item",{attrs:{label:"备注"}},[s("el-input",{staticStyle:{padding:"0",margin:"0"},attrs:{autosize:{minRows:2.5,maxRows:5},maxlength:"500","show-word-limit":"",type:"textarea"},model:{value:e.bookingMessageForm.blRemark,callback:function(t){e.$set(e.bookingMessageForm,"blRemark",t)},expression:"bookingMessageForm.blRemark"}})],1),s("el-form-item",{attrs:{label:"出单地"}},[s("location-select",{attrs:{"load-options":e.psaBookingSelectData.locationOptions,"no-parent":!0,pass:e.bookingMessageForm.polIds,placeholder:"启运港"},on:{returnData:function(t){e.bookingMessageForm.city=t.locationEnShortName}}})],1),s("el-form-item",{attrs:{label:"开船日期"}},[s("el-date-picker",{attrs:{placeholder:"选择日期",type:"date"},model:{value:e.bookingMessageForm.onBoardDate,callback:function(t){e.$set(e.bookingMessageForm,"onBoardDate",t)},expression:"bookingMessageForm.onBoardDate"}})],1),s("el-form-item",{attrs:{label:"付款方式"}},[s("el-select",{attrs:{placeholder:"请选择"},model:{value:e.bookingMessageForm.payWay,callback:function(t){e.$set(e.bookingMessageForm,"payWay",t)},expression:"bookingMessageForm.payWay"}},e._l(e.payWayOptions,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{attrs:{label:"提单类型"}},[s("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.bookingMessageForm.blTypeCode,placeholder:"提单类型",type:"blType"},on:{return:function(t){e.bookingMessageForm.blTypeCode=t}}})],1),s("el-form-item",{attrs:{label:"出单方式"}},[s("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.bookingMessageForm.blFormCode,placeholder:"出单方式",type:"blForm"},on:{return:function(t){e.bookingMessageForm.blFormCode=t}}})],1),s("el-form-item",{attrs:{label:"交单方式"}},[s("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.bookingMessageForm.sqdDocDeliveryWay,placeholder:"货代单交单方式",type:"docReleaseWay"},on:{return:function(t){e.bookingMessageForm.sqdDocDeliveryWay=t}}})],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.bookingMessageConfirm}},[e._v("确 定")]),s("el-button",{attrs:{size:"mini"},on:{click:e.closeBookingMessage}},[e._v("取 消")])],1)],1),s("el-button",{staticStyle:{padding:"0"},attrs:{disabled:e.psaVerify||e.disabled,type:"text"},on:{click:e.addBookingMessage}},[e._v("[＋] ")])],1):e._e()],1),s("transition",{attrs:{name:"fade"}},[e.logisticsInfo?s("el-col",{attrs:{span:4}},[s("el-form-item",{attrs:{label:"进度需求",prop:"goodsNameSummary"}}),s("div",[s("logistics-progress",{attrs:{disabled:e.rsClientMessageFormDisable||e.disabled||e.psaVerify,"logistics-progress-data":e.rsClientMessage.rsOpLogList,"open-logistics-progress-list":!0},on:{deleteItem:e.deleteLogisticsItem,return:e.updateLogisticsProgress}})],1)],1):e._e()],1),s("transition",{attrs:{name:"fade"}},[e.chargeInfo?s("el-col",{attrs:{span:10.5}},[s("charge-list",{attrs:{"a-t-d":e.form.podEta,"charge-data":e.rsClientMessage.rsChargeList,"company-list":e.companyList,disabled:e.rsClientMessageFormDisable||e.disabled,"is-receivable":!0,"open-charge-list":!0,"rs-client-message-payable-r-m-b":e.rsClientMessagePayableRMB,"rs-client-message-payable-tax-r-m-b":e.rsClientMessagePayableTaxRMB,"rs-client-message-payable-tax-u-s-d":e.rsClientMessagePayableTaxUSD,"rs-client-message-payable-u-s-d":e.rsClientMessagePayableUSD,"rs-client-message-profit-r-m-b":e.rsClientMessageProfitRMB,"rs-client-message-profit-tax-r-m-b":e.rsClientMessageProfitTaxRMB,"rs-client-message-profit-tax-u-s-d":e.rsClientMessageProfitTaxUSD,"rs-client-message-profit-u-s-d":e.rsClientMessageProfitUSD,"rs-client-message-receivable-r-m-b":e.rsClientMessageReceivableRMB,"rs-client-message-receivable-tax-r-m-b":e.rsClientMessageReceivableTaxRMB,"rs-client-message-receivable-tax-u-s-d":e.rsClientMessageReceivableTaxUSD,"rs-client-message-receivable-u-s-d":e.rsClientMessageReceivableUSD},on:{copyFreight:function(t){return e.$emit("copyFreight",t)},deleteAll:function(t){e.rsClientMessage.rsChargeList=[]},deleteItem:function(t){e.rsClientMessage.rsChargeList=e.rsClientMessage.rsChargeList.filter((function(e){return e!=t}))},return:function(t){return e.$emit("rsClientMessageCharge",t)},returnProfit:function(t){return e.$emit("handleProfit",t)},selectRow:function(t){return e.$emit("handleReceiveSelected",t)}}})],1):e._e()],1)],1):e._e()],1)],1)},o=[],i=(s("a9e3"),s("4de4"),s("d3b7"),s("b59f")),n=s("4fbf"),l=s("3de8"),r=s("addf"),c={name:"BillOfLadingInfo",components:{LogisticsProgress:i["default"],ChargeList:n["default"],TreeSelect:l["a"],LocationSelect:r["a"]},props:{bookingMessageForm:{type:Object,required:!0,default:null},openBookingMessage:{type:Boolean,default:!1},bookingMessageStatus:{type:String,default:"<UNK>"},bookingMessageList:{type:Array,default:[]},rsClientMessage:{type:Object,required:!0},form:{type:Object,required:!0},disabled:{type:Boolean,default:!1},psaVerify:{type:Boolean,default:!1},auditInfo:{type:Boolean,default:!0},branchInfo:{type:Boolean,default:!0},logisticsInfo:{type:Boolean,default:!0},chargeInfo:{type:Boolean,default:!0},companyList:{type:Array,default:function(){return[]}},rsClientMessageReceivableRMB:{type:Number,default:0},rsClientMessageReceivableUSD:{type:Number,default:0},rsClientMessagePayableRMB:{type:Number,default:0},rsClientMessagePayableUSD:{type:Number,default:0},rsClientMessageProfitRMB:{type:Number,default:0},rsClientMessageProfitUSD:{type:Number,default:0},rsClientMessageReceivableTaxRMB:{type:Number,default:0},rsClientMessageReceivableTaxUSD:{type:Number,default:0},rsClientMessagePayableTaxRMB:{type:Number,default:0},rsClientMessagePayableTaxUSD:{type:Number,default:0},rsClientMessageProfitTaxRMB:{type:Number,default:0},rsClientMessageProfitTaxUSD:{type:Number,default:0}},data:function(){return{visible:!0,bookingMessageTitle:"提单信息",psaBookingSelectData:{locationOptions:[]},rsClientMessageFormDisable:!1,fileOptions:[{file:"操作单",link:"getOpBill",templateList:["整柜","散货","空运","其他"]},{file:"提单",link:"getBillOfLading",templateList:["套打提单","电放提单"]},{file:"费用清单",link:"getChargeListBill",templateList:["CN-广州瑞旗[招行USD+工行RMB]","CN-广州瑞旗[USD->RMB]","EN-广州瑞旗[招行USD]","EN-广州瑞旗[RMB->USD]","EN- 瑞旗香港账户[HSBC RMB->USD]","EN- 香港瑞旗[HSBC]","CN-广州正泽[招行USD+RMB]","CN-广州正泽[USD->RMB]"]}],payWayOptions:[{label:"预付",value:"FREIGHTP REPAID"},{label:"到付",value:"FREIGHTP COLLECT"}]}},computed:{rsClientServiceInstance:function(){return this.rsClientMessage||{}},opConfirmedName:function(){return this.rsClientServiceInstance.dnOpConfirmedName||""},opConfirmedDate:function(){return this.rsClientServiceInstance.dnOpConfirmedDate||""},salesConfirmedName:function(){return this.rsClientServiceInstance.dnSalesConfirmedName||""},salesConfirmedDate:function(){return this.rsClientServiceInstance.dnSalesConfirmedDate||""},clientConfirmedName:function(){return this.rsClientServiceInstance.dnClientConfirmedName||""},clientConfirmedDate:function(){return this.rsClientServiceInstance.dnClientConfirmedDate||""},accountConfirmedName:function(){return this.rsClientServiceInstance.accountConfirmedName||""},accountConfirmedDate:function(){return this.rsClientServiceInstance.accountConfirmedDate||""}},created:function(){},methods:{toggleVisible:function(){this.visible=!this.visible},handleFileAction:function(e,t){this.$emit(e,t)},handleSelectionChange:function(e){this.$emit("handleSelectionChange",e)},handleBookingMessageUpdate:function(e){this.$emit("handleBookingMessageUpdate",e)},addBookingMessage:function(){this.$emit("handleAddBookingMessage",this.bookingMessageForm)},bookingMessageConfirm:function(){this.$emit("bookingMessageConfirm",this.bookingMessageForm)},closeBookingMessage:function(){this.$emit("closeBookingMessage")},deleteBookingMessage:function(e){this.$emit("deleteBookingMessage",e)},deleteLogisticsItem:function(e){this.rsClientMessage&&this.rsClientMessage.rsOpLogList&&(this.rsClientMessage.rsOpLogList=this.rsClientMessage.rsOpLogList.filter((function(t){return t!==e})))},updateLogisticsProgress:function(e){this.rsClientMessage&&(this.rsClientMessage.rsOpLogList=e)}}},m=c,d=(s("8b64"),s("2877")),g=Object(d["a"])(m,a,o,!1,null,"7aa1db0e",null);t["default"]=g.exports},4678:function(e,t,s){var a={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function o(e){var t=i(e);return s(t)}function i(e){if(!s.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}o.keys=function(){return Object.keys(a)},o.resolve=i,e.exports=o,o.id="4678"},"8b64":function(e,t,s){"use strict";s("d307")},b59f:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{attrs:{id:"app-container"}},[s("el-col",{staticStyle:{margin:"0",padding:"0"},style:{display:e.openLogisticsProgressList?"":"none"}},[s("div",{class:{inactive:0==e.openLogisticsProgressList,active:e.openLogisticsProgressList}},[s("el-table",{staticClass:"pd0",attrs:{data:e.logisticsProgressData,"row-class-name":e.rowIndex,border:""}},[s("el-table-column",{attrs:{label:"进度",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showProgress?e._e():s("div",{staticStyle:{width:"50px",height:"20px"},on:{click:function(e){t.row.showProgress=!0}}},[e._v(" "+e._s(t.row.basProcess.processShortName)+" ")]),t.row.showProgress?s("progress-name",{attrs:{disabled:e.disabled,pass:t.row.processId,placeholder:"进度","process-type":e.processType,"service-type":e.serviceType},on:{returnData:function(e){t.row.sqdProcessEnName=e.processEnName},progressName:function(e){t.row.processId=e}}}):e._e()]}}])}),s("el-table-column",{attrs:{align:"center",label:"状态",prop:"quotationChargeId"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showStatus?e._e():s("div",{on:{click:function(e){t.row.showStatus=!0}}},[e._v(" "+e._s(t.row.basProcessStatus.processStatusShortName)+" "+e._s(t.row.processStatusTime)+" ")]),t.row.showStatus?s("div",{staticStyle:{display:"flex"}},[s("progress-status",{staticStyle:{flex:"1"},attrs:{pass:t.row.processStatusId,placeholder:"物流进度"},on:{progressStatus:function(e){t.row.processStatusId=e}}}),t.row.showStatus?s("el-date-picker",{staticStyle:{flex:"2"},attrs:{placeholder:"选择日期时间",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.row.processStatusTime,callback:function(s){e.$set(t.row,"processStatusTime",s)},expression:"scope.row.processStatusTime"}}):e._e()],1):e._e()]}}])}),s("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger",disabled:e.disabled},on:{click:function(s){return e.deleteLogisticsProgress(t.row)}}},[e._v("删除 ")])]}}])})],1)],1),s("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",disabled:e.disabled},on:{click:e.addLogisticsProgress}},[e._v("[＋] ")])],1)],1)},o=[],i=(s("14d9"),s("5bc0")),n=s("1dec"),l=s("c1df"),r=s.n(l),c={name:"logisticsProgress",components:{ProgressName:n["a"],ProgressStatus:i["a"]},props:["logisticsProgressData","openLogisticsProgressList","disabled","serviceType","processType"],watch:{logisticsProgressData:function(e){this.$emit("return",e)}},methods:{rowIndex:function(e){var t=e.row,s=e.rowIndex;t.id=s+1},addLogisticsProgress:function(){var e={showProgress:!0,showStatus:!0,processStatusId:7,processStatusTime:r()().format("yyyy-MM-DD HH:mm:ss"),opId:this.$store.state.user.sid};this.logisticsProgressData.push(e)},deleteLogisticsProgress:function(e){this.$emit("deleteItem",e)}}},m=c,d=s("2877"),g=Object(d["a"])(m,a,o,!1,null,"12005fc7",null);t["default"]=g.exports},d307:function(e,t,s){}}]);