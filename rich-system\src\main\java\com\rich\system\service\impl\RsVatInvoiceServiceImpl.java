package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsVatInvoice;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsVatInvoiceMapper;
import com.rich.system.service.RsVatInvoiceService;

/**
 * 发票登记Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class RsVatInvoiceServiceImpl implements RsVatInvoiceService {
    @Autowired
    private RsVatInvoiceMapper rsVatInvoiceMapper;

    /**
     * 查询发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 发票登记
     */
    @Override
    public RsVatInvoice selectRsVatInvoiceByInvoiceId(Long invoiceId) {
        return rsVatInvoiceMapper.selectRsVatInvoiceByInvoiceId(invoiceId);
    }

    /**
     * 查询发票登记列表
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记
     */
    @Override
    public List<RsVatInvoice> selectRsVatInvoiceList(RsVatInvoice rsVatInvoice) {
        return rsVatInvoiceMapper.selectRsVatInvoiceList(rsVatInvoice);
    }

    /**
     * 新增发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    @Override
    public int insertRsVatInvoice(RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setCreateTime(DateUtils.getNowDate());
        rsVatInvoice.setCreateBy(SecurityUtils.getUserId());
        return rsVatInvoiceMapper.insertRsVatInvoice(rsVatInvoice);
    }

    /**
     * 修改发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    @Override
    public int updateRsVatInvoice(RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setUpdateTime(DateUtils.getNowDate());
        rsVatInvoice.setUpdateBy(SecurityUtils.getUserId());
        return rsVatInvoiceMapper.updateRsVatInvoice(rsVatInvoice);
    }

    /**
     * 修改发票登记状态
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记
     */
    @Override
    public int changeStatus(RsVatInvoice rsVatInvoice) {
        return rsVatInvoiceMapper.updateRsVatInvoice(rsVatInvoice);
    }

    /**
     * 批量删除发票登记
     *
     * @param invoiceIds 需要删除的发票登记主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceByInvoiceIds(Long[] invoiceIds) {
        return rsVatInvoiceMapper.deleteRsVatInvoiceByInvoiceIds(invoiceIds);
    }

    /**
     * 删除发票登记信息
     *
     * @param invoiceId 发票登记主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceByInvoiceId(Long invoiceId) {
        return rsVatInvoiceMapper.deleteRsVatInvoiceByInvoiceId(invoiceId);
    }
}
