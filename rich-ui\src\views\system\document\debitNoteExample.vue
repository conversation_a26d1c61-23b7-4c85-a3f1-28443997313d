<!--
使用示例：在父组件中使用 debitNoteList 组件

使用方式：
1. 引入组件
2. 传入必要的属性
3. 监听组件事件

示例代码：
-->
<template>
  <div>
    <debit-note-list
      :atd="ATD"
      :company-list="companyList"
      :disabled="false"
      :hidden-supplier="false"
      :rct-id="rctId"
      :service-id="serviceId"
      :service-type-id="serviceTypeId"
      @copyFreight="handleCopyFreight"
      @deleteAll="handleDeleteAll"
      @deleteItem="handleDeleteItem"
      @editDebitNote="handleEditDebitNote"
    />
  </div>
</template>

<script>
import DebitNoteList from './debitNodeList.vue'

export default {
  name: 'DebitNoteExample',
  components: {
    DebitNoteList
  },
  data() {
    return {
      companyList: [], // 公司列表
      ATD: null, // 实际到达日期
      serviceTypeId: 1, // 服务类型ID
      serviceId: 123, // 服务ID
      rctId: 456 // 操作单ID
    }
  },
  methods: {
    // 编辑分账单
    handleEditDebitNote(debitNote) {
      console.log('编辑分账单:', debitNote)
      // 这里可以打开编辑对话框或跳转到编辑页面
    },

    // 复制费用
    handleCopyFreight(charge) {
      console.log('复制费用:', charge)
    },

    // 删除费用项
    handleDeleteItem(charge) {
      console.log('删除费用项:', charge)
    },

    // 删除所有费用
    handleDeleteAll() {
      console.log('删除所有费用')
    }
  }
}
</script>

<!--
组件特性：

1. 可展开行显示费用明细
   - 点击展开箭头可以查看每个分账单的详细费用信息
   - 费用明细使用原有的 charges 组件显示

2. 分账单管理
   - 如果没有创建分账单，显示"创建分账单"按钮
   - 如果已创建分账单，显示"编辑"和"删除"按钮
   - 支持分账单的状态显示（草稿、已确认、已关闭等）

3. 费用管理
   - 在展开区域中可以管理费用明细
   - 支持添加、编辑、删除费用项
   - 费用数据会实时更新分账单的应收应付金额

4. 状态标识
   - 账单状态：草稿、已确认、已关闭
   - 发票状态：未开票、已开票、已作废
   - 销账状态：未销账、部分销账、已销账

5. 响应式设计
   - 适配不同屏幕尺寸
   - 合理的列宽和布局
-->
