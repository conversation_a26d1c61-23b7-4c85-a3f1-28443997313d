package com.rich.system.service;

import com.rich.common.core.domain.entity.RsVatInvoice;

import java.util.List;

/**
 * 发票登记Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface RsVatInvoiceService {
    /**
     * 查询发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 发票登记
     */
    RsVatInvoice selectRsVatInvoiceByInvoiceId(Long invoiceId);

    /**
     * 查询发票登记列表
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记集合
     */
    List<RsVatInvoice> selectRsVatInvoiceList(RsVatInvoice rsVatInvoice);

    /**
     * 新增发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    int insertRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 修改发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    int updateRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 批量删除发票登记
     *
     * @param invoiceIds 需要删除的发票登记主键集合
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceIds(Long[] invoiceIds);

    /**
     * 删除发票登记信息
     *
     * @param invoiceId 发票登记主键
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceId(Long invoiceId);

    int changeStatus(RsVatInvoice rsVatInvoice);
}
