(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56797af4","chunk-5daa6ecc","chunk-48054984","chunk-2d0a3aa6"],{"02c1":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.scope.row.slipFile?a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-search"},on:{click:function(t){e.previewImgOpen=!0}}}):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"银行水单",visible:e.previewImgOpen,"append-to-body":"",height:"50%",width:"50%"},on:{"update:visible":function(t){e.previewImgOpen=t}}},[a("el-image",{attrs:{src:e.scope.row.slipFile}})],1)],1)},n=[],l={name:"imgPreview",props:["scope"],data:function(){return{previewImgOpen:!1}}},r=l,o=a("2877"),s=Object(o["a"])(r,i,n,!1,null,"6d267c04",null);t["default"]=s.exports},"36dc":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"f",(function(){return l})),a.d(t,"a",(function(){return r})),a.d(t,"h",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return d})),a.d(t,"d",(function(){return c})),a.d(t,"e",(function(){return p}));var i=a("b775");function n(e){return Object(i["a"])({url:"/system/bankrecord/list",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/system/bankrecord/"+e,method:"get"})}function r(e){return Object(i["a"])({url:"/system/bankrecord",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/system/bankrecord",method:"put",data:e})}function s(e){return Object(i["a"])({url:"/system/bankrecord/"+e,method:"delete"})}function d(e,t){var a={bankRecordId:e,status:t};return Object(i["a"])({url:"/system/bankrecord/changeStatus",method:"put",data:a})}function c(e){return Object(i["a"])({url:"/system/bankrecord/deleteImg",method:"delete",params:e})}function p(e){return Object(i["a"])({url:"/system/bankrecord/statistics",method:"post",data:e})}},4450:function(e,t,a){},"4ba95":function(e,t,a){"use strict";a("4450")},"4fadc":function(e,t,a){var i=a("23e7"),n=a("6f53").entries;i({target:"Object",stat:!0},{entries:function(e){return n(e)}})},"521a":function(e,t,a){"use strict";t["a"]={panels:[{index:0,name:1,height:297,width:210,paperHeader:58.5,paperFooter:831,printElements:[{options:{left:324,top:9,height:48,width:94.5,src:"/static/img/logo2.png",fit:"",right:128.25,bottom:122.25,vCenter:78.75,hCenter:96.75},printElementType:{title:"图片",type:"image"}},{options:{left:420,top:26,height:21,width:90,title:"瑞旗国际货运",fontSize:12,fontWeight:"600",lineHeight:26,coordinateSync:!1,widthHeightSync:!1,draggable:!1,qrCodeLevel:0},printElementType:{title:"自定义文本",type:"text"}},{options:{left:120,top:33,height:9.75,width:120,title:"装柜计划",fontSize:18,textAlign:"center",right:240,bottom:42,vCenter:180,hCenter:37.125,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{type:"text"}},{options:{left:30,top:61.5,height:9.75,width:208.5,title:"TO",field:"company",testData:"Leon",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:265.5,bottom:66,vCenter:161.25,hCenter:61.125},printElementType:{type:"text"}},{options:{left:27,top:108,height:9.75,width:160,title:"装货明细如下：",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{type:"text"}},{options:{left:25.5,top:223.5,height:9.75,width:508.5,title:"装柜联系",coordinateSync:!1,widthHeightSync:!1,fontSize:10,qrCodeLevel:0,right:188.5,bottom:233.25,vCenter:108.5,hCenter:228.375,field:"precarriageContact"},printElementType:{type:"text"}},{options:{left:25.5,top:256.5,height:9.75,width:514.5,title:"装柜地址",field:"precarriageAddress",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:188.5,bottom:267.75,vCenter:108.5,hCenter:262.875},printElementType:{type:"text"}},{options:{left:372,top:298.5,height:9.75,width:160,title:"总拖柜费(RMB)",right:532,bottom:308.25,vCenter:452,hCenter:303.375,field:"subtotal",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{type:"text"}},{options:{left:25.5,top:240,height:9.75,width:514.5,title:"拖柜日期",coordinateSync:!1,widthHeightSync:!1,fontSize:10,qrCodeLevel:0,right:188.5,bottom:249.75,vCenter:108.5,hCenter:244.875,field:"precarriageTime"},printElementType:{type:"text"}},{options:{left:28.5,top:327,height:9.75,width:532.5,title:"特约事项",field:"precarriageRemark",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:190,bottom:364.2421875,vCenter:110,hCenter:359.3671875},printElementType:{type:"text"}},{options:{left:28.5,top:369,height:9.75,width:55.5,title:"注意事项",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{type:"text"}},{options:{left:22.5,top:450,height:121.5,width:550,field:"truckList",right:571.75,bottom:570.75,vCenter:296.75,hCenter:510,coordinateSync:!1,widthHeightSync:!1,textAlign:"center",tableBorder:"border",tableHeaderBorder:"border",tableHeaderCellBorder:"border",tableBodyRowBorder:"noBorder",tableBodyCellBorder:"border",tableFooterCellBorder:"border",columns:[[{width:106.79960433575074,title:"柜型",field:"containerTypeCode",checked:!0,columnId:"containerTypeCode",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:114.16106817256376,title:"柜号",field:"containerNo",checked:!0,columnId:"containerNo",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:112.16258277218203,title:"封条号",field:"sealNo",checked:!0,columnId:"sealNo",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:216.8767447195035,title:"备注",field:"precarriageRemark",checked:!0,columnId:"precarriageRemark",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""}]]},printElementType:{title:"表格",type:"table",editable:!0,columnDisplayEditable:!0,columnDisplayIndexEditable:!0,columnTitleEditable:!0,columnResizable:!0,columnAlignEditable:!0,isEnableEditField:!0,isEnableContextMenu:!0,isEnableInsertRow:!0,isEnableDeleteRow:!0,isEnableInsertColumn:!0,isEnableDeleteColumn:!0,isEnableMergeCell:!0}},{options:{left:453,top:595.5,height:109.5,width:108,right:561,bottom:705,vCenter:507,hCenter:650.25,src:"/static/img/signet.png",fit:""},printElementType:{title:"图片",type:"image"}},{options:{left:12,top:786,height:49,width:49},printElementType:{title:"html",type:"html"}},{options:{left:30,top:81,height:9.75,width:208.5,title:"ATTN",field:"attn",testData:"Leon",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:267.24609375,bottom:85.74609375,vCenter:162.99609375,hCenter:80.87109375},printElementType:{type:"text"}},{options:{left:27,top:118.5,height:9,width:538.5},printElementType:{title:"横线",type:"hline"}},{options:{left:25.5,top:208.5,height:9,width:538.5,right:564,bottom:257.25,vCenter:294.75,hCenter:252.75},printElementType:{title:"横线",type:"hline"}},{options:{left:27,top:118.5,height:90,width:9},printElementType:{title:"竖线",type:"vline"}},{options:{left:27,top:141,height:9,width:538.5,right:564.75,bottom:171.75,vCenter:295.5,hCenter:167.25},printElementType:{title:"横线",type:"hline"}},{options:{left:27,top:162,height:9,width:538.5,right:567.75,bottom:192.75,vCenter:298.5,hCenter:188.25},printElementType:{title:"横线",type:"hline"}},{options:{left:27,top:186,height:9,width:538.5,right:563.25,bottom:214.5,vCenter:294,hCenter:210},printElementType:{title:"横线",type:"hline"}},{options:{left:565.5,top:120,height:90,width:9,right:576,bottom:231.75,vCenter:571.5,hCenter:186.75},printElementType:{title:"竖线",type:"vline"}},{options:{left:85.5,top:118.5,height:90,width:9,right:81,bottom:234,vCenter:76.5,hCenter:189},printElementType:{title:"竖线",type:"vline"}},{options:{left:28.5,top:126,height:9.75,width:42,title:"S/O No.:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:148.5,height:9.75,width:42,title:"起运港:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:72,bottom:178.5,vCenter:51,hCenter:173.625},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:171,height:9.75,width:42,title:"唛头:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:68.25,bottom:203.25,vCenter:47.25,hCenter:198.375},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:193.5,height:9.75,width:42,title:"合计箱量:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:69.75,bottom:225.75,vCenter:48.75,hCenter:220.875},printElementType:{title:"文本",type:"text"}},{options:{left:199.5,top:118.5,height:68,width:9,right:209.25,bottom:231,vCenter:204.75,hCenter:186,coordinateSync:!1,widthHeightSync:!1},printElementType:{title:"竖线",type:"vline"}},{options:{left:256.5,top:120,height:67,width:9,right:276.75,bottom:235.5,vCenter:272.25,hCenter:190.5,coordinateSync:!1,widthHeightSync:!1},printElementType:{title:"竖线",type:"vline"}},{options:{left:382.5,top:120,height:90,width:9,right:405,bottom:230.25,vCenter:400.5,hCenter:185.25},printElementType:{title:"竖线",type:"vline"}},{options:{left:439.5,top:120,height:90,width:9,right:447.99609375,bottom:234.99609375,vCenter:443.49609375,hCenter:189.99609375},printElementType:{title:"竖线",type:"vline"}},{options:{left:205.5,top:126,height:9.75,width:42,title:"船东:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:205.5,top:147,height:9.75,width:42,title:"还柜地点:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:205.5,top:169.5,height:9.75,width:42,title:"货物名称:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:387,top:127.5,height:9.75,width:42,title:"REF:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:431.25,bottom:159.75,vCenter:410.25,hCenter:154.875},printElementType:{title:"文本",type:"text"}},{options:{left:387,top:148.5,height:9.75,width:42,title:"件数:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:430.5,bottom:180.75,vCenter:409.5,hCenter:175.875},printElementType:{title:"文本",type:"text"}},{options:{left:387,top:169.5,height:9.75,width:42,title:"重量:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:429,bottom:201.75,vCenter:408,hCenter:196.875},printElementType:{title:"文本",type:"text"}},{options:{left:387,top:195,height:9.75,width:42,title:"体积:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:431.25,bottom:226.5,vCenter:410.25,hCenter:221.625},printElementType:{title:"文本",type:"text"}},{options:{left:441,top:307.5,height:9,width:109.5},printElementType:{title:"横线",type:"hline"}},{options:{left:99,top:369,height:9.75,width:468,title:"1、提空拍照，并检查柜子是否完好、干燥。如发现有破损、潮湿、异味或污损等情况，请勿使用，并",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:99,top:381,height:9.75,width:468,title:"即时告知我司；",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:567.75,bottom:426,vCenter:333.75,hCenter:421.125},printElementType:{title:"文本",type:"text"}},{options:{left:99,top:396,height:9.75,width:468,title:"2、提柜后请尽快将柜号、司机车牌、手机号以及代垫费用回传；",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:568.5,bottom:437.25,vCenter:334.5,hCenter:432.375},printElementType:{title:"文本",type:"text"}},{options:{left:99,top:411,height:9.75,width:468,title:"3、如果司机不能在约定时间之前到达，请务必第一时间通知我司！",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:569.25,bottom:452.25,vCenter:335.25,hCenter:447.375},printElementType:{title:"文本",type:"text"}},{options:{left:99,top:426,height:9.75,width:468,title:"4、如货柜未满，司机须同我司确认后方可封柜，谢谢！",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:567,bottom:467.25,vCenter:333,hCenter:462.375},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:598.5,height:9.75,width:57,title:"我司联系人：",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:619.5,height:9.75,width:57,title:"手机：",coordinateSync:!1,widthHeightSync:!1,textAlign:"right",qrCodeLevel:0,right:84.75,bottom:628.5,vCenter:56.25,hCenter:623.625},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:640.5,height:9.75,width:57,title:"直线：",coordinateSync:!1,widthHeightSync:!1,textAlign:"right",qrCodeLevel:0,right:85.5,bottom:649.5,vCenter:57,hCenter:644.625},printElementType:{title:"文本",type:"text"}},{options:{left:28.5,top:658.5,height:9.75,width:57,title:"传真：",coordinateSync:!1,widthHeightSync:!1,textAlign:"right",qrCodeLevel:0,right:86.25,bottom:672,vCenter:57.75,hCenter:667.125},printElementType:{title:"文本",type:"text"}},{options:{left:100.5,top:598.5,height:9.75,width:142.5,title:"董小姐",coordinateSync:!1,widthHeightSync:!1,fontWeight:"bold",qrCodeLevel:0,right:156.75,bottom:610.5,vCenter:128.25,hCenter:605.625},printElementType:{title:"文本",type:"text"}},{options:{left:100.5,top:619.5,height:9.75,width:139.5,title:"+86-20-38904",coordinateSync:!1,widthHeightSync:!1,fontWeight:"bold",qrCodeLevel:0,right:156.75,bottom:628.5,vCenter:128.25,hCenter:623.625},printElementType:{title:"文本",type:"text"}},{options:{left:100.5,top:639,height:9.75,width:138,title:"+86-20-38904715",coordinateSync:!1,widthHeightSync:!1,fontWeight:"bold",qrCodeLevel:0,right:158.25,bottom:648,vCenter:129.75,hCenter:643.125},printElementType:{title:"文本",type:"text"}},{options:{left:100.5,top:658.5,height:9.75,width:138,title:"+86-20-38904715",coordinateSync:!1,widthHeightSync:!1,fontWeight:"bold",qrCodeLevel:0,right:158.49609375,bottom:673.74609375,vCenter:129.99609375,hCenter:668.87109375},printElementType:{title:"文本",type:"text"}},{options:{left:375,top:679.5,height:9.75,width:57,title:"签名盖章：",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:431.25,bottom:689.25,vCenter:402.75,hCenter:684.375},printElementType:{title:"文本",type:"text"}},{options:{left:37.5,top:723.75,height:9.75,width:97.5,title:"Guangzhou Office:",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:88.5,bottom:735.75,vCenter:60,hCenter:730.875},printElementType:{title:"文本",type:"text"}},{options:{left:39,top:735,height:9,width:241.5},printElementType:{title:"横线",type:"hline"}},{options:{left:39,top:739.5,height:9.75,width:225,title:"Add.: Rm 302-303, No. 59 Qiao Lin Yuan, Tianhe",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:267,bottom:752.25,vCenter:154.5,hCenter:747.375},printElementType:{title:"文本",type:"text"}},{options:{left:39,top:753,height:9.75,width:225,title:"Road North, Guangzhou, China.",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:267,bottom:765,vCenter:154.5,hCenter:760.125},printElementType:{title:"文本",type:"text"}},{options:{left:39,top:766.5,height:9.75,width:225,title:"Tel: +86-20-38904750, Fax: +86-20-38904715",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:264,bottom:776.25,vCenter:151.5,hCenter:771.375},printElementType:{title:"文本",type:"text"}},{options:{left:39,top:780,height:9.75,width:225,title:"Email: <EMAIL>",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,right:264,bottom:789.75,vCenter:151.5,hCenter:784.875},printElementType:{title:"文本",type:"text"}},{options:{left:88.5,top:126,height:9.75,width:108,title:"文本",field:"soNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:88.5,top:147,height:9.75,width:108,right:202.5,bottom:156.75,vCenter:148.5,hCenter:151.875,field:"pol",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:90,top:169.5,height:9.75,width:108,title:"文本",right:198,bottom:178.5,vCenter:144,hCenter:173.625,field:"mark",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:90,top:193.5,height:9.75,width:277.5,title:"文本",right:200.25,bottom:202.5,vCenter:146.25,hCenter:197.625,field:"revenueTon",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:265.5,top:127.5,height:9.75,width:108,title:"文本",right:373.5,bottom:137.25,vCenter:319.5,hCenter:132.375,field:"carrierEnName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:265.5,top:148.5,height:9.75,width:108,title:"文本",right:374.25,bottom:158.25,vCenter:320.25,hCenter:153.375,field:"backLocation",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:265.5,top:171,height:9.75,width:108,title:"文本",right:373.5,bottom:180,vCenter:319.5,hCenter:175.125,field:"goodsNameSummary",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:450,top:127.5,height:9.75,width:108,title:"文本",right:558,bottom:137.25,vCenter:504,hCenter:132.375,field:"REF",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:450,top:150,height:9.75,width:108,title:"文本",right:557.25,bottom:159,vCenter:503.25,hCenter:154.125,field:"packageQuantity",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:450,top:171,height:9.75,width:108,title:"文本",right:558,bottom:181.5,vCenter:504,hCenter:176.625,field:"grossWeight",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:450,top:195,height:9.75,width:108,title:"文本",right:558,bottom:204.75,vCenter:504,hCenter:199.875,field:"goodsVolume",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}}],paperNumberLeft:573,paperNumberTop:9,paperNumberDisabled:!0,panelPageRule:"none",watermarkOptions:{content:"",fillStyle:"rgba(184, 184, 184, 0.3)",fontSize:"14px",rotate:25,width:200,height:200,timestamp:!1,format:"YYYY-MM-DD HH:mm"}}]}},"58cf":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));var i={rctNo:{name:"操作单号",display:"text",aggregated:!1,align:"left",width:"120"},rctCreateTime:{name:"操作日期",display:"date",aggregated:!1,align:"left",width:"120"},emergencyLevel:{name:"紧急",display:"text",aggregated:!1,align:"left",width:"80"},difficultyLevel:{name:"难度",display:"text",aggregated:!1,align:"left",width:"80"},clientId:{name:"委托单位",display:"text",aggregated:!1,align:"left",width:"120"},clientSummary:{name:"委托单位全称",display:"text",aggregated:!1,align:"left",width:"150"},clientRoleId:{name:"客户角色",display:"text",aggregated:!1,align:"left",width:"100"},clientJobNo:{name:"客户单号",display:"text",aggregated:!1,align:"left",width:"120"},relationClientIdList:{name:"相关单位",display:"text",aggregated:!1,align:"left",width:"120"},orderBelongsTo:{name:"订单所属",display:"text",aggregated:!1,align:"left",width:"100"},goodsNameSummary:{name:"货名概要",display:"text",aggregated:!1,align:"left",width:"150"},cargoTypeCodeSum:{name:"货物特征",display:"text",aggregated:!1,align:"left",width:"100"},packageQuantity:{name:"件数",display:"number",aggregated:!0,align:"right",width:"100"},grossWeight:{name:"毛重",display:"number",aggregated:!0,align:"right",width:"100"},goodsVolume:{name:"体积",display:"number",aggregated:!0,align:"right",width:"100"},goodsCurrencyCode:{name:"货值币种",display:"text",aggregated:!1,align:"left",width:"80"},goodsValue:{name:"货值",display:"number",aggregated:!0,align:"right",width:"100"},logisticsTypeId:{name:"物流类型",display:"text",aggregated:!1,align:"left",width:"100"},impExpType:{name:"进出口类型",display:"text",aggregated:!1,align:"left",width:"100"},logisticsTerms:{name:"运输条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingTerms:{name:"贸易条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingPaymentChannel:{name:"收汇方式",display:"text",aggregated:!1,align:"left",width:"100"},freightPaidWayCode:{name:"运费付于",display:"text",aggregated:!1,align:"left",width:"100"},pol:{name:"启运港",display:"text",aggregated:!1,align:"left",width:"100"},pod:{name:"卸货港",display:"text",aggregated:!1,align:"left",width:"100"},destinationPort:{name:"目的港",display:"text",aggregated:!1,align:"left",width:"100"},revenueTon:{name:"计费货量",display:"text",aggregated:!0,align:"left",width:"100"},ctnrTypeCode:{name:"箱型特征",display:"text",aggregated:!1,align:"left",width:"100"},serviceTypeIdList:{name:"服务类型",display:"text",aggregated:!1,align:"left",width:"100"},noAgreementShowed:{name:"不可套约",display:"boolean",aggregated:!1,align:"center",width:"80"},isCustomsIntransitShowed:{name:"属地清关",display:"boolean",aggregated:!1,align:"center",width:"80"},sqdExportCustomsType:{name:"报关方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdTrailerType:{name:"拖车方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdInsuranceType:{name:"投保方式",display:"text",aggregated:!1,align:"left",width:"100"},blTypeCode:{name:"提单类别",display:"text",aggregated:!1,align:"left",width:"100"},blFormCode:{name:"提单形式",display:"text",aggregated:!1,align:"left",width:"100"},sqdPodHandleAgent:{name:"换单代理",display:"text",aggregated:!1,align:"left",width:"120"},psaNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},carrierCode:{name:"承运人",display:"text",aggregated:!1,align:"left",width:"120"},polBookingAgent:{name:"订舱口",display:"text",aggregated:!1,align:"left",width:"100"},agreementTypeCode:{name:"合约类型",display:"text",aggregated:!1,align:"left",width:"100"},warehousingNo:{name:"入仓号",display:"text",aggregated:!1,align:"left",width:"120"},soNo:{name:"SO号码",display:"text",aggregated:!1,align:"left",width:"120"},blNoSum:{name:"提单号码",display:"text",aggregated:!1,align:"left",width:"120"},containersSum:{name:"柜号汇总",display:"text",aggregated:!1,align:"left",width:"150"},firstVessel:{name:"船名",display:"text",aggregated:!1,align:"left",width:"120"},firstVoyage:{name:"航次",display:"text",aggregated:!1,align:"left",width:"100"},firstCyOpenTime:{name:"开舱",display:"date",aggregated:!1,align:"left",width:"120"},firstCyClosingTime:{name:"截重",display:"date",aggregated:!1,align:"left",width:"120"},cvClosingTime:{name:"截关",display:"date",aggregated:!1,align:"left",width:"120"},siClosingTime:{name:"截补料",display:"date",aggregated:!1,align:"left",width:"120"},etd:{name:"ETD",display:"date",aggregated:!1,align:"left",width:"120"},podEta:{name:"ATD",display:"date",aggregated:!1,align:"left",width:"120"},eta:{name:"ETA",display:"date",aggregated:!1,align:"left",width:"120"},destinationPortAta:{name:"ATA",display:"date",aggregated:!1,align:"left",width:"120"},precarriageSupplierNo:{name:"拖车公司",display:"text",aggregated:!1,align:"left",width:"120"},precarriageRegionId:{name:"装运区域",display:"text",aggregated:!1,align:"left",width:"100"},precarriageAddress:{name:"装运详址",display:"text",aggregated:!1,align:"left",width:"150"},rctProcessStatus:{name:"订单进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusId:{name:"物流进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusTime:{name:"进度时间",display:"date",aggregated:!1,align:"left",width:"120"},transportStatusA:{name:"物流进度a",display:"text",aggregated:!1,align:"left",width:"100"},transportStatusB:{name:"物流进度b",display:"text",aggregated:!1,align:"left",width:"100"},warehousingTime:{name:"入仓",display:"date",aggregated:!1,align:"left",width:"120"},bookingTime:{name:"订舱",display:"date",aggregated:!1,align:"left",width:"120"},spaceCfmTime:{name:"放舱",display:"date",aggregated:!1,align:"left",width:"120"},trailerBookedTime:{name:"约车",display:"date",aggregated:!1,align:"left",width:"120"},containerCfmTime:{name:"约柜",display:"date",aggregated:!1,align:"left",width:"120"},containerLoadedTime:{name:"装柜",display:"date",aggregated:!1,align:"left",width:"120"},vesselCfmTime:{name:"配船",display:"date",aggregated:!1,align:"left",width:"120"},vgmSentTime:{name:"VGM",display:"date",aggregated:!1,align:"left",width:"120"},customDocsCfmTime:{name:"单证",display:"date",aggregated:!1,align:"left",width:"120"},customAuthorizedTime:{name:"授权",display:"date",aggregated:!1,align:"left",width:"120"},customExamineTime:{name:"查验",display:"date",aggregated:!1,align:"left",width:"120"},customReleasedTime:{name:"放行",display:"date",aggregated:!1,align:"left",width:"120"},siVerifyTime:{name:"对单",display:"date",aggregated:!1,align:"left",width:"120"},siPostedTime:{name:"补料",display:"date",aggregated:!1,align:"left",width:"120"},amsEnsPostedTime:{name:"AMS/ENS",display:"date",aggregated:!1,align:"left",width:"120"},isfEmnfPostedTime:{name:"ISF/EMNF",display:"date",aggregated:!1,align:"left",width:"120"},docStatus:{name:"文件进度",display:"text",aggregated:!1,align:"left",width:"100"},dnCompletedTime:{name:"录完应收",display:"date",aggregated:!1,align:"left",width:"120"},cnCompletedTime:{name:"录完应付",display:"date",aggregated:!1,align:"left",width:"120"},dnCfmTime:{name:"应收确认",display:"date",aggregated:!1,align:"left",width:"120"},cnCfmTime:{name:"应付确认",display:"date",aggregated:!1,align:"left",width:"120"},clientBlReleaseType:{name:"放单方式",display:"text",aggregated:!1,align:"left",width:"100"},supplierBlReleaseType:{name:"赎单方式",display:"text",aggregated:!1,align:"left",width:"100"},clientPaymentNode:{name:"收款节点",display:"text",aggregated:!1,align:"left",width:"100"},supplierPaymentNode:{name:"付款节点",display:"text",aggregated:!1,align:"left",width:"100"},estimatedRecieveTime:{name:"预收款日",display:"date",aggregated:!1,align:"left",width:"120"},estimatedPayTime:{name:"预付款日",display:"date",aggregated:!1,align:"left",width:"120"},cnAccCfmTime:{name:"应收审核",display:"date",aggregated:!1,align:"left",width:"120"},dnAccCfmTime:{name:"应付审核",display:"date",aggregated:!1,align:"left",width:"120"},cnInvIssuedTime:{name:"进项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnInvIssuedTime:{name:"销项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnReceiveSlipTime:{name:"应收水单",display:"date",aggregated:!1,align:"left",width:"120"},cnPaySlipTime:{name:"应付水单",display:"date",aggregated:!1,align:"left",width:"120"},dnInRmbBalance:{name:"折合未收",display:"number",aggregated:!0,align:"right",width:"120"},mainServicePaidStatus:{name:"主服务付款",display:"text",aggregated:!1,align:"left",width:"100"},allowReleaseBl:{name:"准许放单",display:"boolean",aggregated:!1,align:"center",width:"80"},allowGettingBl:{name:"准许赎单",display:"boolean",aggregated:!1,align:"center",width:"80"},accPromissBlReleaseTime:{name:"预计放单",display:"date",aggregated:!1,align:"left",width:"120"},accPromissBlGetTime:{name:"预计赎单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlReleaseTime:{name:"期望放单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlGetTime:{name:"期望赎单",display:"date",aggregated:!1,align:"left",width:"120"},actualBlReleaseTime:{name:"提单交付",display:"date",aggregated:!1,align:"left",width:"120"},actualBlGotTime:{name:"提单赎回",display:"date",aggregated:!1,align:"left",width:"120"},docDeliveryWay:{name:"交单方式",display:"text",aggregated:!1,align:"left",width:"100"},docTrackingRefer:{name:"邮递信息",display:"text",aggregated:!1,align:"left",width:"150"},agentNoticeTime:{name:"通知代理",display:"date",aggregated:!1,align:"left",width:"120"},qoutationInRmb:{name:"折合报价",display:"number",aggregated:!0,align:"right",width:"120"},inquiryInRmb:{name:"折合询价",display:"number",aggregated:!0,align:"right",width:"120"},estimatedProfitInRmb:{name:"预期利润",display:"number",aggregated:!0,align:"right",width:"120"},dnUsd:{name:"应收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmb:{name:"应收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnUsdBalance:{name:"未收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmbBalance:{name:"未收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnInRmb:{name:"折合应收",display:"number",aggregated:!0,align:"right",width:"120"},cnUsd:{name:"应付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmb:{name:"应付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnUsdBalance:{name:"未付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmbBalance:{name:"未付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmb:{name:"折合应付",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmbBalance:{name:"折合未付",display:"number",aggregated:!0,align:"right",width:"120"},profitUsd:{name:"USD利润",display:"number",aggregated:!0,align:"right",width:"120"},profitRmb:{name:"RMB利润",display:"number",aggregated:!0,align:"right",width:"120"},profitInRmb:{name:"折合利润",display:"number",aggregated:!0,align:"right",width:"120"},differenceInRmb:{name:"利润差额",display:"number",aggregated:!0,align:"right",width:"120"},profitRate:{name:"毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},profitRateAgg:{name:"统计毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},salesDept:{name:"所属部门",display:"getDept",aggregated:!1,align:"left",width:"100"},salesId:{name:"业务员",display:"getName",aggregated:!1,align:"left",width:"100"},salesAssistantId:{name:"业务助理",display:"getName",aggregated:!1,align:"left",width:"100"},salesObserverId:{name:"协助业务员",display:"getName",aggregated:!1,align:"left",width:"120"},statisticsSalesId:{name:"统计业务",display:"getName",aggregated:!1,align:"left",width:"120"},qoutationNo:{name:"报价单号",display:"text",aggregated:!1,align:"left",width:"120"},qoutationTime:{name:"报价日期",display:"date",aggregated:!1,align:"left",width:"120"},newBookingNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},newBookingTime:{name:"订舱日期",display:"date",aggregated:!1,align:"left",width:"120"},verifyPsaId:{name:"审核商务",display:"getName",aggregated:!1,align:"left",width:"100"},psaVerifyTime:{name:"审核时间",display:"date",aggregated:!1,align:"left",width:"120"},verifyOpLeaderId:{name:"操作主管",display:"text",aggregated:!1,align:"left",width:"100"},opLeaderVerifyTime:{name:"批示时间",display:"date",aggregated:!1,align:"left",width:"120"},opId:{name:"操作员",display:"getName",aggregated:!1,align:"left",width:"100"},opInnerRemark:{name:"操作备注",display:"text",aggregated:!1,align:"left",width:"150"},statusUpdateTime:{name:"状态日期",display:"date",aggregated:!1,align:"left",width:"120"},deleteStatus:{name:"数据状态",display:"text",aggregated:!1,align:"left",width:"100"}}},"632e":function(e,t,a){},"6aee":function(e,t,a){"use strict";a.r(t);var i,n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0}},[a("el-form-item",{attrs:{label:"单号",prop:"rctNo"}},[a("el-input",{attrs:{placeholder:"操作单号"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.rctNo,callback:function(t){e.$set(e.queryParams,"rctNo",t)},expression:"queryParams.rctNo"}})],1),a("el-form-item",{attrs:{label:"操作",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.rctOpDate,callback:function(t){e.$set(e.queryParams,"rctOpDate",t)},expression:"queryParams.rctOpDate"}})],1),a("el-form-item",{attrs:{label:"ATD",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"ATD",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.ATDDate,callback:function(t){e.$set(e.queryParams,"ATDDate",t)},expression:"queryParams.ATDDate"}})],1),a("el-form-item",{attrs:{label:"ETD",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"ETD",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.ETDDate,callback:function(t){e.$set(e.queryParams,"ETDDate",t)},expression:"queryParams.ETDDate"}})],1),a("el-form-item",{attrs:{label:"审核",prop:"pasVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核时间",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.pasVerifyTime,callback:function(t){e.$set(e.queryParams,"pasVerifyTime",t)},expression:"queryParams.pasVerifyTime"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isOpAllotted"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作分配标记"},model:{value:e.queryParams.isOpAllotted,callback:function(t){e.$set(e.queryParams,"isOpAllotted",t)},expression:"queryParams.isOpAllotted"}},[a("el-option",{attrs:{label:"未分配",value:"0"}},[e._v("未分配")]),a("el-option",{attrs:{label:"已分配",value:"1"}},[e._v("已分配")])],1)],1),a("el-form-item",{attrs:{label:"客户",prop:"clientId"}},[a("company-select",{attrs:{multiple:!1,"no-parent":!0,pass:e.queryParams.clientId,placeholder:"客户","role-client":"1",roleTypeId:1},on:{return:function(t){e.queryParams.clientId=t}}})],1),a("el-form-item",{attrs:{label:"进度",prop:"processStatusId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.queryParams.processStatusId=t}}})],1),a("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(t){e.queryParams.logisticsTypeId=t}}})],1),a("el-form-item",{attrs:{label:"启运",prop:"polIds"}},[a("location-select",{attrs:{multiple:!0,"no-parent":!0,pass:e.queryParams.polIds,placeholder:"启运港"},on:{return:function(t){e.queryParams.polIds=t}}})],1),a("el-form-item",{attrs:{label:"目的",prop:"destinationPortIds"}},[a("location-select",{attrs:{en:!0,multiple:!0,pass:e.queryParams.destinationPortIds,placeholder:"目的港"},on:{return:function(t){e.queryParams.destinationPortIds=t}}})],1),a("el-form-item",{attrs:{label:"业务",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{open:e.loadSales,select:function(t){e.queryParams.salesId=t.staffId},input:function(t){void 0==t&&(e.queryParams.salesId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return a("div",{},[e._v(" "+e._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,n=t.shouldShowCount,l=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),n?a("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{open:e.loadSales,select:function(t){e.queryParams.salesAssistantId=t.staffId},input:function(t){void 0==t&&(e.queryParams.salesAssistantId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return a("div",{},[e._v(" "+e._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,n=t.shouldShowCount,l=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),n?a("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isPsaVerified"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核标记"},model:{value:e.queryParams.isPsaVerified,callback:function(t){e.$set(e.queryParams,"isPsaVerified",t)},expression:"queryParams.isPsaVerified"}},[a("el-option",{attrs:{value:"0",label:"已审"}},[e._v("已审")]),a("el-option",{attrs:{value:"1",label:"未审"}},[e._v("未审")])],1)],1),a("el-form-item",{attrs:{label:"商务",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{open:e.loadBusinesses,select:function(t){e.queryParams.verifyPsaId=t.staffId},input:function(t){void 0==t&&(e.queryParams.verifyPsaId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return a("div",{},[e._v(" "+e._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,n=t.shouldShowCount,l=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),n?a("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"操作",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{open:e.loadOp,select:function(t){e.queryParams.opId=t.staffId},input:function(t){void 0==t&&(e.queryParams.opId=null)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return a("div",{},[e._v(" "+e._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,n=t.shouldShowCount,l=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),n?a("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1),a("el-form-item",{attrs:{label:"所属"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,placeholder:"收付路径",type:"rsPaymentTitle"},on:{return:function(t){e.queryParams.orderBelongsTo=t}}})],1),a("el-form-item",{attrs:{label:"应收"}},[a("el-switch",{on:{change:e.handleQuery},model:{value:e.queryParams.params.closedAccount,callback:function(t){e.$set(e.queryParams.params,"closedAccount",t)},expression:"queryParams.params.closedAccount"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:add"],expression:"['system:rct:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:remove"],expression:"['system:rct:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("dynamic-search",{attrs:{"search-fields":e.rctSearchFields,"config-type":"rct-search"},on:{reset:e.resetQuery,search:function(t){return e.handleQuery(t)}}})],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{on:{click:e.handleOpenAggregator}},[e._v("数据汇总")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{visible:e.openAggregator,"append-to-body":"",width:"80%"},on:{"update:visible":function(t){e.openAggregator=t}}},[a("data-aggregator-back-ground",{attrs:{"aggregate-function":e.listAggregatorRct,"data-source":e.aggregatorRctList,"config-type":"rct-agg","data-source-type":"rct","field-label-map":e.fieldLabelMap}})],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{icon:"el-icon-export",plain:"",size:"mini",type:"primary"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},e._l([{file:"打印",link:e.getReconciliationBill,templateList:["对账单"]}],(function(t,i){return a("el-popover",{key:i,attrs:{placement:"top",trigger:"click",width:"100"}},[e._l(t.templateList,(function(i,n){return a("el-button",{key:n,attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.link(i)}}},[e._v(e._s(i)+" ")])})),a("el-button",{attrs:{slot:"reference",size:"mini",type:"primary"},slot:"reference"},[e._v(e._s(t.file)+" ")])],2)})),1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rctList,stripe:""},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.dbclick}},[a("el-table-column",{attrs:{align:"left",label:"序号",type:"index",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-badge",{staticClass:"item",attrs:{value:0==t.row.opAccept?"new":""}},[a("div",{staticStyle:{width:"15px"}},[e._v(e._s(t.$index+1))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"操作单号",prop:"clientId","show-overflow-tooltip":"",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"column-text highlight-text",staticStyle:{"font-size":"18px",height:"23px"}},[e._v(e._s(t.row.rctNo)+" ")]),a("div",{staticClass:"unHighlight-text",staticStyle:{width:"100px"}},[e._v(e._s(e.parseTime(t.row.rctCreateTime,"{m}.{d}")+" "+e.emergencyLevel(t.row.emergencyLevel)+" "+e.difficultyLevel(t.row.difficultyLevel))+" ")])]}}])}),a("el-table-column",{attrs:{align:"left",label:"委托单位","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"column-text highlight-text"},[e._v(" "+e._s(t.row.clientSummary?t.row.clientSummary.split("/")[1]:null)+" ")]),a("div",{staticClass:"unHighlight-text",staticStyle:{height:"23px"}},[e._v(" "+e._s((t.row.orderBelongsTo?t.row.orderBelongsTo:"")+" "+(t.row.releaseType?e.getReleaseType(t.row.releaseType):"")+" "+t.row.paymentNode)+" ")])]}}])}),a("el-table-column",{attrs:{align:"left",label:"物流类型",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{"font-weight":"600"}},[e._v(e._s(t.row.logisticsTypeEnName))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{color:"#b7bbc2",height:"23px"}},[e._v(" "+e._s("1"===t.row.impExpType?"出口":"")+" "+e._s("2"===t.row.impExpType?"进口":"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"启运港","show-overflow-tooltip":"",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box ",staticStyle:{"font-size":"15px"}},[e._v(" "+e._s(t.row.pol?t.row.pol.split("(")[0]:t.row.pol)+" ")]),a("p",{staticClass:"unHighlight-text"},[e._v(" "+e._s(t.row.pol?"("+t.row.pol.split("(")[1]:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"目的港","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{width:"95px",overflow:"hidden"}},[a("p",{staticClass:"column-text bottom-box highlight-text",staticStyle:{}},[e._v(" "+e._s(t.row.destinationPort?t.row.destinationPort.split("(")[0]:t.row.destinationPort)+" ")]),a("p",{staticClass:"unHighlight-text"},[e._v(" "+e._s(t.row.destinationPort?"("+t.row.destinationPort.split("(")[1]:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"计费货量",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box highlight-text",staticStyle:{}},[e._v(e._s(t.row.revenueTon))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[e._v(e._s(t.row.goodsNameSummary))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"提单",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(" "+e._s(t.row.blTypeCode?t.row.blTypeCode:"")+" ")]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[e._v(" "+e._s(t.row.blFormCode?t.row.blFormCode:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订舱","show-overflow-tooltip":"",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text bottom-box",staticStyle:{"text-overflow":"ellipsis","white-space":"nowrap","font-weight":"600","font-size":"13px"}},[e._v(e._s(t.row.carrierEnName)+" "),a("span",{staticClass:"column-text unHighlight-text",staticStyle:{"font-size":"12px"}},[e._v(e._s("("+t.row.agreementTypeCode+")"))])]),a("p",{staticClass:"column-text top-box",staticStyle:{height:"23px"}},[e._v(e._s(t.row.supplierName))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"入仓与SO号"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.warehousingNo))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[e._v(e._s(t.row.soNo))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"提单与柜号","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.blNo))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{height:"23px"}},[e._v(e._s(t.row.sqdContainersSealsSum))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订单状态","show-overflow-tooltip":"",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(" "+e._s(e.processStatus(t.row.processStatusId)))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"物流进度","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.podEta?"ATD: "+e.parseTime(t.row.podEta,"{m}-{d}"):"ETD: "+e.parseTime(t.row.etd,"{m}-{d}")))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{height:"23px"}},[e._v(e._s(t.row.destinationPortEta?"ATA: "+e.parseTime(t.row.destinationPortEta,"{m}-{d}"):"ETA: "+e.parseTime(t.row.eta,"{m}-{d}")))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"文件进度","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.docStatusA?t.row.docStatusA.split(":")[0]+": "+t.row.docStatusA.split(":")[1]:""))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[e._v(" "+e._s(t.row.docStatusB?t.row.docStatusB.split(":")[0]+": "+t.row.docStatusB.split(":")[1]:""))])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"收款","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[a("bankSlip",{tag:"component",attrs:{scope:t,type:"receive"},on:{return:e.getReturn}})],1),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[e._v(e._s(e.currency(e.currency(t.row.dnUsdBalance).value,{separator:",",symbol:"$",precision:2}).format()+" / "+e.currency(e.currency(t.row.dnRmbBalance).value,{separator:",",symbol:"¥",precision:2}).format()))])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"主服务付款","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box "},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[a("bankSlip",{tag:"component",attrs:{scope:t,type:"pay"},on:{return:e.getReturn}})],1),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[e._v(e._s(e.currency(e.currency(t.row.cnUsdBalance).value,{separator:",",symbol:"$",precision:2}).format()+" / "+e.currency(e.currency(t.row.cnRmbBalance).value,{separator:",",symbol:"¥",precision:2}).format()))])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"业绩",width:"100"},scopedSlots:e._u([{key:"header",fn:function(t){return[a("div",{staticStyle:{"margin-right":"5px"}},[e._v(" 业绩 ")])]}},{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box",class:e.currency(t.row.profitInRmb).divide(t.row.dnInRmb).value<0?"warning":"",staticStyle:{"margin-right":"5px"}},[a("p",{staticClass:"column-text top-box",staticStyle:{height:"23px"}},[e._v(e._s(e.currency(t.row.profitInRmb,{separator:",",symbol:"¥",precision:2}).format()))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[e._v(e._s(e.currency(t.row.profitInRmb,{precision:2}).divide(t.row.dnInRmb).multiply(100).value+"%"))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"业务/助理","show-overflow-tooltip":"",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box",staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box",staticStyle:{overflow:"hidden"}},[e._v(e._s(e.getName(t.row.salesId)+(t.row.salesId?"/"+e.getName(t.row.salesAssistantId):e.getName(t.row.salesAssistantId))?e.getName(t.row.salesId)+(t.row.salesId?"/"+e.getName(t.row.salesAssistantId):e.getName(t.row.salesAssistantId)):null))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[e._v(" "+e._s(e.parseTime(t.row.newBookingTime,"{m}.{d}"))+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"商务审核","show-overflow-tooltip":"",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box",staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box",staticStyle:{width:"55px",overflow:"hidden"}},[e._v(e._s(e.getName(t.row.verifyPsaId)))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[e._v(" "+e._s(e.parseTime(t.row.psaVerifyTime,"{m}.{d}")+" "+(1==t.row.psaVerifyStatusId?"通过":"驳回")))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"操作员","show-overflow-tooltip":"",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box",staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box",staticStyle:{width:"55px",overflow:"hidden"}},[e._v(e._s(e.getName(t.row.opId)))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[e._v(" "+e._s(e.parseTime(t.row.statusUpdateTime,"{m}.{d}")+" "+e.processStatus(t.row.processStatusId)))])])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),a("print-preview",{ref:"preView"})],1)},l=[],r=a("5530"),o=a("c7eb"),s=a("1da1"),d=(a("4ec9"),a("d3b7"),a("3ca3"),a("ddb0"),a("e9c4"),a("d81d"),a("4de4"),a("498a"),{rctNo:{label:"单号",type:"input",placeholder:"请输入单号"},ATDDate:{label:"ATD",type:"date",placeholder:"请选择ATD日期"},rctOpDate:{label:"日期",type:"date",placeholder:"请选择操作日期"},ETDDate:{label:"ETD",type:"date",placeholder:"请选择ETD日期"},client:{label:"客户",type:"company",placeholder:"请选择客户",multiple:!1,noParent:!0,roleClient:"1",roleTypeId:1},polIds:{label:"启运港",type:"location",placeholder:"请选择港口",multiple:!0,en:!0},destinationPortIds:{label:"目的港",type:"location",placeholder:"请选择港口",multiple:!0,en:!0},salesId:{label:"业务",type:"treeselect",placeholder:"选择业务员",dataSource:"salesList",loadMethod:"loadSales",valueField:"staffId",disableBranchNodes:!0,disabledFuzzyMatching:!0,flattenSearchResults:!0,showCount:!0,normalizer:"staffNormalizer",valueSlot:!0,optionSlot:!0},salesAssistantId:{label:"助理",type:"treeselect",placeholder:"选择业务员",dataSource:"salesList",loadMethod:"loadSales",valueField:"salesAssistantId",disableBranchNodes:!0,disabledFuzzyMatching:!0,flattenSearchResults:!0,showCount:!0,normalizer:"staffNormalizer",valueSlot:!0,optionSlot:!0},verifyPsaId:{label:"商务",type:"treeselect",placeholder:"选择业务员",dataSource:"salesList",loadMethod:"loadBusinesses",valueField:"verifyPsaId",disableBranchNodes:!0,disabledFuzzyMatching:!0,flattenSearchResults:!0,showCount:!0,normalizer:"staffNormalizer",valueSlot:!0,optionSlot:!0},opId:{label:"操作",type:"treeselect",placeholder:"选择业务员",dataSource:"salesList",loadMethod:"loadOp",valueField:"opId",disableBranchNodes:!0,disabledFuzzyMatching:!0,flattenSearchResults:!0,showCount:!0,normalizer:"staffNormalizer",valueSlot:!0,optionSlot:!0},processStatusId:{type:"tree-select",label:"进度",placeholder:"请选择进度状态",treeType:"processStatus",dLoad:!0,flat:!1,multiple:!1}}),c=a("b0b8"),p=a.n(c),u=a("ca17"),g=a.n(u),h=(a("6f8d"),a("4360")),m=a("c2aa"),f=a("72f9"),y=a.n(f),b=a("bcaf"),w=a("fba1"),v=a("c1df"),S=a.n(v),x=a("6e71"),C=a("e350"),k=a("de7d"),T=a("58cf"),L=a("b635"),_=(a("521a"),a("f870")),I={panels:[{index:0,name:1,height:210,width:297,paperHeader:69,paperFooter:513.0000000000001,printElements:[{options:{left:18,top:10.5,height:48,width:94.5,src:"/static/img/logo2.png",fit:"",right:128.25,bottom:122.25,vCenter:78.75,hCenter:96.75},printElementType:{title:"图片",type:"image"}},{options:{left:111,top:26,height:29,width:202,title:"广州瑞旗国际货运代理有限公司",fontSize:14,fontWeight:"600",lineHeight:26,coordinateSync:!1,widthHeightSync:!1,draggable:!1,qrCodeLevel:0},printElementType:{title:"自定义文本",type:"text"}},{options:{left:111,top:48,height:9.75,width:339,title:'GUANGZHOU RICH SHIPPING INT"L CO.,LTD.',coordinateSync:!1,widthHeightSync:!1,fontSize:12,qrCodeLevel:0,right:512.25,bottom:43.5,vCenter:342.75,hCenter:38.625},printElementType:{title:"文本",type:"text"}},{options:{left:12,top:60,height:9,width:812,coordinateSync:!1,widthHeightSync:!1,right:576.75,bottom:57.75,vCenter:294.75,hCenter:53.25},printElementType:{title:"横线",type:"hline"}},{options:{left:699,top:40.5,height:9.75,width:120,title:"[DEBIT NOTE]",fontSize:18,textAlign:"center",right:351,bottom:131.25,vCenter:291,hCenter:126.375,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{type:"text"}},{options:{left:13.5,top:75,height:9.75,width:325.5,title:"TO",field:"company",testData:"Leon",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:339,bottom:84.75,vCenter:176.25,hCenter:79.875},printElementType:{type:"text"}},{options:{left:13.5,top:90,height:9.75,width:200,title:"(The Rates Details is as following)：",right:230.75,bottom:177.75,vCenter:130.75,hCenter:172.875,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"这是更新后的元素",type:"text"}},{options:{left:13.5,top:117,height:307.5,width:808.5,field:"rctList",right:562,bottom:192.75,vCenter:287,hCenter:154.5,coordinateSync:!1,widthHeightSync:!1,textAlign:"center",tableBorder:"noBorder",tableHeaderBorder:"topBottomBorder",tableHeaderCellBorder:"noBorder",tableBodyRowBorder:"noBorder",tableBodyCellBorder:"noBorder",tableFooterCellBorder:"border",columns:[[{width:99.24935545048237,title:"参考号</br>Ref No.",field:"rctNo",checked:!0,columnId:"rctNo",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:69.04126387493694,title:"计费吨</br>Rev Ton",field:"revenueTon",checked:!0,columnId:"revenueTon",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:79.83901652837406,title:"柜号</br>Ctnr No.",field:"sqdContainersSealsSum",checked:!0,columnId:"sqdContainersSealsSum",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:71.90065084392698,title:"启运港</br>POL",field:"pol",checked:!0,columnId:"pol",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:79.00324781499651,title:"目的港</br>Destination",field:"destinationPort",checked:!0,columnId:"destinationPort",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:61.00359340190148,title:"预计装船</br>ETD",field:"etd",checked:!0,columnId:"etd",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:55.628310089480465,title:"预计到达</br>ETA",field:"eta",checked:!0,columnId:"eta",fixed:!1,rowspan:1,colspan:1},{width:77.6753883165497,title:"应收(USD)</br>Receivable ",field:"dnUsd",checked:!0,columnId:"dnUsd",fixed:!1,rowspan:1,colspan:1},{width:72.28307074326402,title:"应收(RMB)</br>Receivable",field:"dnRmb",checked:!0,columnId:"dnRmb",fixed:!1,rowspan:1,colspan:1},{width:75.52182043244198,title:"未收(USD)</br>Balance",field:"dnUsdBalance",checked:!0,columnId:"dnUsdBalance",fixed:!1,rowspan:1,colspan:1},{width:67.35428250364538,title:"未收(RMB)</br>Balance",field:"dnRmbBalance",checked:!0,columnId:"dnRmbBalance",fixed:!1,rowspan:1,colspan:1}]]},printElementType:{title:"表格",type:"table",editable:!0,columnDisplayEditable:!0,columnDisplayIndexEditable:!0,columnTitleEditable:!0,columnResizable:!0,columnAlignEditable:!0,isEnableEditField:!0,isEnableContextMenu:!0,isEnableInsertRow:!0,isEnableDeleteRow:!0,isEnableInsertColumn:!0,isEnableDeleteColumn:!0,isEnableMergeCell:!0}},{options:{left:6,top:520.5,height:10,width:535.5,right:546,bottom:790,vCenter:282.75,hCenter:785},printElementType:{title:"横线",type:"hline"}},{options:{left:6,top:526.5,height:9.75,width:564,title:"Address: Room 1805, 18/F, Southern Sercurities Building, Tiyu Road East No. 140, Tinahe, Guangzhou, China.",coordinateSync:!1,widthHeightSync:!1,fontSize:10,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:12,top:786,height:49,width:49},printElementType:{title:"html",type:"html"}},{options:{left:6,top:540,height:9.75,width:151.5,title:"Tel: +86-20-38904711, 38904770",right:135.75,bottom:825.75,vCenter:75.75,hCenter:820.875,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:370.5,top:799.5,height:9.75,width:120,title:"Web: www.RICHGZ.com",right:489.75,bottom:825,vCenter:429.75,hCenter:820.125,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:181.5,top:540,height:9.75,width:130.5,title:"Email: <EMAIL>",right:311.25,bottom:825.75,vCenter:251.25,hCenter:820.875,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:699,top:21,height:9.75,width:120,title:"对账单汇总",fontSize:18,textAlign:"center",right:351,bottom:131.25,vCenter:291,hCenter:126.375,coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{type:"text"}},{options:{left:34.5,top:444,height:9,width:800,coordinateSync:!1,widthHeightSync:!1,right:820.25,bottom:465,vCenter:420.25,hCenter:460.5},printElementType:{title:"横线",type:"hline"}},{options:{left:447,top:483,height:9.75,width:72,title:"合计（Total）：",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:558,top:481.5,height:27,width:120,title:"文本",right:678,bottom:489.75,vCenter:618,hCenter:476.25,field:"receivable",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,textAlign:"right",qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:700.5,top:481.5,height:25.5,width:120,title:"文本",right:819,bottom:487.5,vCenter:759,hCenter:474.75,field:"balance",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,textAlign:"right",qrCodeLevel:0},printElementType:{title:"文本",type:"text"}}],paperNumberLeft:565,paperNumberTop:562.5,rotate:!0,watermarkOptions:{}}]},N=a("4f70"),q=a("cd43"),R={name:"Rct",components:{DataAggregatorBackGround:q["default"],DynamicSearch:N["a"],printPreview:_["default"],DataAggregator:k["default"],CompanySelect:x["a"],Treeselect:g.a,bankSlip:b["default"]},data:function(){return{rctSearchFields:d,fieldLabelMap:T["a"],yourDataSource:[{date:"2023-01-01",category:"Electronics",region:"North",sales:1e3,profit:200}],showLeft:3,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,statisticsOp:[],total:0,salesId:null,verifyPsaId:null,salesAssistantId:null,opId:null,belongList:[],opList:[],businessList:[],openAggregator:!1,rctList:[],aggregatorRctList:[],queryParams:{pageNum:1,pageSize:20,params:new Map},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=!1;this.$route.query.no?(this.queryParams.newBookingNo=this.$route.query.no,this.getList().then((function(){e=!0}))):this.getList().then((function(){e=!0})),e&&(this.loadSales(),this.loadOp(),this.loadBusinesses()),this.loadStaffList()},computed:{moment:function(){return S.a}},methods:{listAggregatorRct:function(e){return e.config=JSON.stringify(e.config),this.queryParams.params=e,Object(m["j"])(this.queryParams)},handleOpenAggregator:function(){this.openAggregator=!0},getReconciliationBill:function(){var e={},t=0,a=0,n=0,l=0;e.receivable="",e.balance="",e.company=this.rctList[0].clientSummary.split("/")[1],e.rctList=this._.cloneDeep(this.rctList),e.rctList.map((function(e){return e.etd=Object(w["f"])(e.etd,"{m}-{d}"),e.eta=Object(w["f"])(e.eta,"{m}-{d}"),e.pol=e.pol.split("(")[0],e.destinationPort=e.destinationPort.split("(")[0],console.log(e.dnRmbBalance),t=y()(t).add(e.dnUsd),e.dnUsd=y()(y()(e.dnUsd).value,{separator:",",symbol:"",precision:2}).format(),a=y()(t).add(e.dnRmb),e.dnRmb=y()(y()(e.dnRmb).value,{separator:",",symbol:"",precision:2}).format(),n=y()(n).add(e.dnUsdBalance),e.dnUsdBalance=y()(y()(e.dnUsdBalance).value,{separator:",",symbol:"",precision:2}).format(),l=y()(l).add(e.dnRmbBalance),e.dnRmbBalance=y()(y()(e.dnRmbBalance).value,{separator:",",symbol:"",precision:2}).format(),e})),t=y()(y()(t).value,{separator:",",symbol:"$",precision:2}).format(),a=y()(y()(a).value,{separator:",",symbol:"￥",precision:2}).format(),n=y()(y()(n).value,{separator:",",symbol:"$",precision:2}).format(),l=y()(y()(l).value,{separator:",",symbol:"￥",precision:2}).format(),e.receivable=t+"</br>"+a,e.balance=n+"</br>"+l,i=new L["c"].PrintTemplate({template:I}),this.$refs.preView.print(i,e)},checkRole:C["b"],parseTime:w["f"],getReturn:function(){},currency:y.a,tableRowClassName:function(e){var t=e.row;e.rowIndex;return 0==t.opAccept?"unconfirmed":""},sqdDocDeliveryWay:function(e){return 1==e?" 境外快递":2==e?" 境内快递":3==e?" 跑腿":4==e?" 业务送达":5==e?" 客户自取":6==e?" QQ":7==e?" 微信":8==e?" 电邮":9==e?" 公众号":10==e?" 承运人系统":11==e?" 订舱口系统":12==e?" 第三方系统":""},getReleaseType:function(e){return 1==e?"月结":2==e?"押放":3==e?"票结":4==e?"签放":5==e?"订金":6==e?"预付":7==e?"扣货":9==e?"居间":""},getName:function(e){if(null!==e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t&&void 0!==t)return t.staffFamilyLocalName+t.staffGivingLocalName+t.staffGivingEnName}return""},logisticsPaymentTerms:function(e){return 1==e?"月结":2==e?"押单":3==e?"此票结清":4==e?"经理签单":5==e?"预收订金":6==e?"全额预付":7==e?"扣货":8==e?"背靠背":""},emergencyLevel:function(e){return 0==e?"预定":1==e?"当天":2==e?"常规":3==e?"紧急":4==e?"立即":""},difficultyLevel:function(e){return 0==e?"简易":1==e?"标准":2==e?"高级":3==e?"特别":""},processStatus:function(e){return 1==e?"等待":2==e?"进行":3==e?"变更":4==e?"异常":5==e?"质押":6==e?"确认":7==e?"完成":8==e?"取消":9==e?"驳回":10==e?"回收":""},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?h["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?h["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?h["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},loadStaffList:function(){var e=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?h["a"].dispatch("getAllRsStaffList").then((function(){e.staffList=e.$store.state.data.allRsStaffList})):this.staffList=this.$store.state.data.allRsStaffList},getList:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,e.queryParams.permissionLevel=e.$store.state.user.permissionLevelList.C,t.next=4,Object(m["k"])(e.queryParams).then((function(t){e.rctList=t.rows,e.total=t.total,e.loading=!1}));case 4:case"end":return t.stop()}}),t)})))()},handleQuery:function(e){this.queryParams=Object(r["a"])(Object(r["a"])(Object(r["a"])({},this.queryParams),e),{},{pageNum:1}),this.queryParams.rctNo=this.queryParams.rctNo?this.queryParams.rctNo.trim():this.queryParams.rctNo,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.rctId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.$tab.openPage("操作单","/opprocess/opdetail",{})},handleUpdate:function(e){this.$tab.openPage("操作单","/opprocess/opdetail",{rId:e.rctId})},dbclick:function(e,t,a){this.$tab.openPage("操作单","/opprocess/opdetail",{rId:e.rctId,type:"op"})},handleDelete:function(e){var t=this,a=e.rctId||this.ids;this.$confirm('是否确认删除操作单列表编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(m["d"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/rct/export",Object(r["a"])({},this.queryParams),"rct_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+p.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+p.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+p.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}}}},O=R,P=(a("70e2"),a("2877")),E=Object(P["a"])(O,n,l,!1,null,"317c36ec",null);t["default"]=E.exports},"70e2":function(e,t,a){"use strict";a("83d66")},"83d66":function(e,t,a){},aafe:function(e,t,a){"use strict";a("632e")},bcaf:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],attrs:{size:"mini",type:"text",disabled:0==e.scope.row.opAccept},on:{click:e.openBankSlip}},[e._v(" "+e._s("["+e.currency("pay"===e.type?e.scope.row.cnInRmb:e.scope.row.dnInRmb,{separator:",",symbol:"¥",precision:2}).format()+("pay"===e.type?" "+(e.scope.row.sqdDnPaySlipStatus?e.scope.row.sqdDnPaySlipStatus:"-"):" "+(e.scope.row.sqdDnReceiveSlipStatus?e.scope.row.sqdDnReceiveSlipStatus:"-"))+"]")+" ")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"pay"===e.type?"付款水单":"收款水单",visible:e.bankSlipOpen,"append-to-body":"","destroy-on-close":"",height:"60%",width:"60%"},on:{"update:visible":function(t){e.bankSlipOpen=t},open:e.loadCompanyOptions,close:function(t){e.showDetail=!1}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{"margin-top":"20px"},attrs:{data:e.bankrecordList,"highlight-current-row":"",stripe:""}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"银行流水",prop:"bankRecordNo"}}),a("el-table-column",{attrs:{align:"center",label:"收支",prop:"isRecievingOrPaying",width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.isRecievingOrPaying?"付":"收"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"所属公司",prop:"sqdPaymentTitleCode",width:"50"}}),a("el-table-column",{attrs:{align:"center",label:"银行账户",prop:"bankAccountCode",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?a("div",[e._v(" "+e._s(t.row.bankAccountCode)+" ")]):a("tree-select",{staticClass:"edit",class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,flat:!1,multiple:!1,pass:t.row.bankAccountCode,placeholder:"银行账户",type:"companyAccount"},on:{return:function(e){t.row.bankAccountCode=e},returnData:function(e){t.row.sqdPaymentTitleCode=e.sqdBelongToCompanyCode}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"结算公司",prop:"sqdClearingCompanyShortname"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{class:1==t.row.isBankRecordLocked&&1==t.row.slipConfirmed?"":"edit",attrs:{disabled:1==t.row.isBankRecordLocked&&1==t.row.slipConfirmed,placeholder:"请选择"},model:{value:t.row.clearingCompanyId,callback:function(a){e.$set(t.row,"clearingCompanyId",a)},expression:"scope.row.clearingCompanyId"}},e._l(e.clients,(function(e){return a("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"银行币种",prop:"bankCurrencyCode",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?a("div",[e._v(" "+e._s(t.row.bankCurrencyCode)+" ")]):a("tree-select",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,pass:t.row.bankCurrencyCode,placeholder:"币种",type:"currency"},on:{return:function(e){t.row.bankCurrencyCode=e}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"水单金额",prop:"slipAmount"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?a("div",[e._v(" "+e._s(t.row.slipAmount)+" ")]):a("el-input",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isBankSlipConfirmed,placeholder:"水单金额"},model:{value:t.row.slipAmount,callback:function(a){e.$set(t.row,"slipAmount",a)},expression:"scope.row.slipAmount"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"水单"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("div",{staticStyle:{display:"flex"}},[0==t.row.slipConfirmed?a("el-upload",{ref:"bankSlipUpload",staticClass:"upload-demo",staticStyle:{flex:"1"},attrs:{action:e.uploadUrl,"auto-upload":!1,disabled:!t.row.bankRecordNo,"http-request":e.customHttpRequest,"on-change":e.handleChange,"on-error":e.handleError,"on-success":e.handleSuccess,"show-file-list":!1,action:"xxx"}},[a("el-button",{staticStyle:{color:"rgb(103, 194, 58)"},attrs:{icon:"el-icon-top-right",type:"text"}})],1):e._e(),t.row.slipFile||t.row.slipFile&&t.row.slipConfirmed?a("img-preview",{staticStyle:{flex:"1"},attrs:{scope:t}}):e._e(),t.row.slipFile&&0==t.row.slipConfirmed?a("el-button",{staticStyle:{flex:"1",color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(a){return e.deleteBankSlip(t.row)}}}):e._e()],1)])]}}])}),a("el-table-column",{attrs:{align:"center",label:"水单日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.slipConfirmed?a("div",[e._v(" "+e._s(e.parseTime(t.row.slipDate,"{y}-{m}-{d}"))+" ")]):a("el-date-picker",{staticClass:"edit",class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,clearable:"","default-time":"12:00:00",placeholder:"银行时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.row.slipDate,callback:function(a){e.$set(t.row,"slipDate",a)},expression:"scope.row.slipDate"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"相关操作单号",prop:"sqdRaletiveRctList"}}),a("el-table-column",{attrs:{label:"水单确认",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.slipConfirmed?"√":"-")+" ")]}}])}),"receive"===e.type?a("el-table-column",{attrs:{align:"center",label:"实收金额",prop:"actualBankRecievedAmount"}}):e._e(),"pay"===e.type?a("el-table-column",{attrs:{align:"center",label:"实付金额",prop:"actualBankPaidAmount"}}):e._e(),a("el-table-column",{attrs:{align:"center",label:"银行时间",prop:"bankRecordTime",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.bankRecordTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"流水审核",prop:"isBankRecordLocked"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.isBankRecordLocked?"√":"-"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.slipConfirmed?a("el-button",{attrs:{icon:"el-icon-top-right",type:"text"},on:{click:function(a){return e.submitForm(t.row)}}}):e._e(),0==t.row.slipConfirmed?a("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}}):e._e()]}}])})],1),a("el-button",{attrs:{type:"text"},on:{click:e.handleAdd}},[e._v("[+]")]),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1)},n=[],l=a("c7eb"),r=a("1da1"),o=(a("d81d"),a("14d9"),a("b0c0"),a("99af"),a("d3b7"),a("72f9")),s=a.n(o),d=a("ca17"),c=a.n(d),p=a("6e71"),u=a("36dc"),g=a("fba1"),h=a("b775"),m=a("02c1"),f=a("c2aa"),y=a("b0b8"),b=a.n(y),w=a("aff7"),v={name:"bankSlip",components:{ImgPreview:m["default"],CompanySelect:p["a"],Treeselect:c.a},props:["scope","type"],data:function(){return{queryParams:{pageNum:1,pageSize:20},form:{},rules:{},open:!1,showDetail:!1,bankSlipOpen:!1,bankrecordList:[],total:0,loading:!1,companyList:[],fileList:[],imageFile:null,uploadUrl:"/system/bankrecord/upload",imgUrl:"",bankSlipPreview:!1,previewImgOpen:!1,clients:[]}},beforeMount:function(){},computed:{isLocked:function(){return 1==this.form.isBankRecordLocked},isBankSlipConfirmed:function(){return 1==this.form.slipConfirmed}},methods:{loadCompanyOptions:function(){var e=this,t=[this.scope.row.clientId];this.scope.row.relationClientIdList.split(",").length>0&&this.scope.row.relationClientIdList.split(",").map((function(e){return parseInt(e)?t.push(parseInt(e)):null})),Object(w["h"])({companyIds:t}).then((function(t){e.clients=t.rows}))},selectCompany:function(e,t){e.clearingCompanyId=t.companyId,e.sqdClearingCompanyShortname=t.companyShortName},deleteBankSlip:function(e){var t=this;return Object(r["a"])(Object(l["a"])().mark((function a(){return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,Object(u["d"])({url:e.slipFile});case 3:a.next=9;break;case 5:return a.prev=5,a.t0=a["catch"](0),a.next=9,Object(u["h"])({bankRecordId:e.bankRecordId,slipFile:null,isRecievingOrPaying:"pay"===t.type?"1":"0",bankRecordNo:e.bankRecordNo});case 9:return a.next=11,t.getList();case 11:case"end":return a.stop()}}),a,null,[[0,5]])})))()},customHttpRequest:function(e){var t=this,a=new FormData;a.append("file",e.file),Object(h["a"])({url:"/system/bankrecord/uploadImg",method:"post",data:a}).then((function(a){e.onSuccess(a,e.file),t.imgUrl=a.url,t.form.slipFile=a.url,e.row.slipFile=a.url})).catch((function(t){e.onError(t)}))},handleChange:function(e,t){var a=e.name.substring(e.name.lastIndexOf(".")),i="".concat(this.form.bankRecordNo).concat(a);this.imageFile=new File([e.raw],i,{type:e.type}),this.$message.success("图片已选择")},handleSuccess:function(e,t,a){this.form.slipFile=e.url},handleError:function(e,t,a){this.$message.error("Upload failed:",e)},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)},handleExceed:function(e,t){this.$message.warning("当前限制选择 3 个文件，本次选择了 ".concat(e.length," 个文件，共选择了 ").concat(e.length+t.length," 个文件"))},beforeRemove:function(e,t){return this.$confirm("确定移除 ".concat(e.name,"？"))},uploadImage:function(e){var t=this;return new Promise((function(a,i){t.customHttpRequest({file:t.imageFile,onSuccess:function(e){t.handleSuccess(e),a(e)},onError:function(e){t.handleError(e),i(e)},row:e})}))},submitForm:function(e){var t=this;return Object(r["a"])(Object(l["a"])().mark((function a(){return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.imageFile){a.next=3;break}return a.next=3,t.uploadImage(e);case 3:console.log(e),Object(u["h"])(e).then((function(e){t.$modal.msgSuccess("修改成功"),t.getList()})),"pay"===t.type?Object(f["s"])({rctId:t.scope.row.rctId,sqdDnPaySlipStatus:"√"}).then((function(e){})):Object(f["s"])({rctId:t.scope.row.rctId,sqdDnReceiveSlipStatus:"√"}).then((function(e){}));case 6:case"end":return a.stop()}}),a)})))()},parseTime:g["f"],currency:s.a,openBankSlip:function(){var e=this;return Object(r["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getList();case 2:e.bankSlipOpen=!0;case 3:case"end":return t.stop()}}),t)})))()},getList:function(){var e=this;this.loading=!0,Object(u["g"])({clearingCompanyId:this.scope.row.clientId,isRecievingOrPaying:"pay"===this.type?"1":"0",rctNo:this.scope.row.rctNo}).then((function(t){t.rows.map((function(t){return t.clients=e.clients})),e.bankrecordList=t.rows,e.total=t.total?t.total:0,e.loading=!1,t.rows&&0===t.rows.length&&"receive"===e.type&&null===e.scope.row.sqdDnReceiveSlipStatus&&Object(f["s"])({rctId:e.scope.row.rctId,sqdDnReceiveSlipStatus:"-"}).then((function(e){})),t.rows&&0===t.rows.length&&"pay"===e.type&&null===e.scope.row.sqdDnPaySlipStatus&&Object(f["s"])({rctId:e.scope.row.rctId,sqdDnPaySlipStatus:"-"}).then((function(e){}))}))},selectBankAccount:function(e){console.log(e),this.form.sqdPaymentTitleCode=e.sqdBelongToCompanyCode},handleDelete:function(e){var t=this,a=e.bankRecordId||this.ids;this.$confirm("是否确认删除？","提示",{customClass:"modal-confirm"}).then((function(){return Object(u["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleUpdate:function(e){var t=this;this.add=!1,this.reset();var a=e.bankRecordId||this.ids;Object(u["f"])(a).then((function(e){t.form=e.data,t.form.chargeType="订单",t.open=!0,t.title="查看银行流水-销账明细",t.companyList=[e.companyList]}))},verify:function(){var e=this;null!==this.form.clearingCompanyId?(this.form.isBankRecordLocked=1,Object(u["h"])(this.form).then((function(t){e.$message.success("修改成功")}))):this.$message.warning("请输入结算公司")},reset:function(){this.form={bankRecordId:null,isRecievingOrPaying:null,sqdPaymentTitleCode:null,bankAccountCode:null,clearingCompanyId:null,sqdClearingCompanyShortname:null,chargeTypeId:null,chargeDescription:null,bankCurrencyCode:null,actualBankRecievedAmount:null,actualBankPaidAmount:null,bankRecievedHandlingFee:null,bankPaidHandlingFee:null,bankRecievedExchangeLost:null,bankPaidExchangeLost:null,sqdBillRecievedAmount:null,sqdBillPaidAmount:null,billRecievedWriteoffAmount:null,billPaidWriteoffAmount:null,sqdBillRecievedWriteoffBalance:null,sqdBillPaidWriteoffBalance:null,writeoffStatus:"0",bankRecordTime:null,paymentTypeCode:null,voucherNo:null,invoiceNo:null,bankRecordRemark:null,bankRecordByStaffId:null,bankRecordUpdateTime:null,isBankRecordLocked:null,isWriteoffLocked:null,sqdChargeIdList:null,sqdRaletiveRctList:null,sqdRaletiveInvoiceList:null,sqdRsStaffId:null,writeoffRemark:null,writeoffStaffId:null,writeoffTime:null,chargeType:"订单"},this.resetForm("form")},handleAdd:function(){var e=this,t={};t.isRecievingOrPaying="pay"===this.type?"1":"0",t.paymentTypeCode="T/T",t.chargeType="订单",t.chargeTypeId=2,t.clearingCompanyId=this.scope.row.clientId,t.sqdClearingCompanyShortname=this.scope.row.clientSummary.split("/")[1],t.sqdRaletiveRctList=this.scope.row.rctNo,Object(u["a"])(t).then((function(t){e.form=t.data,e.$modal.msgSuccess("新增成功"),e.getList()}))},isRecievingOrPayingNormalizer:function(e){return{id:e.value,label:e.label}},companyNormalizer:function(e){return{id:e.companyId,label:(null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:"")+","+b.a.getFullChars((null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:""))}},queryCompany:function(e){this.queryParams.company=e.companyShortName,this.queryParams.companyId=e.companyId,this.handleQuery()}}},S=v,x=(a("d30c"),a("2877")),C=Object(x["a"])(S,i,n,!1,null,"6ed56fd2",null);t["default"]=C.exports},c2aa:function(e,t,a){"use strict";a.d(t,"k",(function(){return n})),a.d(t,"j",(function(){return l})),a.d(t,"l",(function(){return r})),a.d(t,"n",(function(){return o})),a.d(t,"m",(function(){return s})),a.d(t,"e",(function(){return d})),a.d(t,"c",(function(){return c})),a.d(t,"r",(function(){return p})),a.d(t,"s",(function(){return u})),a.d(t,"d",(function(){return g})),a.d(t,"b",(function(){return h})),a.d(t,"g",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"i",(function(){return y})),a.d(t,"p",(function(){return b})),a.d(t,"q",(function(){return w})),a.d(t,"h",(function(){return v})),a.d(t,"o",(function(){return S}));var i=a("b775");function n(e){return Object(i["a"])({url:"/system/rct/list",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/system/rct/op",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/system/rct/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/system/rct",method:"post",data:e})}function p(e){return Object(i["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/system/rct",method:"put",data:e})}function g(e){return Object(i["a"])({url:"/system/rct/"+e,method:"delete"})}function h(e){return Object(i["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function m(){return Object(i["a"])({url:"/system/rct/mon",method:"get"})}function f(){return Object(i["a"])({url:"/system/rct/CFmon",method:"get"})}function y(){return Object(i["a"])({url:"/system/rct/RSWHMon",method:"get"})}function b(e){return Object(i["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function w(e){return Object(i["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function v(e){return Object(i["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function S(e){return Object(i["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},cd43:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"data-aggregator"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:(e.showResult,10)}},[a("el-card",{staticClass:"config-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总配置")])])]},proxy:!0}])},[a("el-form",{staticClass:"edit",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"速查名称",required:""}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"请输入速查名称"},model:{value:e.config.name,callback:function(t){e.$set(e.config,"name",t)},expression:"config.name"}})],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.saveConfig}},[e._v("[↗]")]),a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.loadConfigs}},[e._v("[...]")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组依据"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"操作单号"},model:{value:e.config.primaryField,callback:function(t){e.$set(e.config,"primaryField",t)},expression:"config.primaryField"}},e._l(e.availableFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.matchOptions.exact,callback:function(t){e.$set(e.config.matchOptions,"exact",t)},expression:"config.matchOptions.exact"}},[e._v("精确匹配")]),a("el-checkbox",{model:{value:e.config.matchOptions.caseSensitive,callback:function(t){e.$set(e.config.matchOptions,"caseSensitive",t)},expression:"config.matchOptions.caseSensitive"}},[e._v("区分大小写")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组日期"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"分组日期"},model:{value:e.config.dateField,callback:function(t){e.$set(e.config,"dateField",t)},expression:"config.dateField"}},e._l(e.dateFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.dateOptions.convertToNumber,callback:function(t){e.$set(e.config.dateOptions,"convertToNumber",t)},expression:"config.dateOptions.convertToNumber"}},[e._v("转换为数字")]),a("el-radio-group",{staticStyle:{display:"flex","line-height":"26px"},model:{value:e.config.dateOptions.formatType,callback:function(t){e.$set(e.config.dateOptions,"formatType",t)},expression:"config.dateOptions.formatType"}},[a("el-radio",{attrs:{label:"year"}},[e._v("按年")]),a("el-radio",{attrs:{label:"month"}},[e._v("按月")]),a("el-radio",{attrs:{label:"day"}},[e._v("按天")])],1)],1)],1)],1),a("el-form-item",{attrs:{label:"显示方式"}},[a("el-checkbox",{staticStyle:{"padding-left":"5px"},model:{value:e.config.showDetails,callback:function(t){e.$set(e.config,"showDetails",t)},expression:"config.showDetails"}},[e._v("含明细")])],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.config.fields,border:""}},[a("el-table-column",{attrs:{align:"center",label:"序号",type:"index",width:"60"}}),a("el-table-column",{attrs:{label:"表头名称","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},on:{change:function(a){return e.handleFieldSelect(t.$index)}},model:{value:t.row.fieldKey,callback:function(a){e.$set(t.row,"fieldKey",a)},expression:"scope.row.fieldKey"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)]}}])}),a("el-table-column",{attrs:{label:"排序",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.sort,callback:function(a){e.$set(t.row,"sort",a)},expression:"scope.row.sort"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"∧",value:"asc"}}),a("el-option",{attrs:{label:"∨ ",value:"desc"}})],1)]}}])}),a("el-table-column",{attrs:{label:"汇总方式",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.isAggregatable(t.row.fieldKey)},model:{value:t.row.aggregation,callback:function(a){e.$set(t.row,"aggregation",a)},expression:"scope.row.aggregation"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"计数",value:"count"}}),a("el-option",{attrs:{label:"求和",value:"sum"}}),a("el-option",{attrs:{label:"平均值",value:"avg"}}),a("el-option",{attrs:{label:"最大值",value:"max"}}),a("el-option",{attrs:{label:"最小值",value:"min"}}),a("el-option",{attrs:{label:"方差",value:"variance"}})],1)]}}])}),a("el-table-column",{attrs:{label:"显示格式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.format,callback:function(a){e.$set(t.row,"format",a)},expression:"scope.row.format"}},[a("el-option",{attrs:{label:"-",value:"none"}}),"date"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"YYYYMM",value:"YYYYMM"}}),a("el-option",{attrs:{label:"MM-DD",value:"MM-DD"}}),a("el-option",{attrs:{label:"YYYY-MM-DD",value:"YYYY-MM-DD"}})]:e._e(),"number"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"0.00",value:"decimal"}}),a("el-option",{attrs:{label:"0.00%",value:"percent"}}),a("el-option",{attrs:{label:"¥0.00",value:"currency"}}),a("el-option",{attrs:{label:"$0.00",value:"usd"}}),a("el-option",{attrs:{label:"0不显示",value:"hideZero"}})]:e._e()],2)]}}])}),a("el-table-column",{attrs:{label:"零值隐藏",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{disabled:"number"!==e.getFieldDisplay(t.row.fieldKey)},model:{value:t.row.hideZeroValues,callback:function(a){e.$set(t.row,"hideZeroValues",a)},expression:"scope.row.hideZeroValues"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[a("el-button",{attrs:{disabled:0===t.$index,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"up")}}},[e._v("[∧] ")]),a("el-button",{attrs:{disabled:t.$index===e.config.fields.length-1,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"down")}}},[e._v("[∨] ")]),a("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",size:"mini",type:"text"},on:{click:function(a){return e.removeField(t.$index)}}})],1)]}}])})],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("el-button",{attrs:{plain:"",type:"text"},on:{click:e.addField}},[e._v("[ + ]")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleServerAggregate}},[e._v("分类汇总")]),a("el-button",{on:{click:e.resetConfig}},[e._v("重置")])],1)],1)],1)],1),e.showResult?a("el-col",{attrs:{span:14}},[a("el-card",{staticClass:"result-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总结果")]),a("div",{staticClass:"operations"},[a("el-switch",{staticStyle:{"margin-right":"15px"},attrs:{"active-text":"横向","inactive-text":"纵向"},model:{value:e.isLandscape,callback:function(t){e.isLandscape=t},expression:"isLandscape"}}),a("el-button",{attrs:{size:"small"},on:{click:e.printTable}},[e._v("打印")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.exportToPDF}},[e._v("导出PDF")])],1)])]},proxy:!0}],null,!1,1080603383)},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"resultTable",staticStyle:{width:"100%"},attrs:{data:e.processedData,"summary-method":e.getSummary,border:"","show-summary":""}},[a("el-table-column",{attrs:{align:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].align:"left",label:e.groupFieldName,width:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].width:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatGroupKey(t.row.groupKey))+" ")]}}],null,!1,2877943199)}),e._l(e.config.fields,(function(t,i){return[t.fieldKey?a("el-table-column",{key:t.fieldKey+"_"+i,attrs:{align:e.getColumnAlign(t.fieldKey),label:e.getResultLabel(t),width:e.getColumnWidth(t.fieldKey)},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.formatCellValue(a.row[e.getResultProp(t)],t))+" ")]}}],null,!0)}):e._e()]}))],2)],1)],1):e._e()],1),a("el-dialog",{attrs:{visible:e.configDialogVisible,"append-to-body":"",title:"加载配置",width:"550px"},on:{"update:visible":function(t){e.configDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.configLoading,expression:"configLoading"}],staticStyle:{width:"100%"},attrs:{data:e.savedConfigs},on:{"row-click":e.handleConfigSelect}},[a("el-table-column",{attrs:{label:"配置名称",prop:"name"}}),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime"}}),a("el-table-column",{attrs:{width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),e.deleteConfig(t.row)}}},[e._v("删除")])]}}])})],1)],1),a("el-dialog",{attrs:{visible:e.filterDialogVisible,title:"设置筛选条件",width:"650px"},on:{"update:visible":function(t){e.filterDialogVisible=t}}},[a("el-form",{attrs:{model:e.currentFilter,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"字段"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},model:{value:e.currentFilter.field,callback:function(t){e.$set(e.currentFilter,"field",t)},expression:"currentFilter.field"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)],1),a("el-form-item",{attrs:{label:"操作符"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择操作符"},model:{value:e.currentFilter.operator,callback:function(t){e.$set(e.currentFilter,"operator",t)},expression:"currentFilter.operator"}},[a("el-option",{attrs:{label:"等于",value:"eq"}}),a("el-option",{attrs:{label:"不等于",value:"ne"}}),a("el-option",{attrs:{label:"大于",value:"gt"}}),a("el-option",{attrs:{label:"大于等于",value:"ge"}}),a("el-option",{attrs:{label:"小于",value:"lt"}}),a("el-option",{attrs:{label:"小于等于",value:"le"}}),a("el-option",{attrs:{label:"包含",value:"contains"}}),a("el-option",{attrs:{label:"开始于",value:"startsWith"}}),a("el-option",{attrs:{label:"结束于",value:"endsWith"}})],1)],1),a("el-form-item",{attrs:{label:"值"}},[a("el-input",{attrs:{placeholder:"输入筛选值"},model:{value:e.currentFilter.value,callback:function(t){e.$set(e.currentFilter,"value",t)},expression:"currentFilter.value"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.addFilter}},[e._v("添加筛选条件")])],1)],1),e.config.filters&&e.config.filters.length?a("div",[a("h4",[e._v("已添加的筛选条件")]),a("el-table",{attrs:{data:e.config.filters,border:""}},[a("el-table-column",{attrs:{label:"字段",prop:"field"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getFieldLabel(t.row.field))+" ")]}}],null,!1,3496384076)}),a("el-table-column",{attrs:{label:"操作符",prop:"operator"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getOperatorLabel(t.row.operator))+" ")]}}],null,!1,1753700364)}),a("el-table-column",{attrs:{label:"值",prop:"value"}}),a("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{circle:"",icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.removeFilter(t.$index)}}})]}}],null,!1,3366023889)})],1)],1):e._e(),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.filterDialogVisible=!1}}},[e._v("关闭")])],1)],1)],1)},n=[],l=a("53ca"),r=a("b85c"),o=a("c7eb"),s=a("1da1"),d=a("2909"),c=a("5530"),p=(a("b64b"),a("4de4"),a("d3b7"),a("99af"),a("b0c0"),a("7db0"),a("14d9"),a("a434"),a("4e82"),a("d81d"),a("a9e3"),a("b680"),a("159b"),a("13d5"),a("ac1f"),a("5319"),a("caad"),a("2532"),a("c1df")),u=a.n(p),g=(a("72f9"),a("c211")),h=a("d67e"),m=a.n(h),f={name:"DataAggregatorBackGround",props:{fieldLabelMap:{type:Object,required:!0,default:function(){return{}}},dataSourceType:{type:String,required:!0,default:"rct"},aggregateFunction:{type:Function,required:!0},configType:{type:String,required:!1}},data:function(){return{configName:"",config:{name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},dateOptions:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按周",value:"week"},{label:"按日",value:"day"},{label:"按时",value:"hour"},{label:"按分",value:"minute"}],aggregationOptions:[{label:"计数",value:"count"},{label:"求和",value:"sum"},{label:"平均值",value:"avg"},{label:"方差",value:"variance"},{label:"最大值",value:"max"},{label:"最小值",value:"min"}],operatorOptions:[{label:"等于",value:"eq"},{label:"不等于",value:"ne"},{label:"大于",value:"gt"},{label:"大于等于",value:"ge"},{label:"小于",value:"lt"},{label:"小于等于",value:"le"},{label:"包含",value:"contains"},{label:"开始于",value:"startsWith"},{label:"结束于",value:"endsWith"}],loading:!1,configDialogVisible:!1,filterDialogVisible:!1,savedConfigs:[],configLoading:!1,isLandscape:!1,showResult:!1,processedData:[],currentFilter:{field:"",operator:"eq",value:""}}},computed:{availableFields:function(){return Object.keys(this.fieldLabelMap)},dateFields:function(){var e=this;return this.availableFields.filter((function(t){return e.fieldLabelMap[t]&&"date"===e.fieldLabelMap[t].display}))},groupFieldName:function(){return this.config.primaryField&&this.config.dateField?"".concat(this.getFieldLabel(this.config.dateField),"+").concat(this.getFieldLabel(this.config.primaryField)):this.config.primaryField?this.getFieldLabel(this.config.primaryField):this.config.dateField?this.getFieldLabel(this.config.dateField):"分组"}},methods:{getFieldLabel:function(e){var t;return(null===(t=this.fieldLabelMap[e])||void 0===t?void 0:t.name)||e},getOperatorLabel:function(e){var t=this.operatorOptions.find((function(t){return t.value===e}));return t?t.label:e},openFilterDialog:function(){this.currentFilter={field:"",operator:"eq",value:""},this.filterDialogVisible=!0},addFilter:function(){this.currentFilter.field?this.currentFilter.value||0===this.currentFilter.value?(this.config.filters||this.$set(this.config,"filters",[]),this.config.filters.push(Object(c["a"])({},this.currentFilter)),this.currentFilter={field:"",operator:"eq",value:""}):this.$message.warning("请输入筛选值"):this.$message.warning("请选择筛选字段")},removeFilter:function(e){this.config.filters.splice(e,1)},getFieldDisplay:function(e){var t=this.fieldLabelMap[e];return t?t.display&&"function"===typeof this[t.display]?"custom":t.display||"text":"text"},isAggregatable:function(e){var t=this.fieldLabelMap[e];return(null===t||void 0===t?void 0:t.aggregated)||!1},addField:function(){this.config.fields.push({fieldKey:"",aggregation:"none",format:"none",sort:"none",hideZeroValues:!1})},removeField:function(e){this.config.fields.splice(e,1)},moveField:function(e,t){var a=Object(d["a"])(this.config.fields);if("up"===t&&e>0){var i=[a[e-1],a[e]];a[e]=i[0],a[e-1]=i[1]}else if("down"===t&&e<a.length-1){var n=[a[e+1],a[e]];a[e]=n[0],a[e+1]=n[1]}this.$set(this.config,"fields",a)},handleFieldSelect:function(e){var t=this.config.fields[e],a=this.fieldLabelMap[t.fieldKey];a&&(t.format=this.getDefaultFormat(a.display),t.aggregation=a.aggregated?"sum":"none",t.sort="none")},getDefaultFormat:function(e){switch(e){case"date":return"YYYY-MM-DD";case"number":return"decimal";default:return"none"}},resetConfig:function(){this.config={name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},this.showResult=!1},getDateFormat:function(){switch(this.config.dateOptions.formatType){case"year":return"YYYY";case"month":return"YYYY-MM";case"day":default:return"YYYY-MM-DD"}},handleServerAggregate:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,i,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.config.primaryField||e.config.dateField){t.next=3;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 3:if(e.config.fields.length){t.next=6;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 6:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=10;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 10:if(e.aggregateFunction){t.next=13;break}return e.$message.error("汇总函数未定义"),t.abrupt("return");case 13:return t.prev=13,e.loading=!0,i={dataSourceType:e.dataSourceType,config:{primaryField:e.config.primaryField,matchOptions:e.config.matchOptions,dateField:e.config.dateField,dateOptions:e.config.dateOptions,showDetails:e.config.showDetails,fields:e.config.fields,filters:e.config.filters||[]}},t.next=18,e.aggregateFunction(i);case 18:n=t.sent,200===n.code?(e.processedData=e.filterZeroValueRecords(n.data),e.showResult=!0):e.$message.error(n.msg||"汇总数据失败"),t.next=26;break;case 22:t.prev=22,t.t0=t["catch"](13),console.error("数据汇总失败:",t.t0),e.$message.error("汇总处理失败："+(t.t0.message||"未知错误"));case 26:return t.prev=26,e.loading=!1,t.finish(26);case 29:case"end":return t.stop()}}),t,null,[[13,22,26,29]])})))()},filterZeroValueRecords:function(e){var t=this,a=this.config.fields.filter((function(e){return!0===e.hideZeroValues})).map((function(e){return{key:e.fieldKey,aggProp:t.getResultProp(e)}}));return 0===a.length?e:e.filter((function(e){var t,i=Object(r["a"])(a);try{for(i.s();!(t=i.n()).done;){var n=t.value,l=e[n.aggProp];if(0===l||"0"===l||"0.00"===l)return!1}}catch(o){i.e(o)}finally{i.f()}return!0}))},getResultProp:function(e){return e.aggregation&&"none"!==e.aggregation?"".concat(e.fieldKey,"_").concat(e.aggregation):e.fieldKey},getResultLabel:function(e){var t=this.getFieldLabel(e.fieldKey);if(e.aggregation&&"none"!==e.aggregation){var a,i=(null===(a=this.aggregationOptions.find((function(t){return t.value===e.aggregation})))||void 0===a?void 0:a.label)||e.aggregation;return"".concat(t,"(").concat(i,")")}return t},formatCellValue:function(e,t){if(null==e)return"-";var a=this.fieldLabelMap[t.fieldKey];if(!a)return e;if(a.display&&"function"===typeof this[a.display])return this[a.display](e);switch(a.display){case"number":var i=Number(e);if(isNaN(i))return"-";switch(t.format){case"decimal":return i.toFixed(2);case"percent":return(100*i).toFixed(2)+"%";case"currency":return"¥"+i.toFixed(2);case"usd":return"$"+i.toFixed(2);case"hideZero":return 0===i?"-":i.toFixed(2);default:return i.toFixed(2)}case"date":return u()(e).format(t.format||"YYYY-MM-DD");case"boolean":return"avg"===t.aggregation?(100*Number(e)).toFixed(2)+"%":e?"是":"否";default:return e}},formatGroupKey:function(e){if("object"===Object(l["a"])(e)&&null!==e&&void 0!==e.primary&&void 0!==e.date){var t=this.fieldLabelMap[this.config.primaryField],a=e.primary;return t&&t.display&&"function"===typeof this[t.display]&&(a=this[t.display](a)),"".concat(e.date," ").concat(a)}if(this.config.primaryField){var i=this.fieldLabelMap[this.config.primaryField];if(i&&i.display&&"function"===typeof this[i.display])return this[i.display](e)}return String(e||"")},getSummary:function(e){var t=this,a=e.columns,i=e.data,n=[];return a.forEach((function(e,a){if(0!==a){var l=a-1,r=t.config.fields[l];if(r&&r.fieldKey)if(r.aggregation&&"none"!==r.aggregation){var o=t.fieldLabelMap[r.fieldKey];if(o)if("number"===o.display||"percentage"===o.display||"function"===typeof t[o.display]){var s=i.map((function(e){var a=t.getResultProp(r),i=Number(e[a]);return isNaN(i)?0:i})).filter((function(e){return!isNaN(e)}));if(0!==s.length){var c=0;switch(r.aggregation){case"sum":c=s.reduce((function(e,t){return e+t}),0);break;case"avg":c=s.reduce((function(e,t){return e+t}),0)/s.length;break;case"max":c=Math.max.apply(Math,Object(d["a"])(s));break;case"min":c=Math.min.apply(Math,Object(d["a"])(s));break;case"variance":var p=s.reduce((function(e,t){return e+t}),0)/s.length;c=s.reduce((function(e,t){return e+Math.pow(t-p,2)}),0)/s.length;break;default:c=s.reduce((function(e,t){return e+t}),0)}"percentage"===o.display||"percent"===o.display?n[a]=t.percentage(c):"decimal"===r.format?n[a]=c.toFixed(2):"percent"===r.format?n[a]=(100*c).toFixed(2)+"%":"currency"===r.format?n[a]="¥"+c.toFixed(2):"usd"===r.format?n[a]="$"+c.toFixed(2):n[a]=c.toFixed(2)}else n[a]=""}else n[a]="";else n[a]=""}else n[a]="";else n[a]=""}else n[a]="合计"})),n},saveConfig:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,i;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.config.name){t.next=4;break}return e.$message.warning("请输入速查名称"),t.abrupt("return");case 4:if(e.config.primaryField||e.config.dateField){t.next=7;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 7:if(e.config.fields.length){t.next=10;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 10:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=14;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 14:return i={name:e.config.name,type:e.configType,config:e.config},t.next=17,Object(g["c"])(i);case 17:e.$message.success("配置保存成功"),t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](0),"cancel"!==t.t0&&e.$message.error("保存配置失败："+(t.t0.message||"未知错误"));case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},loadConfigs:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,i;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.configLoading=!0,e.configDialogVisible=!0,t.prev=2,t.next=5,Object(g["b"])({configType:e.configType});case 5:a=t.sent,e.savedConfigs=a.rows,t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("加载配置失败:",t.t0),e.$message.error((null===(i=t.t0.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||t.t0.message||"加载配置列表失败，请稍后重试");case 13:return t.prev=13,e.configLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,9,13,16]])})))()},handleConfigSelect:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function a(){var i;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{i=JSON.parse(e.config),i.name=e.name,t.config=i,t.configDialogVisible=!1,t.$message.success("配置加载成功")}catch(n){console.error("加载配置失败:",n),t.$message.error("加载配置失败："+n.message)}case 1:case"end":return a.stop()}}),a)})))()},deleteConfig:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该配置？","提示",{type:"warning"});case 3:return a.next=5,Object(g["a"])(e.id);case 5:t.savedConfigs=t.savedConfigs.filter((function(t){return t.id!==e.id})),t.$message.success("配置删除成功"),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),"cancel"!==a.t0&&t.$message.error("删除配置失败："+a.t0.message);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},printTable:function(){var e=window.open("","_blank"),t=this.$refs.resultTable.$el.cloneNode(!0),a="汇总数据",i=((new Date).toLocaleDateString(),'\n        <div class="company-header">\n          <div class="company-logo">\n            <img src="/logo.png" alt="Rich Shipping Logo" />\n            <div class="company-name">\n              <div class="company-name-cn">广州瑞旗国际货运代理有限公司</div>\n              <div class="company-name-en">GUANGZHOU RICH SHIPPING INT\'L CO.,LTD.</div>\n            </div>\n          </div>\n          <div class="document-title">\n            <div class="title-cn"></div>\n            <div class="title-en"></div>\n          </div>\n        </div>\n      ');e.document.write('\n        <html lang="">\n          <head>\n            <title>'.concat(a,"</title>\n            <style>\n              /* 基础样式 */\n              body {\n                margin: 0;\n                padding: 0;\n                font-family: Arial, sans-serif;\n              }\n\n              /* 打印样式 - 必须放在这里才能生效 */\n              @media print {\n                @page {\n                  size: ").concat(this.isLandscape?"landscape":"portrait",';\n                  margin: 1.5cm 1cm 1cm 1cm;\n                }\n\n                /* 重要：使用重复表头技术 */\n                thead {\n                  display: table-header-group;\n                }\n\n                /* 页眉作为表格的一部分，放在thead中 */\n                .page-header {\n                  display: table-header-group;\n                }\n\n                /* 内容部分 */\n                .page-content {\n                  display: table-row-group;\n                }\n\n                /* 避免元素内部分页 */\n                .company-header, .header-content {\n                  page-break-inside: avoid;\n                }\n\n                /* 表格样式 */\n                table.main-table {\n                  width: 100%;\n                  border-collapse: collapse;\n                  border: none;\n                }\n\n                /* 确保表头在每页都显示 */\n                table.data-table thead {\n                  display: table-header-group;\n                }\n\n                /* 避免行内分页 */\n                table.data-table tr {\n                  page-break-inside: avoid;\n                }\n              }\n\n              /* 表格样式 */\n              table.data-table {\n                border-collapse: collapse;\n                width: 100%;\n                margin-top: 20px;\n                table-layout: fixed;\n              }\n\n              table.data-table th, table.data-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              table.data-table th {\n                background-color: #f2f2f2;\n              }\n\n              /* Element UI 表格样式模拟 */\n              .el-table {\n                border-collapse: collapse;\n                width: 100%;\n                table-layout: fixed;\n              }\n\n              .el-table th, .el-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              .el-table th {\n                background-color: #f2f2f2;\n                font-weight: bold;\n              }\n\n              .el-table__footer {\n                background-color: #f8f8f9;\n                font-weight: bold;\n              }\n\n              .el-table__footer td {\n                border: 1px solid #ddd;\n                padding: 8px;\n              }\n\n              /* 公司标题和标志样式 */\n              .company-header {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                border-bottom: 2px solid #000;\n                padding-bottom: 10px;\n                width: 100%;\n              }\n\n              .company-logo {\n                display: flex;\n                align-items: center;\n              }\n\n              .company-logo img {\n                height: 50px;\n                margin-right: 10px;\n              }\n\n              .company-name {\n                display: flex;\n                flex-direction: column;\n              }\n\n              .company-name-cn {\n                font-size: 18px;\n                font-weight: bold;\n                color: #ff0000;\n              }\n\n              .company-name-en {\n                font-size: 14px;\n              }\n\n              .document-title {\n                text-align: right;\n              }\n\n              .title-cn {\n                font-size: 18px;\n                font-weight: bold;\n              }\n\n              .title-en {\n                font-size: 16px;\n                font-weight: bold;\n              }\n\n              /* 清除表格边框 */\n              table.main-table, table.main-table td {\n                border: none;\n              }\n\n              /* 页眉容器 */\n              .header-container {\n                width: 100%;\n                margin-bottom: 20px;\n              }\n            </style>\n          </head>\n          <body>\n            \x3c!-- 使用表格布局确保页眉在每页重复 --\x3e\n            <table class="main-table">\n              <thead class="page-header">\n                <tr>\n                  <td>\n                    <div class="header-container">\n                      ').concat(i,'\n                    </div>\n                  </td>\n                </tr>\n              </thead>\n              <tbody class="page-content">\n                <tr>\n                  <td>\n                    \x3c!-- 保留原始表格的类名并添加data-table类 --\x3e\n                    ').concat(t.outerHTML.replace("<table",'<table class="el-table data-table"'),"\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </body>\n        </html>\n      ")),e.document.close(),setTimeout((function(){try{e.focus(),e.print()}catch(t){console.error("打印过程中发生错误:",t)}}),1e3)},exportToPDF:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,i;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,a=e.$refs.resultTable.$el,i={margin:[.8,.8,.8,.8],filename:"汇总数据.pdf",image:{type:"jpeg",quality:.98},html2canvas:{scale:2},jsPDF:{unit:"in",format:"a3",orientation:e.isLandscape?"landscape":"portrait"},pagebreak:{mode:["avoid-all","css","legacy"]},header:[{text:"汇总数据",style:"headerStyle"},{text:(new Date).toLocaleDateString(),style:"headerStyle",alignment:"right"}],footer:{height:"20px",contents:{default:'<span style="float:right">{{page}}/{{pages}}</span>'}}},t.next=6,m()().set(i).from(a).save();case 6:e.$message.success("PDF导出成功"),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.$message.error("PDF导出失败："+t.t0.message);case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,9,12,15]])})))()},getName:function(e){if(null!==e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t&&void 0!==t)return t.staffShortName+t.staffFamilyEnName}return""},percentage:function(e){if(null==e||""===e)return"-";if("string"===typeof e&&e.includes("%"))return e;var t=Number(e);if(isNaN(t))return"-";var a=t>0&&t<=1,i=a?100*t:t;return i.toFixed(2)+"%"},getColumnAlign:function(e){var t=this.fieldLabelMap[e];return t&&t.align?t.align:"left"},getColumnWidth:function(e){var t=this.fieldLabelMap[e];return t&&t.width?t.width:""}}},y=f,b=(a("aafe"),a("2877")),w=Object(b["a"])(y,i,n,!1,null,"f80d2af4",null);t["default"]=w.exports},d30c:function(e,t,a){"use strict";a("f20e")},e350:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return l}));a("d3b7"),a("caad"),a("2532");var i=a("4360");function n(e){if(e&&e instanceof Array&&e.length>0){var t=i["a"].getters&&i["a"].getters.permissions,a=e,n="*:*:*",l=t.some((function(e){return n==e||a.includes(e)}));return!!l}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function l(e){if(e&&e instanceof Array&&e.length>0){var t=i["a"].getters&&i["a"].getters.roles,a=e,n="admin",l=t.some((function(e){return n==e||a.includes(e)}));return!!l}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}},f20e:function(e,t,a){},f870:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,width:"220mm"},on:{"update:visible":function(t){e.visible=t},cancel:e.hideModal}},[a("template",{slot:"title"},[a("el-row",{attrs:{gutter:10}},[a("div",{staticStyle:{"margin-right":"20px"}},[e._v("打印预览")]),a("el-button",{attrs:{loading:e.waitShowPrinter,icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.print(t)}}},[e._v("打印")]),a("el-button",{attrs:{icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.toPdf(t)}}},[e._v("pdf")])],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.spinning,expression:"spinning"}],staticStyle:{"min-height":"120px"}},[a("div",{attrs:{id:"preview_content_design"}})])],2)},n=[],l=a("c1df"),r=a.n(l),o={name:"printPreview",props:["customWidth"],data:function(){return{visible:!1,spinning:!0,waitShowPrinter:!1,width:this.customWidth?this.customWidth:"220mm",hiprintTemplate:{},printData:{},pdfName:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{hideModal:function(){this.visible=!1},show:function(e,t){var a=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"210";this.visible=!0,this.spinning=!0,this.pdfName=t.pdfName?t.pdfName:r()().format("YYYYMMDD"),this.width=e.editingPanel?e.editingPanel.width:i,this.hiprintTemplate=e,this.printData=t,setTimeout((function(){$("#preview_content_design").html(e.getHtml(t)),a.spinning=!1}),500)},print:function(e,t){var a=this;this.hiprintTemplate=e||this.hiprintTemplate,this.printData=t||this.printData,this.waitShowPrinter=!0,this.hiprintTemplate.print(this.printData,{},{callback:function(){console.log("callback"),a.waitShowPrinter=!1}})},toPdf:function(){this.hiprintTemplate.toPdf(this.printData,this.pdfName)}}},s=o,d=(a("4ba95"),a("2877")),c=Object(d["a"])(s,i,n,!1,null,"f7e23638",null);t["default"]=c.exports}}]);