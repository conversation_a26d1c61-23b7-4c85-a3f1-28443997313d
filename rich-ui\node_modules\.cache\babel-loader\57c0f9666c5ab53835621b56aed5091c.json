{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue", "mtime": 1752832104329}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_outboundrecord", "require", "_rich", "_inventory", "_moment", "_interopRequireDefault", "_currency", "_warehouseReceipt", "_", "_preview", "_outboundPlant", "hiprintTemplate", "_default", "name", "components", "printPreview", "data", "showLeft", "showRight", "loading", "selectOutboundList", "ids", "single", "multiple", "showSearch", "total", "outboundrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "outboundNo", "clientCode", "clientName", "operator", "containerType", "containerNo", "sealNo", "outboundDate", "warehouseQuote", "workerLoadingFee", "warehouseCollection", "collectionNotes", "totalBoxes", "totalGrossWeight", "totalVolume", "totalRows", "receivedStorageFee", "unpaidUnloadingFee", "unpaidPackagingFee", "logisticsAdvanceFee", "rentalBalanceFee", "overdueRent", "freeStackDays", "overdueUnitPrice", "form", "outboundType", "preOutboundInventoryListLoading", "search", "rules", "required", "message", "trigger", "outboundForm", "moment", "format", "clientRow", "openOutbound", "preOutboundInventoryList", "selectedCargoDetail", "watch", "n", "created", "getList", "mounted", "initPrint", "methods", "<PERSON><PERSON><PERSON>", "init", "providers", "defaultElementTypeProvider", "printOutboundPlant", "_this", "printData", "customerOrderNo", "plannedOutboundDate", "cargoType", "plateNumber", "driverPhone", "warehousePay", "operationRequirement", "outboundNote", "orderDate", "outbound<PERSON><PERSON><PERSON>", "warehouseConfirm", "parseTime", "Date", "totalSummary", "concat", "totalQuantity", "inventoryList", "map", "item", "inboundSerialNo", "subOrderNo", "consignee<PERSON><PERSON>", "sqdShippingMark", "itemName", "driverInfo", "outboundQuantity", "PrintTemplate", "template", "outboundPlant", "$refs", "preView", "print", "$message", "warning", "$store", "state", "user", "split", "success", "loadChildInventory", "tree", "treeNode", "resolve", "_this2", "$set", "listInventory", "packageTo", "inventoryId", "then", "response", "rows", "children", "includes", "setTimeout", "for<PERSON>ach", "child", "push", "table", "toggleRowSelection", "finally", "warehouseRentSettlement", "outbound<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unreceivedFromCustomer", "currency", "add", "additionalStorageFee", "overdueRentalFee", "difficultyWorkFee", "value", "receivedFromCustomer", "customerReceivableBalance", "subtract", "payableToWorker", "receivedUnloadingFee", "receivedPackingFee", "receivedFromSupplier", "receivedSupplier", "promissoryNoteSales", "promissoryNoteCost", "warehouseAdvanceOtherFee", "promissoryNoteGrossProfit", "outboundConfirm", "type", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "cargoDeduction", "error", "inboundSerialNoSub", "partialOutboundFlag", "Number", "rsCargoDetailsList", "boxCount", "unitGrossWeight", "unitVolume", "addOutboundrecord", "preOutboundFlag", "preOutboundInventory", "updateOutboundrecord", "outboundRecordId", "outboundInventory", "isRentSettlement", "rentalSettlementDate", "settlement", "loadPreOutboundInventoryList", "_this4", "sqdPlannedOutboundDate", "inventoryStatus", "preOutboundRecordId", "listInventorys", "filter", "includesInboundFee", "receivedFee", "inboundFee", "difference", "packageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$nextTick", "catch", "console", "handleOutbound", "selectedRows", "handlePreOutbound", "handleDirectOutbound", "handleRentSettlement", "handleOutboundCargoDetailSelectionChange", "selection", "row", "outboundCargoDetailsList", "isRowSelected", "getSummaries", "param", "_this5", "columns", "sums", "statisticalField", "summaryResults", "column", "index", "prop", "property", "reduce", "sum", "Object", "keys", "field", "handleOutboundSelectionChange", "_this6", "treeData", "store", "states", "previousIds", "_toConsumableArray2", "default", "newlySelected", "id", "newlyDeselected", "doLayout", "date1", "date2", "rentalDays", "diff", "volumn", "isNaN", "multiply", "overdueRentalUnitPrice", "days", "freeStackPeriod", "parentNode", "find", "node", "length", "childrenLoaded", "toggleRowExpansion", "parentId", "childIndex", "indexOf", "splice", "itemIndex", "findIndex", "selectContainerType", "rate20gp", "rate40hq", "outboundClient", "rateLcl", "$forceUpdate", "_this7", "listOutboundrecord", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this8", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "handleSelectionChange", "handleAdd", "handleUpdate", "_this9", "getOutboundrecord", "submitForm", "_this10", "validate", "valid", "handleDelete", "_this11", "outboundRecordIds", "delOutboundrecord", "handleExport", "download", "_objectSpread2", "getTime", "handleSearchEnter", "_this12", "serialNo", "String", "searchValue", "scrollWrapper", "$el", "querySelector", "querySelectorAll", "targetIndex", "idx", "rowText", "textContent", "targetRow", "rowTop", "offsetTop", "scrollTo", "top", "clientHeight", "behavior", "classList", "remove", "exports"], "sources": ["src/views/system/outbound/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"名称\" prop=\"clientName\">\r\n            <el-input\r\n              v-model=\"queryParams.clientName\"\r\n              clearable\r\n              placeholder=\"客户名称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n            <el-input\r\n              v-model=\"queryParams.containerNo\"\r\n              clearable\r\n              placeholder=\"柜号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"封号\" prop=\"sealNo\">\r\n            <el-input\r\n              v-model=\"queryParams.sealNo\"\r\n              clearable\r\n              placeholder=\"封号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDate\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handlePreOutbound()\"\r\n            >操作预出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleDirectOutbound()\"\r\n            >直接出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleRentSettlement()\"\r\n            >结算仓租\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"(selectedRows) =>handleOutbound(selectedRows)\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\"/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\"/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"代收备注\" prop=\"collectionNotes\"/>\r\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\r\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\r\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\r\n          <el-table-column align=\"center\" label=\"总行数\" prop=\"totalRows\"/>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackagingFee\"/>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"租金平衡费\" prop=\"rentalBalanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"超期仓租\" prop=\"overdueRent\"/>\r\n          <el-table-column align=\"center\" label=\"免堆天数\" prop=\"freeStackDays\"/>\r\n          <el-table-column align=\"center\" label=\"超期单价\" prop=\"overdueUnitPrice\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓日期\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.outboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"下单日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\"\r\n                    >\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table v-loading=\"preOutboundInventoryListLoading\" :data=\"preOutboundInventoryList\"\r\n                        ref=\"table\" :summary-method=\"getSummaries\" max-height=\"300\"\r\n                        show-summary @selection-change=\"handleOutboundSelectionChange\"\r\n                        :load=\"loadChildInventory\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        element-loading-text=\"加载中...\" row-key=\"inventoryId\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template slot=\"header\" slot-scope=\"scope\">\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter.native=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template slot-scope=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"printOutboundPlant\">打印出仓计划</el-button>\r\n        <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n        <el-button v-else type=\"primary\" @click=\"outboundConfirm(outboundType)\">{{\r\n            outboundType === 3 ? \"结 算\" : \"出 仓\"\r\n          }}</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n        <el-button @click=\"openOutbound = false\">关 闭</el-button>\r\n  </span>\r\n    </el-dialog>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\r\nimport {defaultElementTypeProvider, hiprint} from \"@\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport outboundPlant from \"@/print-template/outboundPlant\"\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  components: {printPreview},\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  mounted() {\r\n    this.initPrint()\r\n  },\r\n  methods: {\r\n    initPrint() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n    },\r\n    printOutboundPlant() {\r\n      // 准备打印数据\r\n      const printData = {\r\n        title: \"瑞旗仓库出仓计划\",\r\n        // 表单数据\r\n        outboundNo: this.outboundForm.outboundNo || \"\",\r\n        customerOrderNo: this.outboundForm.customerOrderNo || \"\",\r\n        clientCode: this.outboundForm.clientCode || \"\",\r\n        clientName: this.outboundForm.clientName || \"\",\r\n        plannedOutboundDate: moment(this.outboundForm.plannedOutboundDate).format(\"yyyy-MM-DD HH:mm\") || \"\",\r\n        outboundType: this.outboundForm.outboundType || \"\",\r\n        containerType: this.outboundForm.containerType || \"\",\r\n        cargoType: this.form.cargoType || \"\",\r\n        containerNo: this.outboundForm.containerNo || \"\",\r\n        sealNo: this.outboundForm.sealNo || \"\",\r\n        plateNumber: this.outboundForm.plateNumber || \"\",\r\n        driverPhone: this.outboundForm.driverPhone || \"\",\r\n        warehouseQuote: this.outboundForm.warehouseQuote || \"\",\r\n        warehouseCollection: this.outboundForm.warehouseCollection || \"\",\r\n        workerLoadingFee: this.outboundForm.workerLoadingFee || \"\",\r\n        warehousePay: this.outboundForm.warehousePay || \"\",\r\n        operationRequirement: this.outboundForm.operationRequirement || \"\",\r\n        outboundNote: this.outboundForm.outboundNote || \"\",\r\n        operator: this.outboundForm.operator || \"\",\r\n        orderDate: this.outboundForm.orderDate || \"\",\r\n        outboundHandler: this.outboundForm.outboundHandler || \"\",\r\n        warehouseConfirm: \"√ 已确认 \" + this.parseTime(new Date(), \"{y}-{m}-{d}\"),\r\n\r\n        // 汇总数据\r\n        totalBoxes: this.outboundForm.totalBoxes || 0,\r\n        totalGrossWeight: this.outboundForm.totalGrossWeight || 0,\r\n        totalVolume: this.outboundForm.totalVolume || 0,\r\n        totalSummary: `件数: ${this.outboundForm.totalBoxes || 0} / 毛重: ${this.outboundForm.totalGrossWeight || 0} / 体积: ${this.outboundForm.totalVolume || 0}`,\r\n        totalQuantity: this.outboundForm.totalBoxes || 0,\r\n\r\n        // 勾选的库存列表\r\n        inventoryList: this.selectOutboundList.map(item => {\r\n          return {\r\n            inboundSerialNo: item.inboundSerialNo || \"\",\r\n            clientCode: `${item.subOrderNo || \"\"} ${item.consigneeName || \"\"}`,\r\n            totalBoxes: (this.outboundForm.sqdShippingMark || \"\") + \" / \" + (item.itemName || \"\") + \" / \" + (item.totalBoxes || 0) + \" / \" + (item.totalGrossWeight || 0) + \"KGS / \" + (item.totalVolume || 0) + \"CBM\",\r\n            totalGrossWeight: item.totalGrossWeight || 0,\r\n            totalVolume: item.totalVolume || 0,\r\n            driverInfo: item.driverInfo || \"\",\r\n            outboundQuantity: item.totalBoxes || 0\r\n          }\r\n        })\r\n      }\r\n\r\n      // 创建打印模板并预览打印\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: outboundPlant})\r\n      this.$refs.preView.print(hiprintTemplate, printData)\r\n    },\r\n    warehouseConfirm() {\r\n      // 检查客户代码是否已选择\r\n      if (!this.outboundForm.clientCode) {\r\n        this.$message.warning(\"请先选择客户\")\r\n        return\r\n      }\r\n\r\n      // 设置操作员为当前用户\r\n      this.outboundForm.operator = this.$store.state.user.name.split(\" \")[1]\r\n\r\n      // 设置下单日期为当前日期\r\n      this.outboundForm.orderDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n      // 提示确认成功\r\n      this.$message.success(\"仓管确认成功\")\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 设置当前行的加载状态\r\n      this.$set(tree, 'loading', true)\r\n\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      }).finally(() => {\r\n        // 无论请求成功还是失败，都需要关闭加载状态\r\n        this.$set(tree, 'loading', false)\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 执行前再次提醒\r\n      this.$confirm(\"确定要\" + (type === 0 ? \"预出仓\" : type === 1 ? \"出仓\" : \"直接出仓\") + \"吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 扣货不可以出仓\r\n        this.selectOutboundList.map(item => {\r\n          if (item.cargoDeduction == 1) {\r\n            this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n            return\r\n          }\r\n        })\r\n\r\n        this.selectOutboundList.map(item => {\r\n          item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n        })\r\n\r\n        // 更新箱数、毛重、体积\r\n        this.outboundForm.totalBoxes = 0\r\n        this.outboundForm.totalGrossWeight = 0\r\n        this.outboundForm.totalVolume = 0\r\n        this.selectOutboundList.map(item => {\r\n          item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n            this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n            this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n            this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n            return item\r\n          }) : null\r\n          return item\r\n        })\r\n        if (type === 0) {\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上预出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.preOutboundFlag = \"1\"\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 1) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"1\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 2) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 3) {\r\n          // 结算仓租\r\n          this.outboundForm.isRentSettlement = 1 // 仓租结算记录\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rentalSettlementDate = this.outboundForm.outboundDate\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            settlement(data).then(response => {\r\n              this.$message.success(\"结算成功\")\r\n              this.loadPreOutboundInventoryList()\r\n            })\r\n          })\r\n        } else {\r\n          const outboundRecordId = this.outboundForm.outboundRecordId\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.preOutboundInventoryListLoading = true\r\n      this.loading = true\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      }\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.queryParams.preOutboundFlag) {\r\n        queryParams.preOutboundFlag = this.queryParams.preOutboundFlag\r\n      }\r\n\r\n      if (this.queryParams.preOutboundRecordId) {\r\n        queryParams.preOutboundRecordId = this.queryParams.preOutboundRecordId\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0)\r\n              const inboundFee = Number(item.inboundFee || 0)\r\n              const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0\r\n            } else {\r\n              item.additionalStorageFee = 0\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            if (this.outboundForm.outboundRecordId === item.preOutboundRecordId) {\r\n              this.selectOutboundList.push(item)\r\n              this.$nextTick(() => {\r\n                this.$refs.table.toggleRowSelection(item, true)\r\n              })\r\n            }\r\n\r\n            return item\r\n          }) : []\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error)\r\n          this.$message.error(\"加载预出库库存列表失败\")\r\n        })\r\n        .finally(() => {\r\n          this.queryParams.preOutboundRecordId = null\r\n          this.queryParams.preOutboundFlag = null\r\n          this.loading = false\r\n          this.preOutboundInventoryListLoading = false\r\n        })\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.queryParams.preOutboundRecordId = this.outboundForm.outboundRecordId\r\n      this.queryParams.preOutboundFlag = \"1\"\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          let total = 0; // 在条件块之前定义total变量\r\n\r\n          if (prop === \"totalBoxes\" || prop === \"totalVolume\" || prop === \"totalGrossWeight\") {\r\n            total = this.selectOutboundList.reduce((sum, row) => {\r\n              if (row.packageTo) {\r\n                return currency(sum).add(Number(row[prop]) || 0).value\r\n              }\r\n              return sum\r\n            }, 0)\r\n          } else {\r\n            total = this.selectOutboundList.reduce((sum, row) =>\r\n              currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n          }\r\n\r\n          sums[index] = total\r\n          // 现在可以安全地使用total\r\n          summaryResults[column.property] = total\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids]\r\n\r\n      // 清空当前选择\r\n      this.ids = []\r\n      this.ids = selection.map(item => item.inventoryId)\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId)\r\n                  this.selectOutboundList.push(child)\r\n                  this.$refs.table.toggleRowSelection(child, true)\r\n                }\r\n              })\r\n            }, 50) // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true)\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        )\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId)\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1)\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              )\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1)\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false)\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        receivedSupplier: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || \"\")\r\n          const searchValue = String(this.search)\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue)\r\n        }\r\n      )\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector(\".el-table__body-wrapper\")\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll(\".el-table__row\")\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx\r\n            }\r\n          })\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex]\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: \"smooth\"\r\n            })\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add(\"highlight-row\")\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove(\"highlight-row\")\r\n            }, 2000)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning(\"未找到匹配的记录\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAgjBA,IAAAA,eAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAOA,IAAAG,OAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,iBAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,CAAA,GAAAP,OAAA;AACA,IAAAQ,QAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,cAAA,GAAAL,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAU,eAAA;AAAA,IAAAC,QAAA,GACA;EACAC,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACAC,kBAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA;MACAC,IAAA;MACAC,YAAA;MACAC,+BAAA;MACAC,MAAA;MACA;MACAC,KAAA;QACA3B,UAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,YAAA;QACAzB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;MACA;MACAC,SAAA;MACAC,YAAA;MACAC,wBAAA;MACAC,mBAAA;IACA;EACA;EACAC,KAAA;IACA/C,UAAA,WAAAA,WAAAgD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAtD,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAwD,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MACAE,SAAA,CAAAC,IAAA;QACAC,SAAA,OAAAC,4BAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MACA;MACA,IAAAC,SAAA;QACAzD,KAAA;QACA;QACAK,UAAA,OAAAgC,YAAA,CAAAhC,UAAA;QACAqD,eAAA,OAAArB,YAAA,CAAAqB,eAAA;QACApD,UAAA,OAAA+B,YAAA,CAAA/B,UAAA;QACAC,UAAA,OAAA8B,YAAA,CAAA9B,UAAA;QACAoD,mBAAA,MAAArB,eAAA,OAAAD,YAAA,CAAAsB,mBAAA,EAAApB,MAAA;QACAT,YAAA,OAAAO,YAAA,CAAAP,YAAA;QACArB,aAAA,OAAA4B,YAAA,CAAA5B,aAAA;QACAmD,SAAA,OAAA/B,IAAA,CAAA+B,SAAA;QACAlD,WAAA,OAAA2B,YAAA,CAAA3B,WAAA;QACAC,MAAA,OAAA0B,YAAA,CAAA1B,MAAA;QACAkD,WAAA,OAAAxB,YAAA,CAAAwB,WAAA;QACAC,WAAA,OAAAzB,YAAA,CAAAyB,WAAA;QACAjD,cAAA,OAAAwB,YAAA,CAAAxB,cAAA;QACAE,mBAAA,OAAAsB,YAAA,CAAAtB,mBAAA;QACAD,gBAAA,OAAAuB,YAAA,CAAAvB,gBAAA;QACAiD,YAAA,OAAA1B,YAAA,CAAA0B,YAAA;QACAC,oBAAA,OAAA3B,YAAA,CAAA2B,oBAAA;QACAC,YAAA,OAAA5B,YAAA,CAAA4B,YAAA;QACAzD,QAAA,OAAA6B,YAAA,CAAA7B,QAAA;QACA0D,SAAA,OAAA7B,YAAA,CAAA6B,SAAA;QACAC,eAAA,OAAA9B,YAAA,CAAA8B,eAAA;QACAC,gBAAA,kBAAAC,SAAA,KAAAC,IAAA;QAEA;QACArD,UAAA,OAAAoB,YAAA,CAAApB,UAAA;QACAC,gBAAA,OAAAmB,YAAA,CAAAnB,gBAAA;QACAC,WAAA,OAAAkB,YAAA,CAAAlB,WAAA;QACAoD,YAAA,mBAAAC,MAAA,MAAAnC,YAAA,CAAApB,UAAA,4BAAAuD,MAAA,MAAAnC,YAAA,CAAAnB,gBAAA,4BAAAsD,MAAA,MAAAnC,YAAA,CAAAlB,WAAA;QACAsD,aAAA,OAAApC,YAAA,CAAApB,UAAA;QAEA;QACAyD,aAAA,OAAAjF,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACA;YACAC,eAAA,EAAAD,IAAA,CAAAC,eAAA;YACAvE,UAAA,KAAAkE,MAAA,CAAAI,IAAA,CAAAE,UAAA,aAAAN,MAAA,CAAAI,IAAA,CAAAG,aAAA;YACA9D,UAAA,GAAAuC,KAAA,CAAAnB,YAAA,CAAA2C,eAAA,mBAAAJ,IAAA,CAAAK,QAAA,mBAAAL,IAAA,CAAA3D,UAAA,kBAAA2D,IAAA,CAAA1D,gBAAA,qBAAA0D,IAAA,CAAAzD,WAAA;YACAD,gBAAA,EAAA0D,IAAA,CAAA1D,gBAAA;YACAC,WAAA,EAAAyD,IAAA,CAAAzD,WAAA;YACA+D,UAAA,EAAAN,IAAA,CAAAM,UAAA;YACAC,gBAAA,EAAAP,IAAA,CAAA3D,UAAA;UACA;QACA;MACA;;MAEA;MACAjC,eAAA,OAAAmE,SAAA,CAAAiC,aAAA;QAAAC,QAAA,EAAAC;MAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAzG,eAAA,EAAAyE,SAAA;IACA;IACAW,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAA/B,YAAA,CAAA/B,UAAA;QACA,KAAAoF,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAtD,YAAA,CAAA7B,QAAA,QAAAoF,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;;MAEA;MACA,KAAA1D,YAAA,CAAA6B,SAAA,OAAA5B,eAAA,IAAAC,MAAA;;MAEA;MACA,KAAAmD,QAAA,CAAAM,OAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,IAAA,CAAAJ,IAAA;;MAEA;MACA,IAAAK,wBAAA;QAAAC,SAAA,EAAAN,IAAA,CAAAO;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAAC,IAAA,GAAAD,QAAA,CAAAC,IAAA;;QAEA;QACAR,OAAA,CAAAQ,IAAA;QACAV,IAAA,CAAAW,QAAA,GAAAD,IAAA;;QAEA;QACA,IAAAP,MAAA,CAAA3G,GAAA,CAAAoH,QAAA,CAAAZ,IAAA,CAAAO,WAAA;UACAM,UAAA;YACAH,IAAA,CAAAI,OAAA,WAAAC,KAAA;cACA,KAAAZ,MAAA,CAAA3G,GAAA,CAAAoH,QAAA,CAAAG,KAAA,CAAAR,WAAA;gBACAJ,MAAA,CAAA3G,GAAA,CAAAwH,IAAA,CAAAD,KAAA,CAAAR,WAAA;gBACAJ,MAAA,CAAA5G,kBAAA,CAAAyH,IAAA,CAAAD,KAAA;cACA;cACA;cACAZ,MAAA,CAAAd,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA,GAAAI,OAAA;QACA;QACAhB,MAAA,CAAAC,IAAA,CAAAJ,IAAA;MACA;IACA;IACAoB,uBAAA,WAAAA,wBAAA;MACA,KAAAC,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA+E,YAAA,WAAAA,aAAA;MACA,KAAAnF,YAAA,CAAAoF,sBAAA,OAAAC,iBAAA,OAAArF,YAAA,CAAAxB,cAAA,EAAA8G,GAAA,MAAAtF,YAAA,CAAAuF,oBAAA,EAAAD,GAAA,MAAAtF,YAAA,CAAAf,kBAAA,EAAAqG,GAAA,MAAAtF,YAAA,CAAAd,kBAAA,EAAAoG,GAAA,MAAAtF,YAAA,CAAAb,mBAAA,EAAAmG,GAAA,MAAAtF,YAAA,CAAAwF,gBAAA,EAAAF,GAAA,MAAAtF,YAAA,CAAAyF,iBAAA,EAAAC,KAAA;MACA,KAAA1F,YAAA,CAAA2F,oBAAA,OAAAN,iBAAA,OAAArF,YAAA,CAAAtB,mBAAA,EAAAgH,KAAA;MACA,KAAA1F,YAAA,CAAA4F,yBAAA,OAAAP,iBAAA,OAAArF,YAAA,CAAAoF,sBAAA,EAAAS,QAAA,MAAA7F,YAAA,CAAA2F,oBAAA,EAAAD,KAAA;MACA,KAAA1F,YAAA,CAAA8F,eAAA,OAAAT,iBAAA,OAAArF,YAAA,CAAA+F,oBAAA,EAAAT,GAAA,MAAAtF,YAAA,CAAAgG,kBAAA,EAAAV,GAAA,MAAAtF,YAAA,CAAAvB,gBAAA,EAAAiH,KAAA;MACA,KAAA1F,YAAA,CAAAiG,oBAAA,OAAAZ,iBAAA,OAAArF,YAAA,CAAAkG,gBAAA,EAAAZ,GAAA,MAAAtF,YAAA,CAAAhB,kBAAA,EAAA0G,KAAA;MACA,KAAA1F,YAAA,CAAAmG,mBAAA,OAAAd,iBAAA,OAAArF,YAAA,CAAAoF,sBAAA,EAAAE,GAAA,MAAAtF,YAAA,CAAAiG,oBAAA,EAAAP,KAAA;MACA,KAAA1F,YAAA,CAAAoG,kBAAA,OAAAf,iBAAA,OAAArF,YAAA,CAAA8F,eAAA,EAAAR,GAAA,MAAAtF,YAAA,CAAAb,mBAAA,EAAAmG,GAAA,MAAAtF,YAAA,CAAAqG,wBAAA,EAAAX,KAAA;MACA,KAAA1F,YAAA,CAAAsG,yBAAA,OAAAjB,iBAAA,OAAArF,YAAA,CAAAmG,mBAAA,EAAAN,QAAA,MAAA7F,YAAA,CAAAoG,kBAAA,EAAAV,KAAA;IACA;IACAL,QAAA,EAAAA,iBAAA;IACA;AACA;AACA;AACA;IACAkB,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAC,QAAA,UAAAF,IAAA,iBAAAA,IAAA;QACAG,iBAAA;QACAC,gBAAA;QACAJ,IAAA;MACA,GAAAnC,IAAA;QACA;QACAoC,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAsE,cAAA;YACAJ,MAAA,CAAApD,QAAA,CAAAyD,KAAA,qBAAAvE,IAAA,CAAAwE,kBAAA;YACA;UACA;QACA;QAEAN,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAAyE,mBAAA,GAAAC,MAAA,CAAA1E,IAAA,CAAAyE,mBAAA;QACA;;QAEA;QACAP,MAAA,CAAAzG,YAAA,CAAApB,UAAA;QACA6H,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA;QACA4H,MAAA,CAAAzG,YAAA,CAAAlB,WAAA;QACA2H,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;UACAA,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;YACAkE,MAAA,CAAAzG,YAAA,CAAApB,UAAA,OAAAyG,iBAAA,EAAA9C,IAAA,CAAA4E,QAAA,EAAA7B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAApB,UAAA,EAAA8G,KAAA;YACAe,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA,OAAAwG,iBAAA,EAAA9C,IAAA,CAAA6E,eAAA,EAAA9B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAAnB,gBAAA,EAAA6G,KAAA;YACAe,MAAA,CAAAzG,YAAA,CAAAlB,WAAA,OAAAuG,iBAAA,EAAA9C,IAAA,CAAA8E,UAAA,EAAA/B,GAAA,CAAAmB,MAAA,CAAAzG,YAAA,CAAAlB,WAAA,EAAA4G,KAAA;YACA,OAAAnD,IAAA;UACA;UACA,OAAAA,IAAA;QACA;QACA,IAAAiE,IAAA;UACA,IAAAc,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAtH,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAgF,eAAA;cACAhF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAgF,eAAA;gBACA,OAAAhF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;YAEA,IAAAiF,+BAAA,EAAAxK,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UAEA,IAAAuH,oCAAA,EAAAhB,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UAEA,IAAAoH,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA,WAAAoG,IAAA;UACA;UACAC,MAAA,CAAAzG,YAAA,CAAA4H,gBAAA;UACA,IAAAN,iCAAA,EAAAb,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA,IAAAoD,gBAAA,GAAApD,QAAA,CAAAtH,IAAA;YACA;YACA,IAAAA,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAAsF,oBAAA,GAAApB,MAAA,CAAAzG,YAAA,CAAAzB,YAAA;cACAgE,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;YAEA,IAAAuF,qBAAA,EAAA9K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAAsB,4BAAA;YACA;UACA;QACA;UACA,IAAAL,gBAAA,GAAAjB,MAAA,CAAAzG,YAAA,CAAA0H,gBAAA;UACAjB,MAAA,CAAAzG,YAAA,CAAAuH,eAAA;UACAd,MAAA,CAAAzG,YAAA,CAAAzB,YAAA,OAAA0B,eAAA,IAAAC,MAAA;UACA,IAAAuH,oCAAA,EAAAhB,MAAA,CAAAzG,YAAA,EAAAqE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAtH,IAAA,GAAAyJ,MAAA,CAAArJ,kBAAA,CAAAkF,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;cACAnF,IAAA,CAAA2E,kBAAA,GAAA3E,IAAA,CAAA2E,kBAAA,CAAA5E,GAAA,WAAAC,IAAA;gBACAA,IAAA,CAAAmF,gBAAA,GAAAA,gBAAA;gBACA,OAAAnF,IAAA;cACA;cACA,OAAAA,IAAA;YACA;;YAEA;YACA,IAAAoF,4BAAA,EAAA3K,IAAA,EAAAqH,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAA/F,OAAA;cACA+F,MAAA,CAAApD,QAAA,CAAAM,OAAA;cACA8C,MAAA,CAAArG,YAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA2H,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,KAAAtI,+BAAA;MACA,KAAAvC,OAAA;;MAEA;MACA,IAAAU,WAAA;QACAoK,sBAAA,OAAAjI,YAAA,CAAAsB,mBAAA;QACArD,UAAA,OAAA+B,YAAA,CAAA/B,UAAA;QACAiK,eAAA;MACA;;MAEA;MACA,SAAArK,WAAA,CAAA0J,eAAA;QACA1J,WAAA,CAAA0J,eAAA,QAAA1J,WAAA,CAAA0J,eAAA;MACA;MAEA,SAAA1J,WAAA,CAAAsK,mBAAA;QACAtK,WAAA,CAAAsK,mBAAA,QAAAtK,WAAA,CAAAsK,mBAAA;MACA;;MAEA;MACA,IAAAC,yBAAA,EAAAvK,WAAA,EACAwG,IAAA,WAAAC,QAAA;QACA;QACA0D,MAAA,CAAA3H,wBAAA,GAAAiE,QAAA,CAAAC,IAAA,CAAA8D,MAAA,WAAA9F,IAAA;UAAA,QAAAA,IAAA,CAAA4B,SAAA;QAAA;QACA6D,MAAA,CAAA3H,wBAAA,GAAAiE,QAAA,CAAAC,IAAA,CAAAjC,GAAA,WAAAC,IAAA;UACA;UACA,IAAAA,IAAA,CAAA+F,kBAAA;YACA,IAAAC,WAAA,GAAAtB,MAAA,CAAA1E,IAAA,CAAAvD,kBAAA;YACA,IAAAwJ,UAAA,GAAAvB,MAAA,CAAA1E,IAAA,CAAAiG,UAAA;YACA,IAAAC,UAAA,OAAApD,iBAAA,EAAAmD,UAAA,EAAA3C,QAAA,CAAA0C,WAAA,EAAA7C,KAAA;;YAEA;YACAnD,IAAA,CAAAgD,oBAAA,GAAAkD,UAAA,OAAAA,UAAA;UACA;YACAlG,IAAA,CAAAgD,oBAAA;UACA;;UAEA;UACA,IAAAhD,IAAA,CAAAmG,aAAA;YACAnG,IAAA,CAAAoG,WAAA;UACA;UAEA,IAAAX,MAAA,CAAAhI,YAAA,CAAA0H,gBAAA,KAAAnF,IAAA,CAAA4F,mBAAA;YACAH,MAAA,CAAA5K,kBAAA,CAAAyH,IAAA,CAAAtC,IAAA;YACAyF,MAAA,CAAAY,SAAA;cACAZ,MAAA,CAAA9E,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAxC,IAAA;YACA;UACA;UAEA,OAAAA,IAAA;QACA;;QAEA;QACAyF,MAAA,CAAAvK,KAAA,GAAA6G,QAAA,CAAA7G,KAAA;;QAEA;QACA,IAAAuK,MAAA,CAAAvI,YAAA,UAAAuI,MAAA,CAAA9E,KAAA,CAAA4B,KAAA;UACAkD,MAAA,CAAAY,SAAA;YACAZ,MAAA,CAAA3H,wBAAA,CAAAsE,OAAA,WAAApC,IAAA;cACA,IAAAA,IAAA,CAAAgF,eAAA;gBACAS,MAAA,CAAA9E,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAxC,IAAA;cACA;YACA;UACA;QACA;MACA,GACAsG,KAAA,WAAA/B,KAAA;QACAgC,OAAA,CAAAhC,KAAA,iBAAAA,KAAA;QACAkB,MAAA,CAAA3E,QAAA,CAAAyD,KAAA;MACA,GACA9B,OAAA;QACAgD,MAAA,CAAAnK,WAAA,CAAAsK,mBAAA;QACAH,MAAA,CAAAnK,WAAA,CAAA0J,eAAA;QACAS,MAAA,CAAA7K,OAAA;QACA6K,MAAA,CAAAtI,+BAAA;MACA;IACA;IACA;IACAqJ,cAAA,WAAAA,eAAAC,YAAA;MACA,KAAA9D,aAAA;MACA,KAAAlF,YAAA,GAAAgJ,YAAA;MACA,KAAAvJ,YAAA;MACA,KAAA5B,WAAA,CAAAsK,mBAAA,QAAAnI,YAAA,CAAA0H,gBAAA;MACA,KAAA7J,WAAA,CAAA0J,eAAA;MACA,KAAAQ,4BAAA;MACA,KAAA3H,YAAA;IACA;IACA;IACA6I,iBAAA,WAAAA,kBAAA;MACA,KAAA/D,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA;IACA8I,oBAAA,WAAAA,qBAAA;MACA,KAAAhE,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA;IACA+I,oBAAA,WAAAA,qBAAA;MACA,KAAAjE,aAAA;MACA,KAAAlF,YAAA,CAAA8B,eAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAA5G,IAAA,CAAA6G,KAAA;MACA,KAAAjE,YAAA;MACA,KAAAW,YAAA;IACA;IACA4B,SAAA,EAAAA,eAAA;IACAoH,wCAAA,WAAAA,yCAAAC,SAAA,EAAAC,GAAA;MACAA,GAAA,CAAAC,wBAAA,GAAAF,SAAA;MACA,KAAA/I,mBAAA,GAAA+I,SAAA;IACA;IACA;IACAG,aAAA,WAAAA,cAAAF,GAAA;MACA,YAAAhJ,mBAAA,CAAAmE,QAAA,CAAA6E,GAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAA5M,IAAA,GAAA0M,KAAA,CAAA1M,IAAA;MACA,IAAA6M,IAAA;MACA,IAAAC,gBAAA,IACA,0EACA,kFACA,sFACA,iEACA;MACA;MACA,IAAAC,cAAA;MACAH,OAAA,CAAAjF,OAAA,WAAAqF,MAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,IAAA,CAAAI,KAAA;QACA;UACA,IAAAC,IAAA,GAAAF,MAAA,CAAAG,QAAA;UACA,IAAA1M,KAAA;;UAEA,IAAAyM,IAAA,qBAAAA,IAAA,sBAAAA,IAAA;YACAzM,KAAA,GAAAkM,MAAA,CAAAvM,kBAAA,CAAAgN,MAAA,WAAAC,GAAA,EAAAf,GAAA;cACA,IAAAA,GAAA,CAAAnF,SAAA;gBACA,WAAAkB,iBAAA,EAAAgF,GAAA,EAAA/E,GAAA,CAAA2B,MAAA,CAAAqC,GAAA,CAAAY,IAAA,SAAAxE,KAAA;cACA;cACA,OAAA2E,GAAA;YACA;UACA;YACA5M,KAAA,GAAAkM,MAAA,CAAAvM,kBAAA,CAAAgN,MAAA,WAAAC,GAAA,EAAAf,GAAA;cAAA,OACA,IAAAjE,iBAAA,EAAAgF,GAAA,EAAA/E,GAAA,CAAA2B,MAAA,CAAAqC,GAAA,CAAAY,IAAA,SAAAxE,KAAA;YAAA;UACA;UAEAmE,IAAA,CAAAI,KAAA,IAAAxM,KAAA;UACA;UACAsM,cAAA,CAAAC,MAAA,CAAAG,QAAA,IAAA1M,KAAA;QACA;MACA;;MAEA;MACA;MACA6M,MAAA,CAAAC,IAAA,CAAAR,cAAA,EAAApF,OAAA,WAAA6F,KAAA;QACA,IAAAb,MAAA,CAAA3J,YAAA;UACA2J,MAAA,CAAA3J,YAAA,CAAAwK,KAAA,IAAAT,cAAA,CAAAS,KAAA;QACA;MACA;;MAEA,KAAArF,YAAA;MAEA,OAAA0E,IAAA;IACA;IACAY,6BAAA,WAAAA,8BAAApB,SAAA;MAAA,IAAAqB,MAAA;MACA;MACA,IAAAC,QAAA,QAAAzH,KAAA,CAAA4B,KAAA,CAAA8F,KAAA,CAAAC,MAAA,CAAA7N,IAAA;MACA;MACA,IAAA8N,WAAA,OAAAC,mBAAA,CAAAC,OAAA,OAAA3N,GAAA;;MAEA;MACA,KAAAA,GAAA;MACA,KAAAA,GAAA,GAAAgM,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA6B,WAAA;MAAA;MACA;MACA,IAAA6G,aAAA,QAAA5N,GAAA,CAAAgL,MAAA,WAAA6C,EAAA;QAAA,QAAAJ,WAAA,CAAArG,QAAA,CAAAyG,EAAA;MAAA;MACA,IAAAC,eAAA,GAAAL,WAAA,CAAAzC,MAAA,WAAA6C,EAAA;QAAA,QAAAR,MAAA,CAAArN,GAAA,CAAAoH,QAAA,CAAAyG,EAAA;MAAA;MAEA,KAAA9N,kBAAA,GAAAiM,SAAA;MACA,KAAAnG,KAAA,CAAA4B,KAAA,CAAAsG,QAAA;;MAEA;MACA;MACA/B,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QACA,IAAA8I,KAAA,OAAApL,eAAA,EAAAyK,MAAA,CAAA1K,YAAA,CAAAzB,YAAA;QACA,IAAA+M,KAAA,OAAArL,eAAA,EAAAsC,IAAA,CAAAsF,oBAAA;QACAtF,IAAA,CAAAgJ,UAAA,GAAAF,KAAA,CAAAG,IAAA,CAAAF,KAAA;QACA,IAAAG,MAAA,GAAAlJ,IAAA,CAAAzD,WAAA;QAEA,KAAAmI,MAAA,CAAAyE,KAAA,CAAAnJ,IAAA,CAAAgJ,UAAA,KAAAhJ,IAAA,CAAAgJ,UAAA;UACA;UACA,IAAAb,MAAA,CAAA1K,YAAA,CAAAP,YAAA;YACA8C,IAAA,CAAAiD,gBAAA,OAAAH,iBAAA,EAAA9C,IAAA,CAAAgJ,UAAA,EAAAI,QAAA,CAAApJ,IAAA,CAAAqJ,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;YACA,IAAAmG,IAAA,OAAAxG,iBAAA,EAAA9C,IAAA,CAAAgJ,UAAA,EAAA1F,QAAA,CAAAtD,IAAA,CAAAuJ,eAAA,EAAApG,KAAA;YACAmG,IAAA,GAAAA,IAAA,OAAAA,IAAA;YACAtJ,IAAA,CAAAgJ,UAAA,GAAAM,IAAA;YACAtJ,IAAA,CAAAiD,gBAAA,OAAAH,iBAAA,EAAAwG,IAAA,EAAAF,QAAA,CAAApJ,IAAA,CAAAqJ,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;QACA;;QAEA;QACA,IAAAnD,IAAA,CAAAmG,aAAA,YAAAuC,aAAA,CAAAxG,QAAA,CAAAlC,IAAA,CAAA6B,WAAA;UACA;;UAEA;UACA,IAAA2H,UAAA,GAAApB,QAAA,CAAAqB,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA7H,WAAA,KAAA7B,IAAA,CAAA6B,WAAA;UAAA;;UAEA;UACA,IAAA2H,UAAA,IAAAA,UAAA,CAAAvH,QAAA,IAAAuH,UAAA,CAAAvH,QAAA,CAAA0H,MAAA;YACA;YACAxH,UAAA;cACAqH,UAAA,CAAAvH,QAAA,CAAAG,OAAA,WAAAC,KAAA;gBACA,KAAA8F,MAAA,CAAArN,GAAA,CAAAoH,QAAA,CAAAG,KAAA,CAAAR,WAAA;kBACAsG,MAAA,CAAArN,GAAA,CAAAwH,IAAA,CAAAD,KAAA,CAAAR,WAAA;kBACAsG,MAAA,CAAAtN,kBAAA,CAAAyH,IAAA,CAAAD,KAAA;kBACA8F,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;gBACA;cACA;YACA;UACA,WAAAmH,UAAA,KAAAA,UAAA,CAAAI,cAAA,IAAAJ,UAAA,CAAApD,WAAA;YACA;YACAoD,UAAA,CAAAI,cAAA;;YAEA;YACAzB,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAsH,kBAAA,CAAAL,UAAA;;YAEA;YACA;UACA;QACA;MACA;;MAEA;MACAZ,eAAA,CAAAxG,OAAA,WAAA0H,QAAA;QACA;QACA,IAAAN,UAAA,GAAApB,QAAA,CAAAqB,IAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA7H,WAAA,KAAAiI,QAAA,IAAAJ,IAAA,CAAAvD,aAAA;QAAA,CACA;QAEA,IAAAqD,UAAA,IAAAA,UAAA,CAAAvH,QAAA,IAAAuH,UAAA,CAAAvH,QAAA,CAAA0H,MAAA;UACA;UACAH,UAAA,CAAAvH,QAAA,CAAAG,OAAA,WAAAC,KAAA;YACA,IAAA0H,UAAA,GAAA5B,MAAA,CAAArN,GAAA,CAAAkP,OAAA,CAAA3H,KAAA,CAAAR,WAAA;YACA,IAAAkI,UAAA;cACA;cACA5B,MAAA,CAAArN,GAAA,CAAAmP,MAAA,CAAAF,UAAA;cACA,IAAAG,SAAA,GAAA/B,MAAA,CAAAtN,kBAAA,CAAAsP,SAAA,CACA,UAAAnK,IAAA;gBAAA,OAAAA,IAAA,CAAA6B,WAAA,KAAAQ,KAAA,CAAAR,WAAA;cAAA,CACA;cACA,IAAAqI,SAAA;gBACA/B,MAAA,CAAAtN,kBAAA,CAAAoP,MAAA,CAAAC,SAAA;cACA;cACA;cACA/B,MAAA,CAAAxH,KAAA,CAAA4B,KAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA;MAEA,KAAAO,YAAA;IACA;IACAwH,mBAAA,WAAAA,oBAAAnG,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAxG,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAAyM,QAAA;UACA;QACA;UACA,KAAA5M,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAA0M,QAAA;UACA;MAEA;IACA;IACAC,cAAA,WAAAA,eAAAxD,GAAA;MACA,KAAAtJ,YAAA,CAAAxB,cAAA,GAAA8K,GAAA,CAAAyD,OAAA;MACA,KAAA/M,YAAA,CAAAV,aAAA,GAAAgK,GAAA,CAAAwC,eAAA;MACA,KAAA3L,SAAA,GAAAmJ,GAAA;MACA,KAAAtJ,YAAA,CAAA4L,sBAAA,GAAAtC,GAAA,CAAAjK,WAAA;MACA,KAAAW,YAAA,CAAA9B,UAAA,GAAAoL,GAAA,CAAApL,UAAA;MACA;MACA,KAAA8O,YAAA;IACA;IACA,eACAtM,OAAA,WAAAA,QAAA;MAAA,IAAAuM,MAAA;MACA,KAAA9P,OAAA;MACA,IAAA+P,kCAAA,OAAArP,WAAA,EAAAwG,IAAA,WAAAC,QAAA;QACA2I,MAAA,CAAAvP,kBAAA,GAAA4G,QAAA,CAAAC,IAAA;QACA0I,MAAA,CAAAxP,KAAA,GAAA6G,QAAA,CAAA7G,KAAA;MACA,GAAAuH,OAAA;QACAiI,MAAA,CAAA9P,OAAA;MACA;IACA;IACA;IACAgQ,MAAA,WAAAA,OAAA;MACA,KAAAvP,IAAA;MACA,KAAAwP,KAAA;IACA;IACAlI,aAAA,WAAAA,cAAA;MACA,KAAAlF,YAAA;QACA0H,gBAAA;QACAxB,gBAAA;QACAlI,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAsC,oBAAA;QACArC,aAAA;QACAC,gBAAA;QACA0G,oBAAA;QACAb,sBAAA;QACAO,oBAAA;QACAC,yBAAA;QACAE,eAAA;QACAK,mBAAA;QACAC,kBAAA;QACAE,yBAAA;QACA/H,YAAA,MAAA0B,eAAA,IAAAC,MAAA;MACA;MACA,KAAAG,wBAAA;MACA,KAAAgN,SAAA;IACA;IACA;IACAD,KAAA,WAAAA,MAAA;MACA,KAAA5N,IAAA;QACAjB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;QACAwH,gBAAA;QACA1J,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA,KAAA8N,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzP,WAAA,CAAAC,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACA6M,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAlE,GAAA;MAAA,IAAAmE,MAAA;MACA,IAAAC,IAAA,GAAApE,GAAA,CAAAqE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAArJ,IAAA;QACA,WAAAyJ,4BAAA,EAAAxE,GAAA,CAAA5B,gBAAA,EAAA4B,GAAA,CAAAqE,MAAA;MACA,GAAAtJ,IAAA;QACAoJ,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAA7E,KAAA;QACAS,GAAA,CAAAqE,MAAA,GAAArE,GAAA,CAAAqE,MAAA;MACA;IACA;IACA;IACAK,qBAAA,WAAAA,sBAAA3E,SAAA;MACA,KAAAhM,GAAA,GAAAgM,SAAA,CAAA/G,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAmF,gBAAA;MAAA;MACA,KAAApK,MAAA,GAAA+L,SAAA,CAAA6C,MAAA;MACA,KAAA3O,QAAA,IAAA8L,SAAA,CAAA6C,MAAA;IACA;IACA,aACA+B,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAxP,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuQ,YAAA,WAAAA,aAAA5E,GAAA;MAAA,IAAA6E,MAAA;MACA,KAAAf,KAAA;MACA,IAAA1F,gBAAA,GAAA4B,GAAA,CAAA5B,gBAAA,SAAArK,GAAA;MACA,IAAA+Q,iCAAA,EAAA1G,gBAAA,EAAArD,IAAA,WAAAC,QAAA;QACA6J,MAAA,CAAA3O,IAAA,GAAA8E,QAAA,CAAAtH,IAAA;QACAmR,MAAA,CAAAvQ,IAAA;QACAuQ,MAAA,CAAAxQ,KAAA;MACA;IACA;IACA,WACA0Q,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAApL,KAAA,iBAAAqL,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,OAAA,CAAAtO,YAAA,CAAA0H,gBAAA;YACA,IAAAD,oCAAA,EAAA6G,OAAA,CAAAtO,YAAA,EAAAqE,IAAA,WAAAC,QAAA;cACAgK,OAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,OAAA,CAAA1Q,IAAA;cACA0Q,OAAA,CAAA5N,OAAA;YACA;UACA;YACA,IAAA4G,iCAAA,EAAAgH,OAAA,CAAAtO,YAAA,EAAAqE,IAAA,WAAAC,QAAA;cACAgK,OAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,OAAA,CAAA1Q,IAAA;cACA0Q,OAAA,CAAA5N,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+N,YAAA,WAAAA,aAAAnF,GAAA;MAAA,IAAAoF,OAAA;MACA,IAAAC,iBAAA,GAAArF,GAAA,CAAA5B,gBAAA,SAAArK,GAAA;MACA,KAAAuQ,MAAA,CAAAC,OAAA,qBAAAc,iBAAA,cAAAtK,IAAA;QACA,WAAAuK,iCAAA,EAAAD,iBAAA;MACA,GAAAtK,IAAA;QACAqK,OAAA,CAAAhO,OAAA;QACAgO,OAAA,CAAAd,MAAA,CAAAG,UAAA;MACA,GAAAlF,KAAA,cACA;IACA;IACA,aACAgG,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAAC,cAAA,CAAA/D,OAAA,MACA,KAAAnN,WAAA,qBAAAsE,MAAA,CACA,IAAAF,IAAA,GAAA+M,OAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,UAAAvP,MAAA;;MAEA;MACA,IAAAsK,KAAA,QAAA5J,wBAAA,CAAAqM,SAAA,CACA,UAAAnK,IAAA;QACA;QACA,IAAA4M,QAAA,GAAAC,MAAA,CAAA7M,IAAA,CAAAC,eAAA;QACA,IAAA6M,WAAA,GAAAD,MAAA,CAAAF,OAAA,CAAAvP,MAAA;QACA;QACA,OAAAwP,QAAA,CAAA1K,QAAA,CAAA4K,WAAA;MACA,CACA;MAEA,IAAApF,KAAA;QACA;QACA,IAAAnF,KAAA,QAAA5B,KAAA,CAAA4B,KAAA;QAEA,KAAA8D,SAAA;UACA;UACA,IAAA0G,aAAA,GAAAxK,KAAA,CAAAyK,GAAA,CAAAC,aAAA;UACA;UACA,IAAAjL,IAAA,GAAA+K,aAAA,CAAAG,gBAAA;;UAEA;UACA,IAAAC,WAAA;UACAnL,IAAA,CAAAI,OAAA,WAAA2E,GAAA,EAAAqG,GAAA;YACA,IAAAC,OAAA,GAAAtG,GAAA,CAAAuG,WAAA;YACA,IAAAD,OAAA,CAAAnL,QAAA,CAAAyK,OAAA,CAAAvP,MAAA;cACA+P,WAAA,GAAAC,GAAA;YACA;UACA;UAEA,IAAAD,WAAA;YACA,IAAAI,SAAA,GAAAvL,IAAA,CAAAmL,WAAA;YACA;YACA,IAAAK,MAAA,GAAAD,SAAA,CAAAE,SAAA;;YAEA;YACAV,aAAA,CAAAW,QAAA;cACAC,GAAA,EAAAH,MAAA,GAAAT,aAAA,CAAAa,YAAA;cACAC,QAAA;YACA;;YAEA;YACAN,SAAA,CAAAO,SAAA,CAAA/K,GAAA;YACA;YACAZ,UAAA;cACAoL,SAAA,CAAAO,SAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;QACA,KAAAjN,QAAA,CAAAC,OAAA;MACA;IACA;EACA;AACA;AAAAiN,OAAA,CAAAvF,OAAA,GAAApO,QAAA"}]}