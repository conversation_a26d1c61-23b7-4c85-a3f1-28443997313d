package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsVatInvoice;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsVatInvoiceService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 发票登记Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/system/vatinvoice")
public class RsVatInvoiceController extends BaseController {
    @Autowired
    private RsVatInvoiceService rsVatInvoiceService;

    /**
     * 查询发票登记列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsVatInvoice rsVatInvoice) {
        startPage();
        List<RsVatInvoice> list = rsVatInvoiceService.selectRsVatInvoiceList(rsVatInvoice);
        return getDataTable(list);
    }

    /**
     * 导出发票登记列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:export')")
    @Log(title = "发票登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsVatInvoice rsVatInvoice) {
        List<RsVatInvoice> list = rsVatInvoiceService.selectRsVatInvoiceList(rsVatInvoice);
        ExcelUtil<RsVatInvoice> util = new ExcelUtil<RsVatInvoice>(RsVatInvoice.class);
        util.exportExcel(response, list, "发票登记数据");
    }

    /**
     * 获取发票登记详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:query')")
    @GetMapping(value = "/{invoiceId}")
    public AjaxResult getInfo(@PathVariable("invoiceId") Long invoiceId) {
        return AjaxResult.success(rsVatInvoiceService.selectRsVatInvoiceByInvoiceId(invoiceId));
    }

    /**
     * 新增发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:add')")
    @Log(title = "发票登记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsVatInvoice rsVatInvoice) {
        return toAjax(rsVatInvoiceService.insertRsVatInvoice(rsVatInvoice));
    }

    /**
     * 修改发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:edit')")
    @Log(title = "发票登记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsVatInvoice rsVatInvoice) {
        return toAjax(rsVatInvoiceService.updateRsVatInvoice(rsVatInvoice));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setUpdateBy(getUserId());
        return toAjax(rsVatInvoiceService.changeStatus(rsVatInvoice));
    }

    /**
     * 删除发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:remove')")
    @Log(title = "发票登记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceIds}")
    public AjaxResult remove(@PathVariable Long[] invoiceIds) {
        return toAjax(rsVatInvoiceService.deleteRsVatInvoiceByInvoiceIds(invoiceIds));
    }
}
