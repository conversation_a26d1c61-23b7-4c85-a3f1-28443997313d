(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-598e1313","chunk-5daa6ecc"],{1442:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[a("el-form-item",{attrs:{label:"",prop:"sqdRctId"}},[a("el-input",{attrs:{clearable:"",placeholder:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdRctId,callback:function(t){e.$set(e.queryParams,"sqdRctId",t)},expression:"queryParams.sqdRctId"}})],1),a("el-form-item",{attrs:{label:"所属服务实例id ,",prop:"serviceId"}},[a("el-input",{attrs:{clearable:"",placeholder:"所属服务实例id ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serviceId,callback:function(t){e.$set(e.queryParams,"serviceId",t)},expression:"queryParams.serviceId"}})],1),a("el-form-item",{attrs:{label:"所属服务类型id ,",prop:"sqdServiceTypeId"}},[a("el-input",{attrs:{clearable:"",placeholder:"所属服务类型id ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdServiceTypeId,callback:function(t){e.$set(e.queryParams,"sqdServiceTypeId",t)},expression:"queryParams.sqdServiceTypeId"}})],1),a("el-form-item",{attrs:{label:"所属操作单号 ,",prop:"sqdRctNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"所属操作单号 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdRctNo,callback:function(t){e.$set(e.queryParams,"sqdRctNo",t)},expression:"queryParams.sqdRctNo"}})],1),a("el-form-item",{attrs:{label:"关联收费条目 ,与此条收入相关的成本，针对报价",prop:"relatedFreightId"}},[a("el-input",{attrs:{clearable:"",placeholder:"关联收费条目 ,与此条收入相关的成本，针对报价"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.relatedFreightId,callback:function(t){e.$set(e.queryParams,"relatedFreightId",t)},expression:"queryParams.relatedFreightId"}})],1),a("el-form-item",{attrs:{label:"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可",prop:"isRecievingOrPaying"}},[a("el-input",{attrs:{clearable:"",placeholder:"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isRecievingOrPaying,callback:function(t){e.$set(e.queryParams,"isRecievingOrPaying",t)},expression:"queryParams.isRecievingOrPaying"}})],1),a("el-form-item",{attrs:{label:"结算公司 ,(应收/应付)",prop:"clearingCompanyId"}},[a("el-input",{attrs:{clearable:"",placeholder:"结算公司 ,(应收/应付)"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clearingCompanyId,callback:function(t){e.$set(e.queryParams,"clearingCompanyId",t)},expression:"queryParams.clearingCompanyId"}})],1),a("el-form-item",{attrs:{label:"结算公司概要 ,(应收/应付)",prop:"clearingCompanySummary"}},[a("el-input",{attrs:{clearable:"",placeholder:"结算公司概要 ,(应收/应付)"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clearingCompanySummary,callback:function(t){e.$set(e.queryParams,"clearingCompanySummary",t)},expression:"queryParams.clearingCompanySummary"}})],1),a("el-form-item",{attrs:{label:"报价策略 ,",prop:"quotationStrategyId"}},[a("el-input",{attrs:{clearable:"",placeholder:"报价策略 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.quotationStrategyId,callback:function(t){e.$set(e.queryParams,"quotationStrategyId",t)},expression:"queryParams.quotationStrategyId"}})],1),a("el-form-item",{attrs:{label:"费用名称ID ,dn=debit note账单",prop:"dnChargeNameId"}},[a("el-input",{attrs:{clearable:"",placeholder:"费用名称ID ,dn=debit note账单"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnChargeNameId,callback:function(t){e.$set(e.queryParams,"dnChargeNameId",t)},expression:"queryParams.dnChargeNameId"}})],1),a("el-form-item",{attrs:{label:"账单币种 ,",prop:"dnCurrencyCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"账单币种 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnCurrencyCode,callback:function(t){e.$set(e.queryParams,"dnCurrencyCode",t)},expression:"queryParams.dnCurrencyCode"}})],1),a("el-form-item",{attrs:{label:"计费单价 ,",prop:"dnUnitRate"}},[a("el-input",{attrs:{clearable:"",placeholder:"计费单价 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnUnitRate,callback:function(t){e.$set(e.queryParams,"dnUnitRate",t)},expression:"queryParams.dnUnitRate"}})],1),a("el-form-item",{attrs:{label:"计费单位 ,",prop:"dnUnitCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"计费单位 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnUnitCode,callback:function(t){e.$set(e.queryParams,"dnUnitCode",t)},expression:"queryParams.dnUnitCode"}})],1),a("el-form-item",{attrs:{label:"计费数量 ,",prop:"dnAmount"}},[a("el-input",{attrs:{clearable:"",placeholder:"计费数量 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnAmount,callback:function(t){e.$set(e.queryParams,"dnAmount",t)},expression:"queryParams.dnAmount"}})],1),a("el-form-item",{attrs:{label:"本位币汇率 ,",prop:"basicCurrencyRate"}},[a("el-input",{attrs:{clearable:"",placeholder:"本位币汇率 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.basicCurrencyRate,callback:function(t){e.$set(e.queryParams,"basicCurrencyRate",t)},expression:"queryParams.basicCurrencyRate"}})],1),a("el-form-item",{attrs:{label:"税率 ,",prop:"dutyRate"}},[a("el-input",{attrs:{clearable:"",placeholder:"税率 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dutyRate,callback:function(t){e.$set(e.queryParams,"dutyRate",t)},expression:"queryParams.dutyRate"}})],1),a("el-form-item",{attrs:{label:"金额小计 ",prop:"subtotal"}},[a("el-input",{attrs:{clearable:"",placeholder:"金额小计 "},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.subtotal,callback:function(t){e.$set(e.queryParams,"subtotal",t)},expression:"queryParams.subtotal"}})],1),a("el-form-item",{attrs:{label:"费用备注 ,",prop:"chargeRemark"}},[a("el-input",{attrs:{clearable:"",placeholder:"费用备注 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.chargeRemark,callback:function(t){e.$set(e.queryParams,"chargeRemark",t)},expression:"queryParams.chargeRemark"}})],1),a("el-form-item",{attrs:{label:"结算币种ID ,",prop:"clearingCurrencyCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"结算币种ID ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clearingCurrencyCode,callback:function(t){e.$set(e.queryParams,"clearingCurrencyCode",t)},expression:"queryParams.clearingCurrencyCode"}})],1),a("el-form-item",{attrs:{label:"折合账单币种已收 ,",prop:"dnCurrencyReceived"}},[a("el-input",{attrs:{clearable:"",placeholder:"折合账单币种已收 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnCurrencyReceived,callback:function(t){e.$set(e.queryParams,"dnCurrencyReceived",t)},expression:"queryParams.dnCurrencyReceived"}})],1),a("el-form-item",{attrs:{label:"折合账单币种已付 ,",prop:"dnCurrencyPaid"}},[a("el-input",{attrs:{clearable:"",placeholder:"折合账单币种已付 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnCurrencyPaid,callback:function(t){e.$set(e.queryParams,"dnCurrencyPaid",t)},expression:"queryParams.dnCurrencyPaid"}})],1),a("el-form-item",{attrs:{label:"本条目账单币种余额 ,",prop:"dnCurrencyBalance"}},[a("el-input",{attrs:{clearable:"",placeholder:"本条目账单币种余额 ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dnCurrencyBalance,callback:function(t){e.$set(e.queryParams,"dnCurrencyBalance",t)},expression:"queryParams.dnCurrencyBalance"}})],1),a("el-form-item",{attrs:{label:"财务已收销账流水号List ,",prop:"accountReceivedIdList"}},[a("el-input",{attrs:{clearable:"",placeholder:"财务已收销账流水号List ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.accountReceivedIdList,callback:function(t){e.$set(e.queryParams,"accountReceivedIdList",t)},expression:"queryParams.accountReceivedIdList"}})],1),a("el-form-item",{attrs:{label:"财务已付销账流水号List ,",prop:"accountPaidIdList"}},[a("el-input",{attrs:{clearable:"",placeholder:"财务已付销账流水号List ,"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.accountPaidIdList,callback:function(t){e.$set(e.queryParams,"accountPaidIdList",t)},expression:"queryParams.accountPaidIdList"}})],1),a("el-form-item",{attrs:{label:"发票查询编号 ,发票查询编号",prop:"logisticsInvoiceIdList"}},[a("el-input",{attrs:{clearable:"",placeholder:"发票查询编号 ,发票查询编号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logisticsInvoiceIdList,callback:function(t){e.$set(e.queryParams,"logisticsInvoiceIdList",t)},expression:"queryParams.logisticsInvoiceIdList"}})],1),a("el-form-item",{attrs:{label:"服务细目code ",prop:"sqdServiceDetailsCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"服务细目code "},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdServiceDetailsCode,callback:function(t){e.$set(e.queryParams,"sqdServiceDetailsCode",t)},expression:"queryParams.sqdServiceDetailsCode"}})],1),a("el-form-item",{attrs:{label:"付款抬头 ",prop:"paymentTitleCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"付款抬头 "},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.paymentTitleCode,callback:function(t){e.$set(e.queryParams,"paymentTitleCode",t)},expression:"queryParams.paymentTitleCode"}})],1),a("el-form-item",{attrs:{label:"结款方式",prop:"logisticsPaymentTermsCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"结款方式"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logisticsPaymentTermsCode,callback:function(t){e.$set(e.queryParams,"logisticsPaymentTermsCode",t)},expression:"queryParams.logisticsPaymentTermsCode"}})],1),a("el-form-item",{attrs:{label:"财务审核标记",prop:"isAccountConfirmed"}},[a("el-input",{attrs:{clearable:"",placeholder:"财务审核标记"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isAccountConfirmed,callback:function(t){e.$set(e.queryParams,"isAccountConfirmed",t)},expression:"queryParams.isAccountConfirmed"}})],1),a("el-form-item",{attrs:{label:"已销账金额",prop:"sqdDnCurrencyPaid"}},[a("el-input",{attrs:{clearable:"",placeholder:"已销账金额"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdDnCurrencyPaid,callback:function(t){e.$set(e.queryParams,"sqdDnCurrencyPaid",t)},expression:"queryParams.sqdDnCurrencyPaid"}})],1),a("el-form-item",{attrs:{label:"未销账余额",prop:"sqdDnCurrencyBalance"}},[a("el-input",{attrs:{clearable:"",placeholder:"未销账余额"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdDnCurrencyBalance,callback:function(t){e.$set(e.queryParams,"sqdDnCurrencyBalance",t)},expression:"queryParams.sqdDnCurrencyBalance"}})],1),a("el-form-item",{attrs:{label:"销账流水号List",prop:"sqdWriteoffNoList"}},[a("el-input",{attrs:{clearable:"",placeholder:"销账流水号List"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdWriteoffNoList,callback:function(t){e.$set(e.queryParams,"sqdWriteoffNoList",t)},expression:"queryParams.sqdWriteoffNoList"}})],1),a("el-form-item",{attrs:{label:"已开票金额",prop:"sqdInvoiceIssued"}},[a("el-input",{attrs:{clearable:"",placeholder:"已开票金额"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdInvoiceIssued,callback:function(t){e.$set(e.queryParams,"sqdInvoiceIssued",t)},expression:"queryParams.sqdInvoiceIssued"}})],1),a("el-form-item",{attrs:{label:"未开票余额 ",prop:"sqdInvoiceBalance"}},[a("el-input",{attrs:{clearable:"",placeholder:"未开票余额 "},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdInvoiceBalance,callback:function(t){e.$set(e.queryParams,"sqdInvoiceBalance",t)},expression:"queryParams.sqdInvoiceBalance"}})],1),a("el-form-item",{attrs:{label:"",prop:"currencyRateCalculateDate"}},[a("el-date-picker",{attrs:{clearable:"",placeholder:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.currencyRateCalculateDate,callback:function(t){e.$set(e.queryParams,"currencyRateCalculateDate",t)},expression:"queryParams.currencyRateCalculateDate"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:charge:export"],expression:"['system:charge:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{on:{click:function(t){e.openAggregator=!0}}},[e._v("数据汇总")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{visible:e.openAggregator,"append-to-body":"",width:"80%"},on:{"update:visible":function(t){e.openAggregator=t}}},[a("data-aggregator-back-ground",{attrs:{"aggregate-function":e.getAggregator,"data-source":e.aggregatorList,"data-source-type":"charges","config-type":"charges-agg","field-label-map":e.rsChargeFieldLabelMap}})],1)],1),a("el-col",{attrs:{span:1.5}},[a("dynamic-search",{attrs:{"search-fields":e.rsChargeSearchFields,"config-type":"charges-search"},on:{reset:e.resetQuery,search:function(t){return e.handleQuery(t)}}})],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.chargeList,border:"",stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"序号",type:"index",width:"30"}}),a("el-table-column",{attrs:{type:"selection",width:"35"}}),a("el-table-column",{attrs:{align:"center",label:"财务审核",prop:"sqdRctNo",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s("1"===t.row.isAccountConfirmed?"√":"-")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"销账状态",prop:"sqdRctNo",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s("1"==t.row.writeoffStatus?"√":"-")+" ")]}}])}),a("el-table-column",{attrs:{label:"操作单号",prop:"sqdRctNo",width:"100"}}),a("el-table-column",{attrs:{label:"计费货量",prop:"revenueTon",width:"100"}}),a("el-table-column",{attrs:{label:"ETA",prop:"eta",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.eta,"{y}-{m}-{d}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"ETD",prop:"etd",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.etd,"{y}-{m}-{d}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"收付",prop:"etd",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(0==t.row.isRecievingOrPaying?"收":"付")+" ")]}}])}),a("el-table-column",{attrs:{label:"结算单位",prop:"companyName"}}),a("el-table-column",{attrs:{label:"委托单位",prop:"clientSummary"}}),a("el-table-column",{attrs:{label:"订单所属",prop:"orderBelongsTo"}}),a("el-table-column",{attrs:{label:"费用名称",prop:"chargeName",width:"80"}}),a("el-table-column",{attrs:{align:"right",label:"单价",prop:"dnUnitRate",width:"80"}}),a("el-table-column",{attrs:{align:"right",label:"数量",prop:"dnAmount",width:"80"}}),a("el-table-column",{attrs:{label:"币种",prop:"dnCurrencyCode"}}),a("el-table-column",{attrs:{label:"汇率",prop:"basicCurrencyRate"}}),a("el-table-column",{attrs:{align:"right",label:"启运港",prop:"dnCurrencyCode"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.pol)+" ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"目的港",prop:"dnCurrencyCode"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.destinationPort)+" ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"提单号",prop:"dnCurrencyCode","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.blNo)+" ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"柜号",prop:"dnCurrencyCode","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdContainersSealsSum)+" ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"税率",prop:"dnCurrencyCode"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.dutyRate)+"% ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"小计",prop:"dnUnitRate"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.subtotal)+" ")]}}])}),a("el-table-column",{attrs:{align:"right",label:"已收小计",prop:"sqdDnCurrencyPaid"}}),a("el-table-column",{attrs:{align:"right",label:"已付小计",prop:"sqdDnCurrencyPaid"}}),a("el-table-column",{attrs:{align:"right",label:"应收RMB",prop:"receivableRmb"}}),a("el-table-column",{attrs:{align:"right",label:"应收USD",prop:"receivableUsd"}}),a("el-table-column",{attrs:{align:"right",label:"未收RMB",prop:"uncollectedRmb"}}),a("el-table-column",{attrs:{align:"right",label:"未收USD",prop:"uncollectedUsd"}}),a("el-table-column",{attrs:{align:"right",label:"应付RMB",prop:"payableRmb"}}),a("el-table-column",{attrs:{align:"right",label:"应付USD",prop:"payableUsd"}}),a("el-table-column",{attrs:{align:"right",label:"未付RMB",prop:"unpaidRmb"}}),a("el-table-column",{attrs:{align:"right",label:"未付USD",prop:"unpaidUsd"}}),a("el-table-column",{attrs:{align:"right",label:"已销账金额",prop:"sqdDnCurrencyPaid"}}),a("el-table-column",{attrs:{align:"right",label:"未销账余额",prop:"sqdDnCurrencyBalance"}}),a("el-table-column",{attrs:{align:"right",label:"备注",prop:"chargeRemark","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"right",label:"付款抬头",prop:"paymentTitleCode"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}}),e.selectedRows.length>0?a("div",{staticClass:"statistics-area"},[a("el-alert",{attrs:{closable:!1,"show-icon":"",title:"已选择记录统计",type:"info"}},[a("div",{staticClass:"statistics-content"},[a("span",{staticClass:"statistics-item"},[a("span",{staticClass:"label"},[e._v("已选择记录数:")]),a("span",{staticClass:"value"},[e._v(e._s(e.selectedRows.length))])]),a("span",{staticClass:"statistics-item"},[a("span",{staticClass:"label"},[e._v("未销账余额合计:")]),a("span",{staticClass:"value"},[e._v(e._s(e.formattedTotalBalance))])])])])],1):e._e()],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"",prop:"sqdRctId"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.sqdRctId,callback:function(t){e.$set(e.form,"sqdRctId",t)},expression:"form.sqdRctId"}})],1),a("el-form-item",{attrs:{label:"所属服务实例id ,",prop:"serviceId"}},[a("el-input",{attrs:{placeholder:"所属服务实例id ,"},model:{value:e.form.serviceId,callback:function(t){e.$set(e.form,"serviceId",t)},expression:"form.serviceId"}})],1),a("el-form-item",{attrs:{label:"所属服务类型id ,",prop:"sqdServiceTypeId"}},[a("el-input",{attrs:{placeholder:"所属服务类型id ,"},model:{value:e.form.sqdServiceTypeId,callback:function(t){e.$set(e.form,"sqdServiceTypeId",t)},expression:"form.sqdServiceTypeId"}})],1),a("el-form-item",{attrs:{label:"所属操作单号 ,",prop:"sqdRctNo"}},[a("el-input",{attrs:{placeholder:"所属操作单号 ,"},model:{value:e.form.sqdRctNo,callback:function(t){e.$set(e.form,"sqdRctNo",t)},expression:"form.sqdRctNo"}})],1),a("el-form-item",{attrs:{label:"关联收费条目 ,与此条收入相关的成本，针对报价",prop:"relatedFreightId"}},[a("el-input",{attrs:{placeholder:"关联收费条目 ,与此条收入相关的成本，针对报价"},model:{value:e.form.relatedFreightId,callback:function(t){e.$set(e.form,"relatedFreightId",t)},expression:"form.relatedFreightId"}})],1),a("el-form-item",{attrs:{label:"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可",prop:"isRecievingOrPaying"}},[a("el-input",{attrs:{placeholder:"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可"},model:{value:e.form.isRecievingOrPaying,callback:function(t){e.$set(e.form,"isRecievingOrPaying",t)},expression:"form.isRecievingOrPaying"}})],1),a("el-form-item",{attrs:{label:"结算公司 ,(应收/应付)",prop:"clearingCompanyId"}},[a("el-input",{attrs:{placeholder:"结算公司 ,(应收/应付)"},model:{value:e.form.clearingCompanyId,callback:function(t){e.$set(e.form,"clearingCompanyId",t)},expression:"form.clearingCompanyId"}})],1),a("el-form-item",{attrs:{label:"结算公司概要 ,(应收/应付)",prop:"clearingCompanySummary"}},[a("el-input",{attrs:{placeholder:"结算公司概要 ,(应收/应付)"},model:{value:e.form.clearingCompanySummary,callback:function(t){e.$set(e.form,"clearingCompanySummary",t)},expression:"form.clearingCompanySummary"}})],1),a("el-form-item",{attrs:{label:"报价策略 ,",prop:"quotationStrategyId"}},[a("el-input",{attrs:{placeholder:"报价策略 ,"},model:{value:e.form.quotationStrategyId,callback:function(t){e.$set(e.form,"quotationStrategyId",t)},expression:"form.quotationStrategyId"}})],1),a("el-form-item",{attrs:{label:"费用名称ID ,dn=debit note账单",prop:"dnChargeNameId"}},[a("el-input",{attrs:{placeholder:"费用名称ID ,dn=debit note账单"},model:{value:e.form.dnChargeNameId,callback:function(t){e.$set(e.form,"dnChargeNameId",t)},expression:"form.dnChargeNameId"}})],1),a("el-form-item",{attrs:{label:"账单币种 ,",prop:"dnCurrencyCode"}},[a("el-input",{attrs:{placeholder:"账单币种 ,"},model:{value:e.form.dnCurrencyCode,callback:function(t){e.$set(e.form,"dnCurrencyCode",t)},expression:"form.dnCurrencyCode"}})],1),a("el-form-item",{attrs:{label:"计费单价 ,",prop:"dnUnitRate"}},[a("el-input",{attrs:{placeholder:"计费单价 ,"},model:{value:e.form.dnUnitRate,callback:function(t){e.$set(e.form,"dnUnitRate",t)},expression:"form.dnUnitRate"}})],1),a("el-form-item",{attrs:{label:"计费单位 ,",prop:"dnUnitCode"}},[a("el-input",{attrs:{placeholder:"计费单位 ,"},model:{value:e.form.dnUnitCode,callback:function(t){e.$set(e.form,"dnUnitCode",t)},expression:"form.dnUnitCode"}})],1),a("el-form-item",{attrs:{label:"计费数量 ,",prop:"dnAmount"}},[a("el-input",{attrs:{placeholder:"计费数量 ,"},model:{value:e.form.dnAmount,callback:function(t){e.$set(e.form,"dnAmount",t)},expression:"form.dnAmount"}})],1),a("el-form-item",{attrs:{label:"本位币汇率 ,",prop:"basicCurrencyRate"}},[a("el-input",{attrs:{placeholder:"本位币汇率 ,"},model:{value:e.form.basicCurrencyRate,callback:function(t){e.$set(e.form,"basicCurrencyRate",t)},expression:"form.basicCurrencyRate"}})],1),a("el-form-item",{attrs:{label:"税率 ,",prop:"dutyRate"}},[a("el-input",{attrs:{placeholder:"税率 ,"},model:{value:e.form.dutyRate,callback:function(t){e.$set(e.form,"dutyRate",t)},expression:"form.dutyRate"}})],1),a("el-form-item",{attrs:{label:"金额小计 ",prop:"subtotal"}},[a("el-input",{attrs:{placeholder:"金额小计 "},model:{value:e.form.subtotal,callback:function(t){e.$set(e.form,"subtotal",t)},expression:"form.subtotal"}})],1),a("el-form-item",{attrs:{label:"费用备注 ,",prop:"chargeRemark"}},[a("el-input",{attrs:{placeholder:"费用备注 ,"},model:{value:e.form.chargeRemark,callback:function(t){e.$set(e.form,"chargeRemark",t)},expression:"form.chargeRemark"}})],1),a("el-form-item",{attrs:{label:"结算币种ID ,",prop:"clearingCurrencyCode"}},[a("el-input",{attrs:{placeholder:"结算币种ID ,"},model:{value:e.form.clearingCurrencyCode,callback:function(t){e.$set(e.form,"clearingCurrencyCode",t)},expression:"form.clearingCurrencyCode"}})],1),a("el-form-item",{attrs:{label:"折合账单币种已收 ,",prop:"dnCurrencyReceived"}},[a("el-input",{attrs:{placeholder:"折合账单币种已收 ,"},model:{value:e.form.dnCurrencyReceived,callback:function(t){e.$set(e.form,"dnCurrencyReceived",t)},expression:"form.dnCurrencyReceived"}})],1),a("el-form-item",{attrs:{label:"折合账单币种已付 ,",prop:"dnCurrencyPaid"}},[a("el-input",{attrs:{placeholder:"折合账单币种已付 ,"},model:{value:e.form.dnCurrencyPaid,callback:function(t){e.$set(e.form,"dnCurrencyPaid",t)},expression:"form.dnCurrencyPaid"}})],1),a("el-form-item",{attrs:{label:"本条目账单币种余额 ,",prop:"dnCurrencyBalance"}},[a("el-input",{attrs:{placeholder:"本条目账单币种余额 ,"},model:{value:e.form.dnCurrencyBalance,callback:function(t){e.$set(e.form,"dnCurrencyBalance",t)},expression:"form.dnCurrencyBalance"}})],1),a("el-form-item",{attrs:{label:"财务已收销账流水号List ,",prop:"accountReceivedIdList"}},[a("el-input",{attrs:{placeholder:"财务已收销账流水号List ,"},model:{value:e.form.accountReceivedIdList,callback:function(t){e.$set(e.form,"accountReceivedIdList",t)},expression:"form.accountReceivedIdList"}})],1),a("el-form-item",{attrs:{label:"财务已付销账流水号List ,",prop:"accountPaidIdList"}},[a("el-input",{attrs:{placeholder:"财务已付销账流水号List ,"},model:{value:e.form.accountPaidIdList,callback:function(t){e.$set(e.form,"accountPaidIdList",t)},expression:"form.accountPaidIdList"}})],1),a("el-form-item",{attrs:{label:"发票查询编号 ,发票查询编号",prop:"logisticsInvoiceIdList"}},[a("el-input",{attrs:{placeholder:"发票查询编号 ,发票查询编号"},model:{value:e.form.logisticsInvoiceIdList,callback:function(t){e.$set(e.form,"logisticsInvoiceIdList",t)},expression:"form.logisticsInvoiceIdList"}})],1),a("el-form-item",{attrs:{label:"服务细目code ",prop:"sqdServiceDetailsCode"}},[a("el-input",{attrs:{placeholder:"服务细目code "},model:{value:e.form.sqdServiceDetailsCode,callback:function(t){e.$set(e.form,"sqdServiceDetailsCode",t)},expression:"form.sqdServiceDetailsCode"}})],1),a("el-form-item",{attrs:{label:"付款抬头 ",prop:"paymentTitleCode"}},[a("el-input",{attrs:{placeholder:"付款抬头 "},model:{value:e.form.paymentTitleCode,callback:function(t){e.$set(e.form,"paymentTitleCode",t)},expression:"form.paymentTitleCode"}})],1),a("el-form-item",{attrs:{label:"结款方式",prop:"logisticsPaymentTermsCode"}},[a("el-input",{attrs:{placeholder:"结款方式"},model:{value:e.form.logisticsPaymentTermsCode,callback:function(t){e.$set(e.form,"logisticsPaymentTermsCode",t)},expression:"form.logisticsPaymentTermsCode"}})],1),a("el-form-item",{attrs:{label:"财务审核标记",prop:"isAccountConfirmed"}},[a("el-input",{attrs:{placeholder:"财务审核标记"},model:{value:e.form.isAccountConfirmed,callback:function(t){e.$set(e.form,"isAccountConfirmed",t)},expression:"form.isAccountConfirmed"}})],1),a("el-form-item",{attrs:{label:"已销账金额",prop:"sqdDnCurrencyPaid"}},[a("el-input",{attrs:{placeholder:"已销账金额"},model:{value:e.form.sqdDnCurrencyPaid,callback:function(t){e.$set(e.form,"sqdDnCurrencyPaid",t)},expression:"form.sqdDnCurrencyPaid"}})],1),a("el-form-item",{attrs:{label:"未销账余额",prop:"sqdDnCurrencyBalance"}},[a("el-input",{attrs:{placeholder:"未销账余额"},model:{value:e.form.sqdDnCurrencyBalance,callback:function(t){e.$set(e.form,"sqdDnCurrencyBalance",t)},expression:"form.sqdDnCurrencyBalance"}})],1),a("el-form-item",{attrs:{label:"销账流水号List",prop:"sqdWriteoffNoList"}},[a("el-input",{attrs:{placeholder:"销账流水号List"},model:{value:e.form.sqdWriteoffNoList,callback:function(t){e.$set(e.form,"sqdWriteoffNoList",t)},expression:"form.sqdWriteoffNoList"}})],1),a("el-form-item",{attrs:{label:"已开票金额",prop:"sqdInvoiceIssued"}},[a("el-input",{attrs:{placeholder:"已开票金额"},model:{value:e.form.sqdInvoiceIssued,callback:function(t){e.$set(e.form,"sqdInvoiceIssued",t)},expression:"form.sqdInvoiceIssued"}})],1),a("el-form-item",{attrs:{label:"未开票余额 ",prop:"sqdInvoiceBalance"}},[a("el-input",{attrs:{placeholder:"未开票余额 "},model:{value:e.form.sqdInvoiceBalance,callback:function(t){e.$set(e.form,"sqdInvoiceBalance",t)},expression:"form.sqdInvoiceBalance"}})],1),a("el-form-item",{attrs:{label:"",prop:"currencyRateCalculateDate"}},[a("el-date-picker",{attrs:{clearable:"",placeholder:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.currencyRateCalculateDate,callback:function(t){e.$set(e.form,"currencyRateCalculateDate",t)},expression:"form.currencyRateCalculateDate"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],l=a("5530"),i=(a("b680"),a("e9c4"),a("13d5"),a("d3b7"),a("fff5")),o=a("72f9"),s=a.n(o),c={chargeId:{label:"费用条目ID",type:"input",placeholder:"请输入费用条目ID"},sqdRctNo:{label:"操作单号",type:"input",placeholder:"请输入操作单号"},orderBelongsTo:{label:"订单所属",type:"input",placeholder:"请输入订单所属"},isRecievingOrPaying:{label:"收支标志",type:"select",placeholder:"请选择收支类型",options:[{label:"应收",value:"0"},{label:"应付",value:"1"}]},clearingCompanyId:{label:"结算公司",type:"company",placeholder:"请选择结算公司",multiple:!1,noParent:!0,roleClient:"1"},clientId:{label:"委托单位",type:"company",placeholder:"请选择委托单位",multiple:!1,noParent:!0,roleClient:"1"},dnCurrencyCode:{label:"账单币种",type:"select",placeholder:"请选择币种",options:[{label:"人民币",value:"RMB"},{label:"美元",value:"USD"},{label:"欧元",value:"EUR"},{label:"英镑",value:"GBP"},{label:"日元",value:"JPY"}]},subtotalRange:{label:"金额小计",type:"date",placeholder:"请选择金额范围"},sqdServiceTypeId:{label:"服务类型",type:"tree-select",placeholder:"请选择服务类型",treeType:"serviceType",dLoad:!0,flat:!1,multiple:!1},dnChargeNameId:{label:"费用名称",type:"tree-select",placeholder:"请选择费用名称",treeType:"chargeName",dLoad:!0,flat:!1,multiple:!1},currencyRateCalculateDate:{label:"汇率日期",type:"date",placeholder:"请选择汇率计算日期"},writeoffStatus:{label:"销账状态",type:"select",placeholder:"请选择销账状态",options:[{label:"未销账",value:"0"},{label:"已销账",value:"1"}]},isAccountConfirmed:{label:"财务审核",type:"select",placeholder:"请选择审核状态",options:[{label:"未审核",value:"0"},{label:"已审核",value:"1"}]},sqdDnCurrencyBalance:{label:"未收余额",type:"number",placeholder:"请选择未销账余额范围"},chargeRemark:{label:"费用备注",type:"input",placeholder:"请输入费用备注关键词"},sqdServiceDetailsCode:{label:"服务细目",type:"tree-select",placeholder:"请选择服务细目",treeType:"serviceDetails",dLoad:!0,flat:!1,multiple:!1},logisticsPaymentTermsCode:{label:"结款方式",type:"select",placeholder:"请选择结款方式",options:[{label:"现金",value:"CASH"},{label:"转账",value:"TRANSFER"},{label:"信用证",value:"LC"}]},paymentTitleCode:{label:"付款抬头",type:"select",placeholder:"请选择付款抬头",remote:!0,options:"paymentTitles"},ATDDate:{label:"ATD",type:"date",placeholder:"请选择ATD日期"},ATADate:{label:"ATA",type:"date",placeholder:"请选择操作日期"},sqdInvoiceIssued:{label:"已开票金额",type:"number",placeholder:"请选择已开票金额"},sqdInvoiceBalance:{label:"未开票余额",type:"number",placeholder:"请选择未开票余额"}},d=a("de7d"),u={chargeId:{name:"费用条目ID",display:"text",aggregated:!1,align:"left",width:"120"},sqdRctId:{name:"操作单ID",display:"text",aggregated:!1,align:"left",width:"120"},serviceId:{name:"服务实例ID",display:"text",aggregated:!1,align:"left",width:"120"},sqdServiceTypeId:{name:"服务类型",display:"text",aggregated:!1,align:"left",width:"100"},sqdRctNo:{name:"操作单号",display:"text",aggregated:!1,align:"left",width:"120"},relatedFreightId:{name:"关联收费条目",display:"text",aggregated:!1,align:"left",width:"120"},isRecievingOrPaying:{name:"收支标志",display:"text",aggregated:!1,align:"center",width:"80"},clearingCompanyId:{name:"结算公司",display:"text",aggregated:!1,align:"left",width:"120"},clearingCompanySummary:{name:"结算公司概要",display:"text",aggregated:!1,align:"left",width:"150"},salesId:{name:"业务员",display:"getName",aggregated:!1,align:"left",width:"100"},quotationStrategyId:{name:"报价策略",display:"text",aggregated:!1,align:"left",width:"100"},dnChargeNameId:{name:"费用名称",display:"text",aggregated:!1,align:"left",width:"120"},dnCurrencyCode:{name:"账单币种",display:"text",aggregated:!1,align:"left",width:"80"},dnUnitRate:{name:"计费单价",display:"number",aggregated:!0,align:"right",width:"120"},dnUnitCode:{name:"计费单位",display:"text",aggregated:!1,align:"left",width:"80"},dnAmount:{name:"计费数量",display:"number",aggregated:!0,align:"right",width:"100"},basicCurrencyRate:{name:"本位币汇率",display:"number",aggregated:!1,align:"right",width:"100"},dutyRate:{name:"税率",display:"percentage",aggregated:!1,align:"right",width:"80"},subtotal:{name:"金额小计",display:"number",aggregated:!0,align:"right",width:"120"},chargeRemark:{name:"费用备注",display:"text",aggregated:!1,align:"left",width:"150"},clearingCurrencyCode:{name:"结算币种",display:"text",aggregated:!1,align:"left",width:"80"},dnCurrencyReceived:{name:"账单币种已收",display:"number",aggregated:!0,align:"right",width:"120"},dnCurrencyPaid:{name:"账单币种已付",display:"number",aggregated:!0,align:"right",width:"120"},dnCurrencyBalance:{name:"账单币种余额",display:"number",aggregated:!0,align:"right",width:"120"},accountReceivedIdList:{name:"已收销账流水号",display:"text",aggregated:!1,align:"left",width:"150"},accountPaidIdList:{name:"已付销账流水号",display:"text",aggregated:!1,align:"left",width:"150"},logisticsInvoiceIdList:{name:"发票查询编号",display:"text",aggregated:!1,align:"left",width:"150"},sqdServiceDetailsCode:{name:"服务细目",display:"text",aggregated:!1,align:"left",width:"120"},paymentTitleCode:{name:"付款抬头",display:"text",aggregated:!1,align:"left",width:"120"},logisticsPaymentTermsCode:{name:"结款方式",display:"text",aggregated:!1,align:"left",width:"100"},revenueTon:{name:"计费货量",display:"text",aggregated:!0,align:"right",width:"120"},eta:{name:"ETA",display:"date",aggregated:!0,align:"right",width:"120"},etd:{name:"ETD",display:"date",aggregated:!0,align:"right",width:"120"},isAccountConfirmed:{name:"财务审核",display:"boolean",aggregated:!1,align:"center",width:"80"},sqdDnCurrencyPaid:{name:"已销账金额",display:"number",aggregated:!0,align:"right",width:"120"},sqdDnCurrencyBalance:{name:"未收余额",display:"number",aggregated:!0,align:"right",width:"120"},sqdWriteoffNoList:{name:"销账流水号",display:"text",aggregated:!1,align:"left",width:"150"},sqdInvoiceIssued:{name:"已开票金额",display:"number",aggregated:!0,align:"right",width:"120"},sqdInvoiceBalance:{name:"未开票余额",display:"number",aggregated:!0,align:"right",width:"120"},currencyRateCalculateDate:{name:"汇率计算日期",display:"date",aggregated:!1,align:"left",width:"120"},writeoffStatus:{name:"销账状态",display:"text",aggregated:!1,align:"center",width:"80"},pol:{name:"启运港",display:"text",aggregated:!1,align:"center",width:"80"},destinationPort:{name:"目的港",display:"text",aggregated:!1,align:"center",width:"80"},blNo:{name:"提单号",display:"text",aggregated:!1,align:"center",width:"120"},sqdContainersSealsSum:{name:"柜号",display:"text",aggregated:!1,align:"center",width:"200"},receivableUsd:{name:"应收USD",display:"number",aggregated:!0,align:"right",width:"120"},receivableRmb:{name:"应收RMB",display:"number",aggregated:!0,align:"right",width:"120"},uncollectedUsd:{name:"未收USD",display:"number",aggregated:!0,align:"right",width:"120"},uncollectedRmb:{name:"未收RMB",display:"number",aggregated:!0,align:"right",width:"120"},payableUsd:{name:"应付USD",display:"number",aggregated:!0,align:"right",width:"120"},payableRmb:{name:"应付RMB",display:"number",aggregated:!0,align:"right",width:"120"},unpaidUsd:{name:"未付USD",display:"number",aggregated:!0,align:"right",width:"120"},unpaidRmb:{name:"未付RMB",display:"number",aggregated:!0,align:"right",width:"120"}},p=(a("c2aa"),a("4f70")),m=a("fba1"),f=a("cd43"),g={name:"Charges",components:{DataAggregatorBackGround:f["default"],DynamicSearch:p["a"],DataAggregator:d["default"]},data:function(){return{rsChargeSearchFields:c,showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,chargeList:[],title:"",open:!1,openAggregator:!1,aggregatorList:[],rsChargeFieldLabelMap:u,queryParams:{pageNum:1,pageSize:20,sqdRctId:null,serviceId:null,sqdServiceTypeId:null,sqdRctNo:null,relatedFreightId:null,isRecievingOrPaying:null,clearingCompanyId:null,clearingCompanySummary:null,quotationStrategyId:null,dnChargeNameId:null,dnCurrencyCode:null,dnUnitRate:null,dnUnitCode:null,dnAmount:null,basicCurrencyRate:null,dutyRate:null,subtotal:null,chargeRemark:null,clearingCurrencyCode:null,dnCurrencyReceived:null,dnCurrencyPaid:null,dnCurrencyBalance:null,accountReceivedIdList:null,accountPaidIdList:null,logisticsInvoiceIdList:null,sqdServiceDetailsCode:null,paymentTitleCode:null,logisticsPaymentTermsCode:null,isAccountConfirmed:null,sqdDnCurrencyPaid:null,sqdDnCurrencyBalance:null,sqdWriteoffNoList:null,sqdInvoiceIssued:null,sqdInvoiceBalance:null,currencyRateCalculateDate:null,writeoffStatus:null},form:{},rules:{},selectedRows:[],totalBalance:0}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},computed:{formattedTotalBalance:function(){return this.currency?this.currency(this.totalBalance).format():this.totalBalance.toFixed(2)}},methods:{getAggregator:function(e){return e.config=JSON.stringify(e.config),this.queryParams.params=e,this.queryParams.rsChargeList=this.aggregatorList,Object(i["b"])(this.queryParams)},parseTime:m["f"],currency:s.a,getList:function(){var e=this;this.loading=!0,Object(i["i"])(this.queryParams).then((function(t){e.chargeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={chargeId:null,sqdRctId:null,serviceId:null,sqdServiceTypeId:null,sqdRctNo:null,relatedFreightId:null,isRecievingOrPaying:null,clearingCompanyId:null,clearingCompanySummary:null,quotationStrategyId:null,dnChargeNameId:null,dnCurrencyCode:null,dnUnitRate:null,dnUnitCode:null,dnAmount:null,basicCurrencyRate:null,dutyRate:null,subtotal:null,chargeRemark:null,clearingCurrencyCode:null,dnCurrencyReceived:null,dnCurrencyPaid:null,dnCurrencyBalance:null,accountReceivedIdList:null,accountPaidIdList:null,logisticsInvoiceIdList:null,sqdServiceDetailsCode:null,paymentTitleCode:null,logisticsPaymentTermsCode:null,isAccountConfirmed:null,sqdDnCurrencyPaid:null,sqdDnCurrencyBalance:null,sqdWriteoffNoList:null,sqdInvoiceIssued:null,sqdInvoiceBalance:null,currencyRateCalculateDate:null,writeoffStatus:"0"},this.resetForm("form")},handleQuery:function(e){this.queryParams=Object(l["a"])(Object(l["a"])(Object(l["a"])({},this.queryParams),e),{},{pageNum:1}),this.queryParams.pageNum=1,console.log(e),this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+"吗？").then((function(){return Object(i["c"])(e.chargeId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.selectedRows=e,this.aggregatorList=e,this.calculateTotalBalance()},calculateTotalBalance:function(){this.selectedRows&&0!==this.selectedRows.length?this.totalBalance=this.selectedRows.reduce((function(e,t){var a=parseFloat(t.sqdDnCurrencyBalance)||0;return e+a}),0):this.totalBalance=0},handleAdd:function(){this.reset(),this.open=!0,this.title="添加费用明细"},handleUpdate:function(e){var t=this;this.reset();var a=e.chargeId||this.ids;Object(i["g"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改费用明细"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.chargeId?Object(i["l"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.chargeId||this.ids;this.$modal.confirm('是否确认删除费用明细编号为"'+a+'"的数据项？').then((function(){return Object(i["e"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/charge/export",Object(l["a"])({},this.queryParams),"charge_".concat((new Date).getTime(),".xlsx"))}}},y=g,b=(a("38a8"),a("2877")),h=Object(b["a"])(y,n,r,!1,null,"d9e76168",null);t["default"]=h.exports},"1f91":function(e,t,a){},"38a8":function(e,t,a){"use strict";a("1f91")},"4fadc":function(e,t,a){var n=a("23e7"),r=a("6f53").entries;n({target:"Object",stat:!0},{entries:function(e){return r(e)}})},"632e":function(e,t,a){},aafe:function(e,t,a){"use strict";a("632e")},c2aa:function(e,t,a){"use strict";a.d(t,"k",(function(){return r})),a.d(t,"j",(function(){return l})),a.d(t,"l",(function(){return i})),a.d(t,"n",(function(){return o})),a.d(t,"m",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"r",(function(){return u})),a.d(t,"s",(function(){return p})),a.d(t,"d",(function(){return m})),a.d(t,"b",(function(){return f})),a.d(t,"g",(function(){return g})),a.d(t,"f",(function(){return y})),a.d(t,"i",(function(){return b})),a.d(t,"p",(function(){return h})),a.d(t,"q",(function(){return v})),a.d(t,"h",(function(){return k})),a.d(t,"o",(function(){return C}));var n=a("b775");function r(e){return Object(n["a"])({url:"/system/rct/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/system/rct/op",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/system/rct/"+e,method:"get"})}function d(e){return Object(n["a"])({url:"/system/rct",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/system/rct",method:"put",data:e})}function m(e){return Object(n["a"])({url:"/system/rct/"+e,method:"delete"})}function f(e){return Object(n["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function g(){return Object(n["a"])({url:"/system/rct/mon",method:"get"})}function y(){return Object(n["a"])({url:"/system/rct/CFmon",method:"get"})}function b(){return Object(n["a"])({url:"/system/rct/RSWHMon",method:"get"})}function h(e){return Object(n["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function k(e){return Object(n["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function C(e){return Object(n["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},cd43:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"data-aggregator"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:(e.showResult,10)}},[a("el-card",{staticClass:"config-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总配置")])])]},proxy:!0}])},[a("el-form",{staticClass:"edit",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"速查名称",required:""}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"请输入速查名称"},model:{value:e.config.name,callback:function(t){e.$set(e.config,"name",t)},expression:"config.name"}})],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.saveConfig}},[e._v("[↗]")]),a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.loadConfigs}},[e._v("[...]")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组依据"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"操作单号"},model:{value:e.config.primaryField,callback:function(t){e.$set(e.config,"primaryField",t)},expression:"config.primaryField"}},e._l(e.availableFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.matchOptions.exact,callback:function(t){e.$set(e.config.matchOptions,"exact",t)},expression:"config.matchOptions.exact"}},[e._v("精确匹配")]),a("el-checkbox",{model:{value:e.config.matchOptions.caseSensitive,callback:function(t){e.$set(e.config.matchOptions,"caseSensitive",t)},expression:"config.matchOptions.caseSensitive"}},[e._v("区分大小写")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组日期"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"分组日期"},model:{value:e.config.dateField,callback:function(t){e.$set(e.config,"dateField",t)},expression:"config.dateField"}},e._l(e.dateFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.dateOptions.convertToNumber,callback:function(t){e.$set(e.config.dateOptions,"convertToNumber",t)},expression:"config.dateOptions.convertToNumber"}},[e._v("转换为数字")]),a("el-radio-group",{staticStyle:{display:"flex","line-height":"26px"},model:{value:e.config.dateOptions.formatType,callback:function(t){e.$set(e.config.dateOptions,"formatType",t)},expression:"config.dateOptions.formatType"}},[a("el-radio",{attrs:{label:"year"}},[e._v("按年")]),a("el-radio",{attrs:{label:"month"}},[e._v("按月")]),a("el-radio",{attrs:{label:"day"}},[e._v("按天")])],1)],1)],1)],1),a("el-form-item",{attrs:{label:"显示方式"}},[a("el-checkbox",{staticStyle:{"padding-left":"5px"},model:{value:e.config.showDetails,callback:function(t){e.$set(e.config,"showDetails",t)},expression:"config.showDetails"}},[e._v("含明细")])],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.config.fields,border:""}},[a("el-table-column",{attrs:{align:"center",label:"序号",type:"index",width:"60"}}),a("el-table-column",{attrs:{label:"表头名称","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},on:{change:function(a){return e.handleFieldSelect(t.$index)}},model:{value:t.row.fieldKey,callback:function(a){e.$set(t.row,"fieldKey",a)},expression:"scope.row.fieldKey"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)]}}])}),a("el-table-column",{attrs:{label:"排序",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.sort,callback:function(a){e.$set(t.row,"sort",a)},expression:"scope.row.sort"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"∧",value:"asc"}}),a("el-option",{attrs:{label:"∨ ",value:"desc"}})],1)]}}])}),a("el-table-column",{attrs:{label:"汇总方式",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.isAggregatable(t.row.fieldKey)},model:{value:t.row.aggregation,callback:function(a){e.$set(t.row,"aggregation",a)},expression:"scope.row.aggregation"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"计数",value:"count"}}),a("el-option",{attrs:{label:"求和",value:"sum"}}),a("el-option",{attrs:{label:"平均值",value:"avg"}}),a("el-option",{attrs:{label:"最大值",value:"max"}}),a("el-option",{attrs:{label:"最小值",value:"min"}}),a("el-option",{attrs:{label:"方差",value:"variance"}})],1)]}}])}),a("el-table-column",{attrs:{label:"显示格式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.format,callback:function(a){e.$set(t.row,"format",a)},expression:"scope.row.format"}},[a("el-option",{attrs:{label:"-",value:"none"}}),"date"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"YYYYMM",value:"YYYYMM"}}),a("el-option",{attrs:{label:"MM-DD",value:"MM-DD"}}),a("el-option",{attrs:{label:"YYYY-MM-DD",value:"YYYY-MM-DD"}})]:e._e(),"number"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"0.00",value:"decimal"}}),a("el-option",{attrs:{label:"0.00%",value:"percent"}}),a("el-option",{attrs:{label:"¥0.00",value:"currency"}}),a("el-option",{attrs:{label:"$0.00",value:"usd"}}),a("el-option",{attrs:{label:"0不显示",value:"hideZero"}})]:e._e()],2)]}}])}),a("el-table-column",{attrs:{label:"零值隐藏",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{disabled:"number"!==e.getFieldDisplay(t.row.fieldKey)},model:{value:t.row.hideZeroValues,callback:function(a){e.$set(t.row,"hideZeroValues",a)},expression:"scope.row.hideZeroValues"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[a("el-button",{attrs:{disabled:0===t.$index,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"up")}}},[e._v("[∧] ")]),a("el-button",{attrs:{disabled:t.$index===e.config.fields.length-1,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"down")}}},[e._v("[∨] ")]),a("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",size:"mini",type:"text"},on:{click:function(a){return e.removeField(t.$index)}}})],1)]}}])})],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("el-button",{attrs:{plain:"",type:"text"},on:{click:e.addField}},[e._v("[ + ]")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleServerAggregate}},[e._v("分类汇总")]),a("el-button",{on:{click:e.resetConfig}},[e._v("重置")])],1)],1)],1)],1),e.showResult?a("el-col",{attrs:{span:14}},[a("el-card",{staticClass:"result-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总结果")]),a("div",{staticClass:"operations"},[a("el-switch",{staticStyle:{"margin-right":"15px"},attrs:{"active-text":"横向","inactive-text":"纵向"},model:{value:e.isLandscape,callback:function(t){e.isLandscape=t},expression:"isLandscape"}}),a("el-button",{attrs:{size:"small"},on:{click:e.printTable}},[e._v("打印")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.exportToPDF}},[e._v("导出PDF")])],1)])]},proxy:!0}],null,!1,1080603383)},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"resultTable",staticStyle:{width:"100%"},attrs:{data:e.processedData,"summary-method":e.getSummary,border:"","show-summary":""}},[a("el-table-column",{attrs:{align:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].align:"left",label:e.groupFieldName,width:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].width:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatGroupKey(t.row.groupKey))+" ")]}}],null,!1,2877943199)}),e._l(e.config.fields,(function(t,n){return[t.fieldKey?a("el-table-column",{key:t.fieldKey+"_"+n,attrs:{align:e.getColumnAlign(t.fieldKey),label:e.getResultLabel(t),width:e.getColumnWidth(t.fieldKey)},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.formatCellValue(a.row[e.getResultProp(t)],t))+" ")]}}],null,!0)}):e._e()]}))],2)],1)],1):e._e()],1),a("el-dialog",{attrs:{visible:e.configDialogVisible,"append-to-body":"",title:"加载配置",width:"550px"},on:{"update:visible":function(t){e.configDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.configLoading,expression:"configLoading"}],staticStyle:{width:"100%"},attrs:{data:e.savedConfigs},on:{"row-click":e.handleConfigSelect}},[a("el-table-column",{attrs:{label:"配置名称",prop:"name"}}),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime"}}),a("el-table-column",{attrs:{width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),e.deleteConfig(t.row)}}},[e._v("删除")])]}}])})],1)],1),a("el-dialog",{attrs:{visible:e.filterDialogVisible,title:"设置筛选条件",width:"650px"},on:{"update:visible":function(t){e.filterDialogVisible=t}}},[a("el-form",{attrs:{model:e.currentFilter,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"字段"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},model:{value:e.currentFilter.field,callback:function(t){e.$set(e.currentFilter,"field",t)},expression:"currentFilter.field"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)],1),a("el-form-item",{attrs:{label:"操作符"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择操作符"},model:{value:e.currentFilter.operator,callback:function(t){e.$set(e.currentFilter,"operator",t)},expression:"currentFilter.operator"}},[a("el-option",{attrs:{label:"等于",value:"eq"}}),a("el-option",{attrs:{label:"不等于",value:"ne"}}),a("el-option",{attrs:{label:"大于",value:"gt"}}),a("el-option",{attrs:{label:"大于等于",value:"ge"}}),a("el-option",{attrs:{label:"小于",value:"lt"}}),a("el-option",{attrs:{label:"小于等于",value:"le"}}),a("el-option",{attrs:{label:"包含",value:"contains"}}),a("el-option",{attrs:{label:"开始于",value:"startsWith"}}),a("el-option",{attrs:{label:"结束于",value:"endsWith"}})],1)],1),a("el-form-item",{attrs:{label:"值"}},[a("el-input",{attrs:{placeholder:"输入筛选值"},model:{value:e.currentFilter.value,callback:function(t){e.$set(e.currentFilter,"value",t)},expression:"currentFilter.value"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.addFilter}},[e._v("添加筛选条件")])],1)],1),e.config.filters&&e.config.filters.length?a("div",[a("h4",[e._v("已添加的筛选条件")]),a("el-table",{attrs:{data:e.config.filters,border:""}},[a("el-table-column",{attrs:{label:"字段",prop:"field"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getFieldLabel(t.row.field))+" ")]}}],null,!1,3496384076)}),a("el-table-column",{attrs:{label:"操作符",prop:"operator"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getOperatorLabel(t.row.operator))+" ")]}}],null,!1,1753700364)}),a("el-table-column",{attrs:{label:"值",prop:"value"}}),a("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{circle:"",icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.removeFilter(t.$index)}}})]}}],null,!1,3366023889)})],1)],1):e._e(),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.filterDialogVisible=!1}}},[e._v("关闭")])],1)],1)],1)},r=[],l=a("53ca"),i=a("b85c"),o=a("c7eb"),s=a("1da1"),c=a("2909"),d=a("5530"),u=(a("b64b"),a("4de4"),a("d3b7"),a("99af"),a("b0c0"),a("7db0"),a("14d9"),a("a434"),a("4e82"),a("d81d"),a("a9e3"),a("b680"),a("159b"),a("13d5"),a("ac1f"),a("5319"),a("caad"),a("2532"),a("c1df")),p=a.n(u),m=(a("72f9"),a("c211")),f=a("d67e"),g=a.n(f),y={name:"DataAggregatorBackGround",props:{fieldLabelMap:{type:Object,required:!0,default:function(){return{}}},dataSourceType:{type:String,required:!0,default:"rct"},aggregateFunction:{type:Function,required:!0},configType:{type:String,required:!1}},data:function(){return{configName:"",config:{name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},dateOptions:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按周",value:"week"},{label:"按日",value:"day"},{label:"按时",value:"hour"},{label:"按分",value:"minute"}],aggregationOptions:[{label:"计数",value:"count"},{label:"求和",value:"sum"},{label:"平均值",value:"avg"},{label:"方差",value:"variance"},{label:"最大值",value:"max"},{label:"最小值",value:"min"}],operatorOptions:[{label:"等于",value:"eq"},{label:"不等于",value:"ne"},{label:"大于",value:"gt"},{label:"大于等于",value:"ge"},{label:"小于",value:"lt"},{label:"小于等于",value:"le"},{label:"包含",value:"contains"},{label:"开始于",value:"startsWith"},{label:"结束于",value:"endsWith"}],loading:!1,configDialogVisible:!1,filterDialogVisible:!1,savedConfigs:[],configLoading:!1,isLandscape:!1,showResult:!1,processedData:[],currentFilter:{field:"",operator:"eq",value:""}}},computed:{availableFields:function(){return Object.keys(this.fieldLabelMap)},dateFields:function(){var e=this;return this.availableFields.filter((function(t){return e.fieldLabelMap[t]&&"date"===e.fieldLabelMap[t].display}))},groupFieldName:function(){return this.config.primaryField&&this.config.dateField?"".concat(this.getFieldLabel(this.config.dateField),"+").concat(this.getFieldLabel(this.config.primaryField)):this.config.primaryField?this.getFieldLabel(this.config.primaryField):this.config.dateField?this.getFieldLabel(this.config.dateField):"分组"}},methods:{getFieldLabel:function(e){var t;return(null===(t=this.fieldLabelMap[e])||void 0===t?void 0:t.name)||e},getOperatorLabel:function(e){var t=this.operatorOptions.find((function(t){return t.value===e}));return t?t.label:e},openFilterDialog:function(){this.currentFilter={field:"",operator:"eq",value:""},this.filterDialogVisible=!0},addFilter:function(){this.currentFilter.field?this.currentFilter.value||0===this.currentFilter.value?(this.config.filters||this.$set(this.config,"filters",[]),this.config.filters.push(Object(d["a"])({},this.currentFilter)),this.currentFilter={field:"",operator:"eq",value:""}):this.$message.warning("请输入筛选值"):this.$message.warning("请选择筛选字段")},removeFilter:function(e){this.config.filters.splice(e,1)},getFieldDisplay:function(e){var t=this.fieldLabelMap[e];return t?t.display&&"function"===typeof this[t.display]?"custom":t.display||"text":"text"},isAggregatable:function(e){var t=this.fieldLabelMap[e];return(null===t||void 0===t?void 0:t.aggregated)||!1},addField:function(){this.config.fields.push({fieldKey:"",aggregation:"none",format:"none",sort:"none",hideZeroValues:!1})},removeField:function(e){this.config.fields.splice(e,1)},moveField:function(e,t){var a=Object(c["a"])(this.config.fields);if("up"===t&&e>0){var n=[a[e-1],a[e]];a[e]=n[0],a[e-1]=n[1]}else if("down"===t&&e<a.length-1){var r=[a[e+1],a[e]];a[e]=r[0],a[e+1]=r[1]}this.$set(this.config,"fields",a)},handleFieldSelect:function(e){var t=this.config.fields[e],a=this.fieldLabelMap[t.fieldKey];a&&(t.format=this.getDefaultFormat(a.display),t.aggregation=a.aggregated?"sum":"none",t.sort="none")},getDefaultFormat:function(e){switch(e){case"date":return"YYYY-MM-DD";case"number":return"decimal";default:return"none"}},resetConfig:function(){this.config={name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},this.showResult=!1},getDateFormat:function(){switch(this.config.dateOptions.formatType){case"year":return"YYYY";case"month":return"YYYY-MM";case"day":default:return"YYYY-MM-DD"}},handleServerAggregate:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n,r;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.config.primaryField||e.config.dateField){t.next=3;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 3:if(e.config.fields.length){t.next=6;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 6:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=10;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 10:if(e.aggregateFunction){t.next=13;break}return e.$message.error("汇总函数未定义"),t.abrupt("return");case 13:return t.prev=13,e.loading=!0,n={dataSourceType:e.dataSourceType,config:{primaryField:e.config.primaryField,matchOptions:e.config.matchOptions,dateField:e.config.dateField,dateOptions:e.config.dateOptions,showDetails:e.config.showDetails,fields:e.config.fields,filters:e.config.filters||[]}},t.next=18,e.aggregateFunction(n);case 18:r=t.sent,200===r.code?(e.processedData=e.filterZeroValueRecords(r.data),e.showResult=!0):e.$message.error(r.msg||"汇总数据失败"),t.next=26;break;case 22:t.prev=22,t.t0=t["catch"](13),console.error("数据汇总失败:",t.t0),e.$message.error("汇总处理失败："+(t.t0.message||"未知错误"));case 26:return t.prev=26,e.loading=!1,t.finish(26);case 29:case"end":return t.stop()}}),t,null,[[13,22,26,29]])})))()},filterZeroValueRecords:function(e){var t=this,a=this.config.fields.filter((function(e){return!0===e.hideZeroValues})).map((function(e){return{key:e.fieldKey,aggProp:t.getResultProp(e)}}));return 0===a.length?e:e.filter((function(e){var t,n=Object(i["a"])(a);try{for(n.s();!(t=n.n()).done;){var r=t.value,l=e[r.aggProp];if(0===l||"0"===l||"0.00"===l)return!1}}catch(o){n.e(o)}finally{n.f()}return!0}))},getResultProp:function(e){return e.aggregation&&"none"!==e.aggregation?"".concat(e.fieldKey,"_").concat(e.aggregation):e.fieldKey},getResultLabel:function(e){var t=this.getFieldLabel(e.fieldKey);if(e.aggregation&&"none"!==e.aggregation){var a,n=(null===(a=this.aggregationOptions.find((function(t){return t.value===e.aggregation})))||void 0===a?void 0:a.label)||e.aggregation;return"".concat(t,"(").concat(n,")")}return t},formatCellValue:function(e,t){if(null==e)return"-";var a=this.fieldLabelMap[t.fieldKey];if(!a)return e;if(a.display&&"function"===typeof this[a.display])return this[a.display](e);switch(a.display){case"number":var n=Number(e);if(isNaN(n))return"-";switch(t.format){case"decimal":return n.toFixed(2);case"percent":return(100*n).toFixed(2)+"%";case"currency":return"¥"+n.toFixed(2);case"usd":return"$"+n.toFixed(2);case"hideZero":return 0===n?"-":n.toFixed(2);default:return n.toFixed(2)}case"date":return p()(e).format(t.format||"YYYY-MM-DD");case"boolean":return"avg"===t.aggregation?(100*Number(e)).toFixed(2)+"%":e?"是":"否";default:return e}},formatGroupKey:function(e){if("object"===Object(l["a"])(e)&&null!==e&&void 0!==e.primary&&void 0!==e.date){var t=this.fieldLabelMap[this.config.primaryField],a=e.primary;return t&&t.display&&"function"===typeof this[t.display]&&(a=this[t.display](a)),"".concat(e.date," ").concat(a)}if(this.config.primaryField){var n=this.fieldLabelMap[this.config.primaryField];if(n&&n.display&&"function"===typeof this[n.display])return this[n.display](e)}return String(e||"")},getSummary:function(e){var t=this,a=e.columns,n=e.data,r=[];return a.forEach((function(e,a){if(0!==a){var l=a-1,i=t.config.fields[l];if(i&&i.fieldKey)if(i.aggregation&&"none"!==i.aggregation){var o=t.fieldLabelMap[i.fieldKey];if(o)if("number"===o.display||"percentage"===o.display||"function"===typeof t[o.display]){var s=n.map((function(e){var a=t.getResultProp(i),n=Number(e[a]);return isNaN(n)?0:n})).filter((function(e){return!isNaN(e)}));if(0!==s.length){var d=0;switch(i.aggregation){case"sum":d=s.reduce((function(e,t){return e+t}),0);break;case"avg":d=s.reduce((function(e,t){return e+t}),0)/s.length;break;case"max":d=Math.max.apply(Math,Object(c["a"])(s));break;case"min":d=Math.min.apply(Math,Object(c["a"])(s));break;case"variance":var u=s.reduce((function(e,t){return e+t}),0)/s.length;d=s.reduce((function(e,t){return e+Math.pow(t-u,2)}),0)/s.length;break;default:d=s.reduce((function(e,t){return e+t}),0)}"percentage"===o.display||"percent"===o.display?r[a]=t.percentage(d):"decimal"===i.format?r[a]=d.toFixed(2):"percent"===i.format?r[a]=(100*d).toFixed(2)+"%":"currency"===i.format?r[a]="¥"+d.toFixed(2):"usd"===i.format?r[a]="$"+d.toFixed(2):r[a]=d.toFixed(2)}else r[a]=""}else r[a]="";else r[a]=""}else r[a]="";else r[a]=""}else r[a]="合计"})),r},saveConfig:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.config.name){t.next=4;break}return e.$message.warning("请输入速查名称"),t.abrupt("return");case 4:if(e.config.primaryField||e.config.dateField){t.next=7;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 7:if(e.config.fields.length){t.next=10;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 10:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=14;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 14:return n={name:e.config.name,type:e.configType,config:e.config},t.next=17,Object(m["c"])(n);case 17:e.$message.success("配置保存成功"),t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](0),"cancel"!==t.t0&&e.$message.error("保存配置失败："+(t.t0.message||"未知错误"));case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},loadConfigs:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.configLoading=!0,e.configDialogVisible=!0,t.prev=2,t.next=5,Object(m["b"])({configType:e.configType});case 5:a=t.sent,e.savedConfigs=a.rows,t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("加载配置失败:",t.t0),e.$message.error((null===(n=t.t0.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||t.t0.message||"加载配置列表失败，请稍后重试");case 13:return t.prev=13,e.configLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,9,13,16]])})))()},handleConfigSelect:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function a(){var n;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{n=JSON.parse(e.config),n.name=e.name,t.config=n,t.configDialogVisible=!1,t.$message.success("配置加载成功")}catch(r){console.error("加载配置失败:",r),t.$message.error("加载配置失败："+r.message)}case 1:case"end":return a.stop()}}),a)})))()},deleteConfig:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该配置？","提示",{type:"warning"});case 3:return a.next=5,Object(m["a"])(e.id);case 5:t.savedConfigs=t.savedConfigs.filter((function(t){return t.id!==e.id})),t.$message.success("配置删除成功"),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),"cancel"!==a.t0&&t.$message.error("删除配置失败："+a.t0.message);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},printTable:function(){var e=window.open("","_blank"),t=this.$refs.resultTable.$el.cloneNode(!0),a="汇总数据",n=((new Date).toLocaleDateString(),'\n        <div class="company-header">\n          <div class="company-logo">\n            <img src="/logo.png" alt="Rich Shipping Logo" />\n            <div class="company-name">\n              <div class="company-name-cn">广州瑞旗国际货运代理有限公司</div>\n              <div class="company-name-en">GUANGZHOU RICH SHIPPING INT\'L CO.,LTD.</div>\n            </div>\n          </div>\n          <div class="document-title">\n            <div class="title-cn"></div>\n            <div class="title-en"></div>\n          </div>\n        </div>\n      ');e.document.write('\n        <html lang="">\n          <head>\n            <title>'.concat(a,"</title>\n            <style>\n              /* 基础样式 */\n              body {\n                margin: 0;\n                padding: 0;\n                font-family: Arial, sans-serif;\n              }\n\n              /* 打印样式 - 必须放在这里才能生效 */\n              @media print {\n                @page {\n                  size: ").concat(this.isLandscape?"landscape":"portrait",';\n                  margin: 1.5cm 1cm 1cm 1cm;\n                }\n\n                /* 重要：使用重复表头技术 */\n                thead {\n                  display: table-header-group;\n                }\n\n                /* 页眉作为表格的一部分，放在thead中 */\n                .page-header {\n                  display: table-header-group;\n                }\n\n                /* 内容部分 */\n                .page-content {\n                  display: table-row-group;\n                }\n\n                /* 避免元素内部分页 */\n                .company-header, .header-content {\n                  page-break-inside: avoid;\n                }\n\n                /* 表格样式 */\n                table.main-table {\n                  width: 100%;\n                  border-collapse: collapse;\n                  border: none;\n                }\n\n                /* 确保表头在每页都显示 */\n                table.data-table thead {\n                  display: table-header-group;\n                }\n\n                /* 避免行内分页 */\n                table.data-table tr {\n                  page-break-inside: avoid;\n                }\n              }\n\n              /* 表格样式 */\n              table.data-table {\n                border-collapse: collapse;\n                width: 100%;\n                margin-top: 20px;\n                table-layout: fixed;\n              }\n\n              table.data-table th, table.data-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              table.data-table th {\n                background-color: #f2f2f2;\n              }\n\n              /* Element UI 表格样式模拟 */\n              .el-table {\n                border-collapse: collapse;\n                width: 100%;\n                table-layout: fixed;\n              }\n\n              .el-table th, .el-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              .el-table th {\n                background-color: #f2f2f2;\n                font-weight: bold;\n              }\n\n              .el-table__footer {\n                background-color: #f8f8f9;\n                font-weight: bold;\n              }\n\n              .el-table__footer td {\n                border: 1px solid #ddd;\n                padding: 8px;\n              }\n\n              /* 公司标题和标志样式 */\n              .company-header {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                border-bottom: 2px solid #000;\n                padding-bottom: 10px;\n                width: 100%;\n              }\n\n              .company-logo {\n                display: flex;\n                align-items: center;\n              }\n\n              .company-logo img {\n                height: 50px;\n                margin-right: 10px;\n              }\n\n              .company-name {\n                display: flex;\n                flex-direction: column;\n              }\n\n              .company-name-cn {\n                font-size: 18px;\n                font-weight: bold;\n                color: #ff0000;\n              }\n\n              .company-name-en {\n                font-size: 14px;\n              }\n\n              .document-title {\n                text-align: right;\n              }\n\n              .title-cn {\n                font-size: 18px;\n                font-weight: bold;\n              }\n\n              .title-en {\n                font-size: 16px;\n                font-weight: bold;\n              }\n\n              /* 清除表格边框 */\n              table.main-table, table.main-table td {\n                border: none;\n              }\n\n              /* 页眉容器 */\n              .header-container {\n                width: 100%;\n                margin-bottom: 20px;\n              }\n            </style>\n          </head>\n          <body>\n            \x3c!-- 使用表格布局确保页眉在每页重复 --\x3e\n            <table class="main-table">\n              <thead class="page-header">\n                <tr>\n                  <td>\n                    <div class="header-container">\n                      ').concat(n,'\n                    </div>\n                  </td>\n                </tr>\n              </thead>\n              <tbody class="page-content">\n                <tr>\n                  <td>\n                    \x3c!-- 保留原始表格的类名并添加data-table类 --\x3e\n                    ').concat(t.outerHTML.replace("<table",'<table class="el-table data-table"'),"\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </body>\n        </html>\n      ")),e.document.close(),setTimeout((function(){try{e.focus(),e.print()}catch(t){console.error("打印过程中发生错误:",t)}}),1e3)},exportToPDF:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,a=e.$refs.resultTable.$el,n={margin:[.8,.8,.8,.8],filename:"汇总数据.pdf",image:{type:"jpeg",quality:.98},html2canvas:{scale:2},jsPDF:{unit:"in",format:"a3",orientation:e.isLandscape?"landscape":"portrait"},pagebreak:{mode:["avoid-all","css","legacy"]},header:[{text:"汇总数据",style:"headerStyle"},{text:(new Date).toLocaleDateString(),style:"headerStyle",alignment:"right"}],footer:{height:"20px",contents:{default:'<span style="float:right">{{page}}/{{pages}}</span>'}}},t.next=6,g()().set(n).from(a).save();case 6:e.$message.success("PDF导出成功"),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.$message.error("PDF导出失败："+t.t0.message);case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,9,12,15]])})))()},getName:function(e){if(null!==e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t&&void 0!==t)return t.staffShortName+t.staffFamilyEnName}return""},percentage:function(e){if(null==e||""===e)return"-";if("string"===typeof e&&e.includes("%"))return e;var t=Number(e);if(isNaN(t))return"-";var a=t>0&&t<=1,n=a?100*t:t;return n.toFixed(2)+"%"},getColumnAlign:function(e){var t=this.fieldLabelMap[e];return t&&t.align?t.align:"left"},getColumnWidth:function(e){var t=this.fieldLabelMap[e];return t&&t.width?t.width:""}}},b=y,h=(a("aafe"),a("2877")),v=Object(h["a"])(b,n,r,!1,null,"f80d2af4",null);t["default"]=v.exports},fff5:function(e,t,a){"use strict";a.d(t,"i",(function(){return r})),a.d(t,"b",(function(){return l})),a.d(t,"g",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"l",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"h",(function(){return u})),a.d(t,"j",(function(){return p})),a.d(t,"f",(function(){return m})),a.d(t,"d",(function(){return f})),a.d(t,"m",(function(){return g})),a.d(t,"k",(function(){return y}));var n=a("b775");function r(e){return Object(n["a"])({url:"/system/rscharge/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/rscharge/aggregator",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/system/rscharge/"+e,method:"get"})}function o(e){return Object(n["a"])({url:"/system/rscharge",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/system/rscharge",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/rscharge/"+e,method:"delete"})}function d(e,t){var a={chargeId:e,status:t};return Object(n["a"])({url:"/system/rscharge/changeStatus",method:"put",data:a})}function u(e){return Object(n["a"])({url:"/system/rscharge/charges",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/system/rscharge/selectList",method:"get",params:e})}function m(e){return Object(n["a"])({url:"/system/rscharge/findHedging",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/system/rscharge/writeoff",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/system/rscharge/verify",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/system/rscharge/turnback",method:"post",data:e})}}}]);