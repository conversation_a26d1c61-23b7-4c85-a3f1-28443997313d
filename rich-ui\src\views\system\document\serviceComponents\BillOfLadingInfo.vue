<template>
  <div>
    <!--title-->
    <el-row>
      <el-col :span="18">
        <div class="service-bar" style="display: flex;margin-top: 10px;margin-bottom: 10px;width: 100%">
          <a :class="{'el-icon-arrow-down':visible,'el-icon-arrow-right':!visible}"/>
          <div style="width:150px;display: flex">
            <h3 style="margin: 0;width: 250px;text-align: left" @click="toggleVisible">提单信息</h3>
            <el-button style="margin-left: 10px;" type="text" @click="$emit('openChargeSelect', rsClientMessage)">
              [DN...]
            </el-button>
          </div>

          <el-button type="primary" size="mini" style="margin-left: 10px;" @click="profitOpen=true">利润</el-button>

          <el-col v-if="auditInfo"
                  :span="15" style="display: flex"
          >
            <div v-hasPermi="['system:booking:opapproval','system:rct:opapproval']"
                 style="width:25%;display: flex;font-size: 12px"
            >
              <el-button :icon="rsClientServiceInstance.isDnOpConfirmed?'el-icon-check':'el-icon-minus'"
                         style="padding: 0" type="text"
                         @click="$emit('confirmed', 'op')"
              >操作确认
              </el-button>
              <div style="text-align: left;width: 120px">
                <div><i class="el-icon-user"/>{{ opConfirmedName }}</div>
                <div><i class="el-icon-alarm-clock"/>{{ opConfirmedDate }}</div>
              </div>
            </div>
            <div v-hasPermi="['system:booking:salesapproval','system:rct:salesapproval']"
                 style="width:25%;display: flex;font-size: 12px"
            >
              <el-button :icon="rsClientServiceInstance.isDnSalesConfirmed?'el-icon-check':'el-icon-minus'"
                         style="padding: 0" type="text"
                         @click="$emit('confirmed', 'sales')"
              >业务确认
              </el-button>
              <div style="text-align: left;width: 120px">
                <div><i class="el-icon-user"/>{{ salesConfirmedName }}</div>
                <div><i class="el-icon-alarm-clock"/>{{ salesConfirmedDate }}</div>
              </div>
            </div>
            <div v-hasPermi="['system:booking:clientapproval','system:rct:clientapproval']"
                 style="width:25%;display: flex;font-size: 12px"
            >
              <el-button :icon="rsClientServiceInstance.isDnClientConfirmed?'el-icon-check':'el-icon-minus'"
                         style="padding: 0" type="text"
                         @click="$emit('confirmed', 'client')"
              >客户确认
              </el-button>
              <div style="text-align: left;width: 120px">
                <div><i class="el-icon-user"/>{{ clientConfirmedName }}</div>
                <div><i class="el-icon-alarm-clock"/>{{ clientConfirmedDate }}</div>
              </div>
            </div>
            <div v-hasPermi="['system:booking:financeapproval','system:rct:financeapproval']"
                 style="width:25%;display: flex;font-size: 12px"
            >
              <el-button :icon="rsClientServiceInstance.isAccountConfirmed?'el-icon-check':'el-icon-minus'"
                         style="padding: 0" type="text"
                         @click="$emit('confirmed', 'account', rsClientMessage.rsChargeList)"
              >财务确认
              </el-button>
              <div style="text-align: left;width: 120px">
                <div><i class="el-icon-user"/>{{ accountConfirmedName }}</div>
                <div><i class="el-icon-alarm-clock"/>{{ accountConfirmedDate }}</div>
              </div>
            </div>
          </el-col>

          <div style="margin-left: auto">
            <el-popover
              v-for="(item,index) in fileOptions" :key="index"
              placement="top" trigger="click" width="100"
            >
              <el-button v-for="(item2,index) in item.templateList" :key="index"
                         @click="handleFileAction(item.link, item2)"
              >{{ item2 }}
              </el-button>
              <a slot="reference" style="color: blue;padding: 0;margin-left: 10px" target="_blank"
              >[{{ item.file }}]</a>
            </el-popover>
          </div>
        </div>
      </el-col>
    </el-row>

    <!--content-->
    <transition name="fade">
      <el-row v-if="visible" :gutter="10" style="margin-bottom:15px;display:-webkit-box">
        <!--主表信息-->
        <transition name="fade">
          <el-col v-if="branchInfo" :span="18">
            <el-table :data="bookingMessageList" border @selection-change="handleSelectionChange">
              <el-table-column
                type="selection"
                width="55"
              >
              </el-table-column>
              <el-table-column
                label="序号"
                type="index"
                width="50"
              />
              <el-table-column label="MB/L No" prop="mBlNo"/>
              <el-table-column label="HB/L No" prop="hBlNo"/>
              <el-table-column label="发货人" prop="bookingShipper"/>
              <el-table-column label="收货人" prop="bookingConsignee"/>
              <el-table-column label="通知人" prop="bookingNotifyParty"/>
              <el-table-column label="代理" prop="bookingAgent"/>
              <el-table-column label="柜号" prop="containerNo"/>
              <el-table-column label="封号" prop="sealNo"/>
              <el-table-column label="柜型" prop="containerType"/>
              <el-table-column label="唛头" prop="shippingMark"/>
              <el-table-column label="件数" prop="packageQuantity"/>
              <el-table-column label="货描" prop="goodsDescription"/>
              <el-table-column label="体积" prop="goodsVolume"/>
              <el-table-column label="重量" prop="grossWeight"/>
              <el-table-column label="提单类型" prop="blTypeCode"/>
              <el-table-column label="出单方式" prop="blFormCode"/>
              <el-table-column label="交单方式" prop="sqdDocDeliveryWay">
                <template slot-scope="scope">
                  <tree-select :class="'disable-form'" :disabled="true"
                               :flat="false"
                               :multiple="false" :pass="scope.row.sqdDocDeliveryWay"
                               :placeholder="'货代单交单方式'"
                               :type="'docReleaseWay'" @return="scope.row.sqdDocDeliveryWay=$event"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="sqdDocDeliveryWay">
                <template slot-scope="scope">
                  <el-button type="text" @click="handleBookingMessageUpdate(scope.row)">修改</el-button>
                  <el-button style="color: red" type="text"
                             @click="deleteBookingMessage(scope.row)"
                  >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!--弹出层-->
            <el-dialog
              v-dialogDrag
              v-dialogDragWidth
              :close-on-click-modal="false" :modal-append-to-body="false"
              :show-close="false" :title="bookingMessageTitle" @close="closeBookingMessage"
              :visible.sync="openBookingMessage" append-to-body width="30%"
            >
              <el-form ref="bookingMessageForm" :model="bookingMessageForm" class="edit" label-width="80px"
                       style=""
              >
                <div v-if="bookingMessageForm.blTypeCode==='MBL'">
                  <el-form-item label="提单号码">
                    <el-input v-model="bookingMessageForm.mBlNo" style="padding: 0;margin: 0;"/>
                  </el-form-item>
                </div>
                <div v-else>
                  <el-form-item label="MB/L No">
                    <el-input v-model="bookingMessageForm.mBlNo" style="padding: 0;margin: 0;"/>
                  </el-form-item>
                  <el-form-item label="HB/L No">
                    <el-input v-model="bookingMessageForm.hBlNo" style="padding: 0;margin: 0;"/>
                  </el-form-item>
                </div>
                <el-form-item label="发货人">
                  <template slot="label">
                    <div>发货人</div>
                    <el-button style="color: blue" type="text" @click="$emit('handleAddCommon', 'release')">[↗]
                    </el-button>
                    <el-button style="color: blue" type="text" @click="$emit('openReleaseUsed')">[...]</el-button>
                  </template>
                  <el-input v-model="bookingMessageForm.bookingShipper" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="收货人">
                  <el-input v-model="bookingMessageForm.bookingConsignee" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="通知人">
                  <el-input v-model="bookingMessageForm.bookingNotifyParty" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="代理">
                  <el-input v-model="bookingMessageForm.bookingAgent" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="启运港">
                  <el-input v-model="bookingMessageForm.polName"/>
                </el-form-item>
                <el-form-item label="卸货港">
                  <el-input v-model="bookingMessageForm.podName"/>
                </el-form-item>
                <el-form-item label="目的港">
                  <el-input v-model="bookingMessageForm.destinationPort"/>
                </el-form-item>
                <el-form-item label="柜号">
                  <el-input v-model="bookingMessageForm.containerNo" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="封号">
                  <el-input v-model="bookingMessageForm.sealNo" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="柜型">
                  <el-input v-model="bookingMessageForm.containerType" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="唛头">
                  <el-input v-model="bookingMessageForm.shippingMark" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="件数">
                  <el-input v-model="bookingMessageForm.packageQuantity" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500"
                            placeholder="件数" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="货描">
                  <el-input v-model="bookingMessageForm.goodsDescription" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="重量">
                  <el-input v-model="bookingMessageForm.grossWeight" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500"
                            placeholder="重量" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="体积">
                  <el-input v-model="bookingMessageForm.goodsVolume" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500"
                            placeholder="体积" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="备注">
                  <el-input v-model="bookingMessageForm.blRemark" :autosize="{ minRows: 2.5, maxRows: 5}"
                            maxlength="500" show-word-limit style="padding: 0;margin: 0;" type="textarea"
                  />
                </el-form-item>
                <el-form-item label="出单地">
                  <location-select :load-options="psaBookingSelectData.locationOptions" :no-parent="true"
                                   :pass="bookingMessageForm.polIds" :placeholder="'启运港'"
                                   @returnData="bookingMessageForm.city=$event.locationEnShortName"
                  />
                </el-form-item>
                <el-form-item label="开船日期">
                  <el-date-picker
                    v-model="bookingMessageForm.onBoardDate"
                    placeholder="选择日期"
                    type="date"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="付款方式">
                  <el-select v-model="bookingMessageForm.payWay" placeholder="请选择">
                    <el-option
                      v-for="item in payWayOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="提单类型">
                  <tree-select :flat="false"
                               :multiple="false" :pass="bookingMessageForm.blTypeCode"
                               :placeholder="'提单类型'" :type="'blType'"
                               @return="bookingMessageForm.blTypeCode=$event"
                  />
                </el-form-item>
                <el-form-item label="出单方式">
                  <tree-select :flat="false"
                               :multiple="false" :pass="bookingMessageForm.blFormCode"
                               :placeholder="'出单方式'" :type="'blForm'"
                               @return="bookingMessageForm.blFormCode=$event"
                  />
                </el-form-item>
                <el-form-item label="交单方式">
                  <tree-select :flat="false"
                               :multiple="false" :pass="bookingMessageForm.sqdDocDeliveryWay"
                               :placeholder="'货代单交单方式'"
                               :type="'docReleaseWay'" @return="bookingMessageForm.sqdDocDeliveryWay=$event"
                  />
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button size="mini" type="primary" @click="bookingMessageConfirm">确 定</el-button>
                <el-button size="mini" @click="closeBookingMessage">取 消</el-button>
              </div>
            </el-dialog>

            <el-button :disabled="psaVerify || disabled"
                       style="padding: 0"
                       type="text"
                       @click="addBookingMessage"
            >[＋]
            </el-button>
          </el-col>
        </transition>

        <!--物流进度-->
        <transition name="fade">
          <el-col v-if="logisticsInfo" :span="4">
            <el-form-item label="进度需求" prop="goodsNameSummary"/>
            <div>
              <logistics-progress :disabled="rsClientMessageFormDisable || disabled || psaVerify"
                                  :logistics-progress-data="rsClientMessage.rsOpLogList"
                                  :open-logistics-progress-list="true"
                                  @deleteItem="deleteLogisticsItem"
                                  @return="updateLogisticsProgress"
              />
            </div>
          </el-col>
        </transition>

        <!--费用列表-->
        <transition name="fade">
          <el-col v-if="chargeInfo" :span="10.5">
            <charge-list :a-t-d="form.podEta" :charge-data="rsClientMessage.rsChargeList"
                         :company-list="companyList"
                         :disabled="rsClientMessageFormDisable || disabled"
                         :is-receivable="true"
                         :open-charge-list="true"
                         :rs-client-message-payable-r-m-b="rsClientMessagePayableRMB"
                         :rs-client-message-payable-tax-r-m-b="rsClientMessagePayableTaxRMB"
                         :rs-client-message-payable-tax-u-s-d="rsClientMessagePayableTaxUSD"
                         :rs-client-message-payable-u-s-d="rsClientMessagePayableUSD"
                         :rs-client-message-profit-r-m-b="rsClientMessageProfitRMB"
                         :rs-client-message-profit-tax-r-m-b="rsClientMessageProfitTaxRMB"
                         :rs-client-message-profit-tax-u-s-d="rsClientMessageProfitTaxUSD"
                         :rs-client-message-profit-u-s-d="rsClientMessageProfitUSD"
                         :rs-client-message-receivable-r-m-b="rsClientMessageReceivableRMB"
                         :rs-client-message-receivable-tax-r-m-b="rsClientMessageReceivableTaxRMB"
                         :rs-client-message-receivable-tax-u-s-d="rsClientMessageReceivableTaxUSD"
                         :rs-client-message-receivable-u-s-d="rsClientMessageReceivableUSD"
                         @copyFreight="$emit('copyFreight', $event)"
                         @deleteAll="rsClientMessage.rsChargeList=[]"
                         @deleteItem="rsClientMessage.rsChargeList=rsClientMessage.rsChargeList.filter(item=>{return item!=$event})"
                         @return="$emit('rsClientMessageCharge', $event)"
                         @returnProfit="$emit('handleProfit', $event)"
                         @selectRow="$emit('handleReceiveSelected', $event)"
            />
          </el-col>
        </transition>

        <!--分帐单列表-->
        <transition name="fade">
          <el-col v-if="chargeInfo" :span="10.5">
            <debit-note-list
              :is-receivable="1"
              :company-list="companyList"
              :debit-note-list="rsClientMessage.rsDebitNoteList"
              :disabled="false"
              :hidden-supplier="false"
              :rct-id="form.rctId"
              @addDebitNote="handleAddDebitNote"
              @copyFreight="handleCopyFreight"
              @deleteAll="handleDeleteAll"
              @deleteItem="handleDeleteItem"
              @editDebitNote="handleEditDebitNote"
            />
          </el-col>
        </transition>
      </el-row>
    </transition>

    <!-- 利润对话框 -->
    <el-dialog
      :visible.sync="profitOpen"
      title="单票利润"
      width="30%"
      @open="openProfit"
    >
      <el-table
        :data="profitTableData"
        border
        style="width: 100%"
      >
        <el-table-column
          label="货币"
          prop="currencyCode"
        >
        </el-table-column>
        <el-table-column
          label="应收"
          prop="receivable"
        >
        </el-table-column>
        <el-table-column
          label="应付"
          prop="payable"
        >
        </el-table-column>
        <el-table-column
          label="不含税利润" prop="profit"
          style="color: #0d0dfd"
        >
        </el-table-column>
        <el-table-column
          label="含税应收"
          prop="receivableTax"
        >
        </el-table-column>
        <el-table-column
          label="含税应付"
          prop="payableTax"
        >
        </el-table-column>
        <el-table-column
          label="含税利润"
          prop="profitTax"
        >
        </el-table-column>
      </el-table>

      <el-row>
        <el-col :span="5">
          <el-form-item label="折合币种" prop="rctOpDate">
            <el-select v-model="currencyCode" @change="profitCount(currencyCode)">
              <el-option label="RMB" value="RMB"/>
              <el-option label="USD" value="USD"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-row>
            <el-col :span="12">
              <div style="color: #0d0dfd">不含税利润</div>
            </el-col>
            <el-col :span="12">
              <el-input v-model="profit" placeholder="不含税利润"/>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="7">
          <el-row>
            <el-col :span="12">
              <div>含税利润</div>
            </el-col>
            <el-col :span="12">
              <el-input v-model="profitTax" placeholder="含税利润"/>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="5">
          <el-row>
            <el-col :span="12">
              <div>折算汇率</div>
            </el-col>
            <el-col :span="12">
              <el-input v-model="exchangeRate" placeholder="含税利润"/>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import LogisticsProgress from "../logisticsProgress"
import ChargeList from "../chargeList"
import TreeSelect from "@/components/TreeSelect"
import LocationSelect from "@/components/LocationSelect"
import {parseTime} from "@/utils/rich"
import currency from "currency.js"
import DebitNoteList from "@/views/system/document/debitNodeList.vue"

export default {
  name: "BillOfLadingInfo",
  components: {
    DebitNoteList,
    LogisticsProgress,
    ChargeList,
    TreeSelect,
    LocationSelect
  },
  props: {
    bookingMessageForm: {
      type: Object,
      required: true,
      default: null
    },
    openBookingMessage: {
      type: Boolean,
      default: false
    },
    bookingMessageStatus: {
      type: String,
      default: "<UNK>"
    },
    bookingMessageList: {
      type: Array,
      default: []
    },
    rsClientMessage: {
      type: Object,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    auditInfo: {
      type: Boolean,
      default: true
    },
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    companyList: {
      type: Array,
      default: () => []
    },
    rsClientMessageReceivableRMB: {
      type: Number,
      default: 0
    },
    rsClientMessageReceivableUSD: {
      type: Number,
      default: 0
    },
    rsClientMessagePayableRMB: {
      type: Number,
      default: 0
    },
    rsClientMessagePayableUSD: {
      type: Number,
      default: 0
    },
    rsClientMessageProfitRMB: {
      type: Number,
      default: 0
    },
    rsClientMessageProfitUSD: {
      type: Number,
      default: 0
    },
    rsClientMessageReceivableTaxRMB: {
      type: Number,
      default: 0
    },
    rsClientMessageReceivableTaxUSD: {
      type: Number,
      default: 0
    },
    rsClientMessagePayableTaxRMB: {
      type: Number,
      default: 0
    },
    rsClientMessagePayableTaxUSD: {
      type: Number,
      default: 0
    },
    rsClientMessageProfitTaxRMB: {
      type: Number,
      default: 0
    },
    rsClientMessageProfitTaxUSD: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      visible: true,
      bookingMessageTitle: "提单信息",
      psaBookingSelectData: {
        locationOptions: []
      },
      rsClientMessageFormDisable: false,
      fileOptions: [
        {file: "操作单", link: "getOpBill", templateList: ["整柜", "散货", "空运", "其他"]},
        {file: "提单", link: "getBillOfLading", templateList: ["套打提单", "电放提单"]},
        {
          file: "费用清单",
          link: "getChargeListBill",
          templateList: ["CN-广州瑞旗[招行USD+工行RMB]", "CN-广州瑞旗[USD->RMB]", "EN-广州瑞旗[招行USD]", "EN-广州瑞旗[RMB->USD]", "EN- 瑞旗香港账户[HSBC RMB->USD]", "EN- 香港瑞旗[HSBC]", "CN-广州正泽[招行USD+RMB]", "CN-广州正泽[USD->RMB]"]
        }
      ],
      payWayOptions: [
        {label: "预付", value: "FREIGHTP REPAID"},
        {label: "到付", value: "FREIGHTP COLLECT"}
      ],
      profitOpen: false,
      profitTableData: [],
      currencyCode: "RMB",
      profit: 0,
      profitTax: 0,
      exchangeRate: 1
    }
  },
  computed: {
    rsClientServiceInstance() {
      return this.rsClientMessage || {}
    },
    opConfirmedName() {
      return this.rsClientServiceInstance.dnOpConfirmedName || ""
    },
    opConfirmedDate() {
      return this.rsClientServiceInstance.dnOpConfirmedDate || ""
    },
    salesConfirmedName() {
      return this.rsClientServiceInstance.dnSalesConfirmedName || ""
    },
    salesConfirmedDate() {
      return this.rsClientServiceInstance.dnSalesConfirmedDate || ""
    },
    clientConfirmedName() {
      return this.rsClientServiceInstance.dnClientConfirmedName || ""
    },
    clientConfirmedDate() {
      return this.rsClientServiceInstance.dnClientConfirmedDate || ""
    },
    accountConfirmedName() {
      return this.rsClientServiceInstance.accountConfirmedName || ""
    },
    accountConfirmedDate() {
      return this.rsClientServiceInstance.accountConfirmedDate || ""
    }
  },
  created() {
    // 初始化时的操作
  },
  methods: {
    handleAddDebitNote() {
      let row = {}
      row.sqdRctNo = this.form.rctNo
      row.rctId = this.form.rctId
      row.isRecievingOrPaying = 0
      this.$emit('addDebitNote', row)
    },
    toggleVisible() {
      this.visible = !this.visible
    },
    handleFileAction(methodName, templateType) {
      this.$emit(methodName, templateType)
    },
    handleSelectionChange(selection) {
      this.$emit("handleSelectionChange", selection)
    },
    handleBookingMessageUpdate(row) {
      this.$emit("handleBookingMessageUpdate", row)
    },
    addBookingMessage() {
      this.$emit("handleAddBookingMessage", this.bookingMessageForm)
    },
    bookingMessageConfirm() {
      // 将操作通过事件发送给父组件
      this.$emit("bookingMessageConfirm", this.bookingMessageForm)

    },
    closeBookingMessage() {
      this.$emit("closeBookingMessage")
    },
    deleteBookingMessage(row) {
      // 通过事件发送给父组件
      this.$emit("deleteBookingMessage", row)
    },
    deleteLogisticsItem(item) {
      if (this.rsClientMessage && this.rsClientMessage.rsOpLogList) {
        this.rsClientMessage.rsOpLogList = this.rsClientMessage.rsOpLogList.filter(log => log !== item)
      }
    },
    updateLogisticsProgress(data) {
      if (this.rsClientMessage) {
        this.rsClientMessage.rsOpLogList = data
      }
    },
    openProfit() {
      this.profitTableData = []

      let RMB = {}
      RMB.currencyCode = "RMB"
      RMB.receivable = this.rsClientMessageReceivableRMB
      RMB.payable = this.rsClientMessagePayableRMB
      // 不含税利润
      RMB.profit = this.rsClientMessageProfitRMB
      // 含税应收
      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB
      // 含税应付
      RMB.payableTax = this.rsClientMessagePayableTaxRMB
      // 含税利润
      RMB.profitTax = this.rsClientMessageProfitTaxRMB

      let USD = {}
      USD.currencyCode = "USD"
      USD.receivable = this.rsClientMessageReceivableUSD
      USD.payable = this.rsClientMessagePayableUSD
      USD.profit = this.rsClientMessageProfitUSD
      USD.receivableTax = this.rsClientMessageReceivableTaxUSD
      USD.payableTax = this.rsClientMessagePayableTaxUSD
      USD.profitTax = this.rsClientMessageProfitTaxUSD

      this.profitTableData.push(RMB)
      this.profitTableData.push(USD)

      this.profitCount("RMB")
    },
    profitCount(type) {
      let exchangeRate
      for (const a of this.$store.state.data.exchangeRateList) {
        if (this.form.podEta) {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(this.form.podEta)
            && parseTime(this.form.podEta) <= parseTime(a.validTo)
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
        if (!exchangeRate) {
          if (a.localCurrency === "RMB"
            && "USD" == a.overseaCurrency
            && parseTime(a.validFrom) <= parseTime(new Date())
            && parseTime(new Date()) <= parseTime(a.validTo)) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      }
      this.exchangeRate = exchangeRate

      if (type === "RMB") {
        // 都折算成人民币
        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value
        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value
      } else {
        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value
        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value
      }
    },
    handleCopyFreight(charge) {
      this.$emit('copyFreight', charge)
    },
    handleDeleteAll() {
      this.$emit('deleteAll')
    },
    handleDeleteItem(charge) {
      this.$emit('deleteItem', charge)
    },
    handleEditDebitNote(debitNote) {
      this.$emit('editDebitNote', debitNote)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document.scss';
</style>
