(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22d747"],{f821:function(l,e,n){"use strict";n.r(e),n.d(e,"createServiceInstance",(function(){return r})),n.d(e,"createServiceObject",(function(){return i})),n.d(e,"createService",(function(){return s})),n.d(e,"SERVICE_TYPE_MAP",(function(){return q}));var u=n("5530");function r(){return{accountConfirmTime:null,agreementNo:"",agreementTypeCode:null,clientConfirmedTime:null,confirmAccountId:null,createBy:null,createByName:null,createTime:null,deleteBy:null,deleteByName:null,deleteStatus:null,deleteTime:null,inquiryInnerRemark:"",inquiryLeatestUpdatedTime:null,inquiryNo:null,inquiryNotice:null,inquiryPsaId:null,isAccountConfirmed:null,isDnClientConfirmed:null,isDnOpConfirmed:null,isDnPsaConfirmed:null,isDnSalesConfirmed:null,isDnSupplierConfirmed:null,logisticsPaymentTermsCode:null,maxWeight:null,opConfirmedTime:null,paymentTitleCode:null,permissionLevel:null,psaConfirmedTime:null,rctId:null,rctNo:null,remark:null,salesConfirmedTime:null,serviceBelongTo:null,serviceId:null,serviceTypeId:null,supplierConfirmedTime:null,supplierContact:null,supplierId:null,supplierName:null,supplierSummary:null,supplierTel:null,updateBy:null,updateByName:null,updateTime:null}}function i(){var l=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object(u["a"])(Object(u["a"])({},l&&{rsServiceInstances:r()}),{},{rsChargeList:[],rsOpLogList:[],rsDocList:[]},e)}function s(l){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};switch(l){case"seaFcl":return t(e);case"seaLcl":return o(e);case"air":return a(e);case"rail":return c(e);case"express":return d(e);case"ctnrTruck":return m(e);case"bulkTruck":return f(e);case"docDeclare":return p(e);case"freeDeclare":return b(e);case"warehouse":return T(e);default:return i()}}function t(l){var e=i(!0,Object(u["a"])({seaId:null,bookingApplyTime:null,bookingConfirmTime:null,billNumber:null,etd:null,eta:null,sqdContainersSealsSum:null,sqdVesselRemark:null,sqdPreparingRemark:null,sqdMBLNumber:null,sqdSoNo:null,sqdOThroughBillNumber:null,sqdInsuranceNumber:null,sqdMasterBl:null,inquiryExpDepartureTime:null,firstCyCutoffTime:null,firstBlCutoffTime:null,firstVgmCutoffTime:null,firstVessel:null,firstPol:null,firstPod:null,secondVessel:null,secondPol:null,secondPod:null,destinationPortEta:null},l));return e.rsServiceInstances.serviceTypeId=1,e}function o(l){var e=i(!0,Object(u["a"])({seaId:null,bookingApplyTime:null,bookingConfirmTime:null,billNumber:null,etd:null,eta:null,sqdContainersSealsSum:null,sqdVesselRemark:null,sqdPreparingRemark:null,sqdMBLNumber:null,sqdSoNo:null,sqdOThroughBillNumber:null,sqdInsuranceNumber:null,sqdMasterBl:null,inquiryExpDepartureTime:null,firstCyCutoffTime:null,firstBlCutoffTime:null,firstVgmCutoffTime:null,firstVessel:null,firstPol:null,firstPod:null,secondVessel:null,secondPol:null,secondPod:null,destinationPortEta:null},l));return e.rsServiceInstances.serviceTypeId=2,e}function a(l){var e=i(!0,Object(u["a"])({airId:null,bookingApplyTime:null,bookingConfirmTime:null,sqdFlightNumber:null,sqdAirwaybillNumber:null,sqdOThroughBillNumber:null,sqdVesselRemark:null,sqdPreparingRemark:null,firstPol:null,firstPod:null,secondPol:null,secondPod:null,etd:null,eta:null},l));return e.rsServiceInstances.serviceTypeId=10,e}function c(l){return i(!0,Object(u["a"])({railId:null,bookingApplyTime:null,bookingConfirmTime:null,sqdRailNumber:null,sqdMBLNumber:null,sqdVesselRemark:null,sqdPreparingRemark:null,etd:null,eta:null},l))}function d(l){return i(!0,Object(u["a"])({expressId:null,bookingApplyTime:null,bookingConfirmTime:null,sqdExpressNumber:null,etd:null,eta:null},l))}function m(l){return i(!0,Object(u["a"])({truckId:null,rsOpTruckList:[]},l))}function f(l){return i(!0,Object(u["a"])({truckId:null,rsOpTruckList:[]},l))}function p(l){return i(!0,Object(u["a"])({declareId:null,bookingApplyTime:null,bookingConfirmTime:null,sqdDeclareNumber:null,sqdDeclareRemark:null},l))}function b(l){return i(!0,Object(u["a"])({declareId:null,bookingApplyTime:null,bookingConfirmTime:null,sqdDeclareNumber:null,sqdDeclareRemark:null},l))}function T(l){return i(!0,Object(u["a"])({warehouseId:null},l))}var q={1:"整柜海运",2:"拼柜海运",10:"空运",20:"整柜铁路",21:"拼柜铁路",40:"快递",50:"整柜拖车",51:"散货拖车",60:"单证报关",61:"全包报关",70:"代理放单",71:"清关代理",80:"仓储服务"}}}]);