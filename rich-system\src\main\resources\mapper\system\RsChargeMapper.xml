<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsChargeMapper">
    <resultMap type="RsCharge" id="RsChargeResult">
        <result property="chargeId" column="charge_id"/>
        <result property="chargeTypeId" column="charge_type_id"/>
        <result property="serviceId" column="service_id"/>
        <result property="debitNoteId" column="debit_note_id"/>
        <result property="sqdServiceTypeId" column="sqd_service_type_id"/>
        <result property="sqdRctNo" column="sqd_rct_no"/>
        <result property="relatedFreightId" column="related_freight_id"/>
        <result property="isRecievingOrPaying" column="is_recieving_or_paying"/>
        <result property="clearingCompanyId" column="clearing_company_id"/>
        <result property="clearingCompanySummary" column="clearing_company_summary"/>
        <result property="quotationStrategyId" column="quotation_strategy_id"/>
        <result property="dnChargeNameId" column="dn_charge_name_id"/>
        <result property="dnCurrencyCode" column="dn_currency_code"/>
        <result property="dnUnitRate" column="dn_unit_rate"/>
        <result property="dnUnitCode" column="dn_unit_code"/>
        <result property="dnAmount" column="dn_amount"/>
        <result property="basicCurrencyRate" column="basic_currency_rate"/>
        <result property="dutyRate" column="duty_rate"/>
        <result property="subtotal" column="subtotal"/>
        <result property="chargeRemark" column="charge_remark"/>
        <result property="isDnSalesConfirmed" column="is_dn_sales_confirmed"/>
        <result property="isDnClientConfirmed" column="is_dn_client_confirmed"/>
        <result property="isDnOpConfirmed" column="is_dn_op_confirmed"/>
        <result property="isDnPsaConfirmed" column="is_dn_psa_confirmed"/>
        <result property="isDnSupplierConfirmed" column="is_dn_supplier_confirmed"/>
        <result property="isAccountConfirmed" column="is_account_confirmed"/>
        <result property="confirmAccountId" column="confirm_account_id"/>
        <result property="accountConfirmTime" column="account_confirm_time"/>
        <result property="clearingCurrencyCode" column="clearing_currency_code"/>
        <result property="dnCurrencyReceived" column="dn_currency_received"/>
        <result property="dnCurrencyPaid" column="dn_currency_paid"/>
        <result property="dnCurrencyBalance" column="dn_currency_balance"/>
        <result property="accountReceivedIdList" column="account_received_id_list"/>
        <result property="accountPaidIdList" column="account_paid_id_list"/>
        <result property="logisticsInvoiceIdList" column="logistics_invoice_id_list"/>
        <result property="companyName" column="company_name"/>
        <result property="chargeName" column="charge_name"/>
        <result property="sqdRctId" column="sqd_rct_id"/>
        <result property="sqdInvoiceIssued" column="sqd_invoice_issued"/>
        <result property="sqdInvoiceBalance" column="sqd_invoice_balance"/>
        <result property="sqdIsAccountConfirmed" column="sqd_is_account_confirmed"/>
        <result property="sqdDnCurrencyPaid" column="sqd_dn_currency_paid"/>
        <result property="sqdDnCurrencyBalance" column="sqd_dn_currency_balance"/>
        <result property="sqdWriteoffNoList" column="sqd_writeoff_no_list"/>
        <result property="staffId" column="staff_id"/>
        <result property="currencyRateCalculateDate" column="currency_rate_calculate_date"/>
        <result property="paymentTitleCode" column="payment_title_code"/>
        <!--展示-->
        <result property="showClient" column="show_client"/>
        <result property="showSupplier" column="show_supplier"/>
        <result property="showQuotationCharge" column="show_quotation_charge"/>
        <result property="showCostCharge" column="show_cost_charge"/>
        <result property="showQuotationCurrency" column="show_quotation_currency"/>
        <result property="showCostCurrency" column="show_cost_currency"/>
        <result property="showQuotationUnit" column="show_quotation_unit"/>
        <result property="showCostUnit" column="show_cost_unit"/>
        <result property="showStrategy" column="show_strategy"/>
        <result property="showUnitRate" column="show_unit_rate"/>
        <result property="showAmount" column="show_amount"/>
        <result property="showCurrencyRate" column="show_currency_rate"/>
        <result property="showDutyRate" column="show_duty_rate"/>
        <result property="serviceName" column="service_local_name"/>

        <result property="bankRecordId" column="bank_record_id"/>
        <result property="writeoffStatus" column="writeoff_status"/>
        <result property="sqdRaletiveRctList" column="sqd_raletive_rct_list"/>
        <result property="sqdServiceDetailsCode" column="sqd_service_details_code"/>
        <result property="logisticsPaymentTermsCode" column="logistics_payment_terms_code"/>

        <result property="pol" column="pol"/>
        <result property="destinationPort" column="destination_port"/>
        <result property="blNo" column="bl_no"/>
        <result property="sqdContainersSealsSum" column="sqd_containers_seals_sum"/>
        <result property="revenueTon" column="revenue_ton"/>
        <result property="eta" column="eta"/>
        <result property="etd" column="etd"/>
        <result property="salesId" column="sales_id"/>

        <result property="receivableUsd" column="receivable_usd"/>
        <result property="receivableRmb" column="receivable_rmb"/>
        <result property="uncollectedUsd" column="uncollected_usd"/>
        <result property="uncollectedRmb" column="uncollected_rmb"/>
        <result property="payableUsd" column="payable_usd"/>
        <result property="payableRmb" column="payable_rmb"/>
        <result property="unpaidUsd" column="unpaid_usd"/>
        <result property="unpaidRmb" column="unpaid_rmb"/>
        <result property="clientId" column="client_id"/>
        <result property="clientSummary" column="client_summary"/>
        <result property="orderBelongsTo" column="order_belongs_to"/>

        <association property="midChargeBankWriteoff"
                     resultMap="com.rich.system.mapper.MidChargeBankWriteoffMapper.MidChargeBankWriteoffResult"/>
    </resultMap>


    <sql id="selectRsChargeVo">
        select rc.charge_id,
               service_id,
               sqd_service_type_id,
               sqd_rct_no,
               related_freight_id,
               is_recieving_or_paying,
               clearing_company_id,
               clearing_company_summary,
               quotation_strategy_id,
               dn_charge_name_id,
               dn_currency_code,
               dn_unit_rate,
               dn_unit_code,
               dn_amount,
               basic_currency_rate,
               duty_rate,
               currency_rate_calculate_date,
               subtotal,
               charge_remark,
               clearing_currency_code,
               dn_currency_received,
               dn_currency_paid,
               dn_currency_balance,
               account_received_id_list,
               account_paid_id_list,
               logistics_invoice_id_list,
               sqd_invoice_issued,
               sqd_invoice_balance,
               rc.is_account_confirmed,
               sqd_dn_currency_paid,
               sqd_dn_currency_balance,
               sqd_writeoff_no_list,
               rc.payment_title_code,
               IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
               bc.charge_local_name                                                         charge_name,
               bdst.service_local_name                                                   as service_local_name,
               rr.sales_id                                                               as staff_id
        from rs_charge rc
                 left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
                 left join ext_company ec on rc.clearing_company_id = ec.company_id
                 left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
                 left join rich.rs_rct rr on rr.rct_id = rc.sqd_rct_id
    </sql>

    <select id="selectWriteOffRsChargeList" parameterType="RsCharge" resultMap="RsChargeResult">
        select rc.charge_id,
        service_id,
        sqd_service_type_id,
        rc.sqd_rct_no,
        related_freight_id,
        rc.is_recieving_or_paying,
        rc.clearing_company_id,
        clearing_company_summary,
        quotation_strategy_id,
        dn_charge_name_id,
        dn_currency_code,
        dn_unit_rate,
        dn_unit_code,
        dn_amount,
        basic_currency_rate,
        duty_rate,
        currency_rate_calculate_date,
        subtotal,
        charge_remark,
        clearing_currency_code,
        dn_currency_received,
        dn_currency_paid,
        dn_currency_balance,
        account_received_id_list,
        account_paid_id_list,
        logistics_invoice_id_list,
        sqd_invoice_issued,
        sqd_invoice_balance,
        rc.is_account_confirmed,
        rc.sqd_dn_currency_paid,
        rc.sqd_dn_currency_balance,
        sqd_writeoff_no_list,
        IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
        bc.charge_local_name charge_name,
        bdst.service_local_name as service_local_name,
        rr.sales_id as staff_id,
        rc.payment_title_code,
        mcbw.mid_charge_bank_id, mcbw.charge_id, mcbw.bank_record_id, mcbw.sqd_rct_no, mcbw.sqd_is_recieving_or_paying,
        mcbw.sqd_clearing_company_id, mcbw.sqd_dn_charge_name_id, mcbw.sqd_charge_shortname, mcbw.sqd_dn_currency_code,
        mcbw.temp_dn_balance_remain, mcbw.sqd_is_account_confirmed, mcbw.sqd_dn_currency_paid,
        mcbw.sqd_dn_currency_balance, mcbw.writeoff_from_dn_balance, mcbw.sqd_bank_currency_code,
        mcbw.temp_bank_balance_remain, mcbw.dn_basic_rate, mcbw.bank_basic_rate, mcbw.exchange_rate_showing,
        mcbw.writeoff_from_bank_balance, mcbw.writeoff_status, mcbw.writeoff_staff_id, mcbw.writeoff_time
        from rs_charge rc
        left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
        left join ext_company ec on rc.clearing_company_id = ec.company_id
        left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        left join rich.rs_rct rr on rr.rct_id = rc.sqd_rct_id
        left join (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY bank_record_id,charge_id ORDER BY
        writeoff_time DESC) AS
        row_num FROM mid_charge_bank_writeoff where bank_record_id = #{bankRecordId} ) AS subquery WHERE row_num = 1 )
        mcbw on mcbw.charge_id=rc.charge_id
        <where>
            rr.op_accept=1 and mcbw.bank_record_id = #{bankRecordId} and rc.sqd_dn_currency_balance = 0
            <if test="serviceId != null ">
                and service_id = #{serviceId}
            </if>
            <if test="sqdServiceTypeId != null ">
                and sqd_service_type_id = #{sqdServiceTypeId}
            </if>
            <if test="sqdRctNo != null  and sqdRctNo != ''">
                and rc.sqd_rct_no = #{sqdRctNo}
            </if>
            <if test="relatedFreightId != null ">
                and related_freight_id = #{relatedFreightId}
            </if>
            <!--<if test="isRecievingOrPaying != null ">
                and rc.is_recieving_or_paying = #{isRecievingOrPaying}
            </if>-->
            <!--销完的流水只展示销账记录-->
            <if test="isRecievingOrPaying != null and writeoffStatusString =='ALL'.toString()">
                and mcbw.mid_charge_bank_id is not null
            </if>
            <!--没销过的流水-->
            <if test="isRecievingOrPaying != null and writeoffStatusString =='NULL'.toString()">
                and rc.sqd_dn_currency_balance <![CDATA[<>]]> 0
            </if>
            <if test="clearingCompanyId != null ">
                and rc.clearing_company_id = #{clearingCompanyId}
            </if>
            <if test="clearingCompanySummary != null  and clearingCompanySummary != ''">
                and clearing_company_summary = #{clearingCompanySummary}
            </if>
            <if test="quotationStrategyId != null ">
                and qoutation_strategy_id = #{quotationStrategyId}
            </if>
            <if test="dnChargeNameId != null ">
                and dn_charge_name_id = #{dnChargeNameId}
            </if>
            <if test="dnCurrencyCode != null  and dnCurrencyCode != ''">
                and dn_currency_code = #{dnCurrencyCode}
            </if>
            <if test="dnUnitRate != null ">
                and dn_unit_rate = #{dnUnitRate}
            </if>
            <if test="dnUnitCode != null  and dnUnitCode != ''">
                and dn_unit_code = #{dnUnitCode}
            </if>
            <if test="dnAmount != null ">
                and dn_amount = #{dnAmount}
            </if>
            <if test="basicCurrencyRate != null ">
                and basic_currency_rate = #{basicCurrencyRate}
            </if>
            <if test="dutyRate != null ">
                and duty_rate = #{dutyRate}
            </if>
            <if test="subtotal != null ">
                and subtotal = #{subtotal}
            </if>
            <if test="chargeRemark != null  and chargeRemark != ''">
                and charge_remark = #{chargeRemark}
            </if>
            <if test="isDnSalesConfirmed != null  and isDnSalesConfirmed != ''">
                and is_dn_sales_confirmed = #{isDnSalesConfirmed}
            </if>
            <if test="isDnClientConfirmed != null  and isDnClientConfirmed != ''">
                and is_dn_client_confirmed = #{isDnClientConfirmed}
            </if>
            <if test="isDnOpConfirmed != null  and isDnOpConfirmed != ''">
                and is_dn_op_confirmed = #{isDnOpConfirmed}
            </if>
            <if test="isDnPsaConfirmed != null  and isDnPsaConfirmed != ''">
                and is_dn_psa_confirmed = #{isDnPsaConfirmed}
            </if>
            <if test="isDnSupplierConfirmed != null  and isDnSupplierConfirmed != ''">
                and is_dn_supplier_confirmed = #{isDnSupplierConfirmed}
            </if>
            <if test="isAccountConfirmed != null  and isAccountConfirmed != ''">
                and rc.is_account_confirmed = #{isAccountConfirmed}
            </if>
            <if test="confirmAccountId != null ">
                and confirm_account_id = #{confirmAccountId}
            </if>
            <if test="accountConfirmTime != null ">
                and account_confirm_time = #{accountConfirmTime}
            </if>
            <if test="clearingCurrencyCode != null  and clearingCurrencyCode != ''">
                and clearing_currency_code = #{clearingCurrencyCode}
            </if>
            <if test="dnCurrencyReceived != null ">
                and dn_currency_received = #{dnCurrencyReceived}
            </if>
            <if test="dnCurrencyPaid != null ">
                and dn_currency_paid = #{dnCurrencyPaid}
            </if>
            <if test="dnCurrencyBalance != null ">
                and dn_currency_balance = #{dnCurrencyBalance}
            </if>
            <if test="accountReceivedIdList != null  and accountReceivedIdList != ''">
                and account_received_id_list = #{accountReceivedIdList}
            </if>
            <if test="accountPaidIdList != null  and accountPaidIdList != ''">
                and account_paid_id_list = #{accountPaidIdList}
            </if>
            <if test="logisticsInvoiceIdList != null  and logisticsInvoiceIdList != ''">
                and logistics_invoice_id_list = #{logisticsInvoiceIdList}
            </if>
            <if test="sqdInvoiceIssued != null ">
                and sqd_invoice_issued = #{sqdInvoiceIssued}
            </if>
            <if test="sqdInvoiceBalance != null ">
                and sqd_invoice_balance = #{sqdInvoiceBalance}
            </if>
            <if test="sqdIsAccountConfirmed != null  and sqdIsAccountConfirmed != ''">
                and sqd_is_account_confirmed = #{sqdIsAccountConfirmed}
            </if>
            <if test="sqdDnCurrencyPaid != null ">
                and sqd_dn_currency_paid = #{sqdDnCurrencyPaid}
            </if>
            <if test="sqdDnCurrencyBalance != null ">
                and sqd_dn_currency_balance = #{sqdDnCurrencyBalance}
            </if>
            <if test="sqdWriteoffNoList != null  and sqdWriteoffNoList != ''">
                and sqd_writeoff_no_list = #{sqdWriteoffNoList}
            </if>
            <if test="sqdRaletiveRctList != null  and sqdRaletiveRctList != ''">
                and find_in_set(rc.sqd_rct_no,#{sqdRaletiveRctList})
            </if>
        </where>
    </select>

    <select id="selectRsChargeList" parameterType="RsCharge" resultMap="RsChargeResult">
        select rc.charge_id,
        service_id,
        sqd_service_type_id,
        rc.sqd_rct_no,
        related_freight_id,
        rc.is_recieving_or_paying,
        rc.clearing_company_id,
        clearing_company_summary,
        quotation_strategy_id,
        dn_charge_name_id,
        dn_currency_code,
        dn_unit_rate,
        dn_unit_code,
        dn_amount,
        basic_currency_rate,
        duty_rate,
        currency_rate_calculate_date,
        subtotal,
        charge_remark,
        clearing_currency_code,
        dn_currency_received,
        dn_currency_paid,
        dn_currency_balance,
        account_received_id_list,
        account_paid_id_list,
        rc.writeoff_status,
        logistics_invoice_id_list,
        sqd_invoice_issued,
        sqd_invoice_balance,
        rc.is_account_confirmed,
        rc.sqd_dn_currency_paid,
        rc.sqd_dn_currency_balance,
        sqd_writeoff_no_list,
        IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
        bc.charge_local_name charge_name,
        bdst.service_local_name as service_local_name,
        rr.sales_id as staff_id,
        rc.payment_title_code,
        mcbw.mid_charge_bank_id, mcbw.charge_id, mcbw.bank_record_id, mcbw.sqd_rct_no, mcbw.sqd_is_recieving_or_paying,
        mcbw.sqd_clearing_company_id, mcbw.sqd_dn_charge_name_id, mcbw.sqd_charge_shortname, mcbw.sqd_dn_currency_code,
        mcbw.temp_dn_balance_remain, mcbw.sqd_is_account_confirmed, mcbw.sqd_dn_currency_paid,
        mcbw.sqd_dn_currency_balance, mcbw.writeoff_from_dn_balance, mcbw.sqd_bank_currency_code,
        mcbw.temp_bank_balance_remain, mcbw.dn_basic_rate, mcbw.bank_basic_rate, mcbw.exchange_rate_showing,
        mcbw.writeoff_from_bank_balance, mcbw.writeoff_status, mcbw.writeoff_staff_id, mcbw.writeoff_time,
        <!--费用列表中关联的操作单信息-->
        dep.location_local_name as pol,
        dep2.location_en_name as destination_port,
        rr.bl_no as bl_no,
        rr.sqd_containers_seals_sum as sqd_containers_seals_sum,
        rr.revenue_ton as revenue_ton,
        rr.sales_id as sales_id,
        rr.client_id as client_id,
        SUBSTRING_INDEX(SUBSTRING_INDEX(rr.client_summary, '/', 2), '/', -1) as client_summary,
        rr.order_belongs_to as order_belongs_to,
        ifnull(rr.eta,rr.destination_port_eta) as eta,
        ifnull(rr.etd,rr.pod_eta) as etd,
        <!-- 应收USD 应收RMB 未收USD 未收RMB 应付USD 应付RMB 未付USD 未付RMB -->
        CASE WHEN rc.is_recieving_or_paying = 0 AND rc.dn_currency_code = 'USD' THEN rc.subtotal ELSE 0 END AS receivable_usd,
        CASE WHEN rc.is_recieving_or_paying = 0 AND rc.dn_currency_code = 'RMB' THEN rc.subtotal ELSE 0 END AS receivable_rmb,
        CASE WHEN rc.is_recieving_or_paying = 0 AND rc.dn_currency_code = 'USD' THEN rc.sqd_dn_currency_balance ELSE 0 END AS uncollected_usd,
        CASE WHEN rc.is_recieving_or_paying = 0 AND rc.dn_currency_code = 'RMB' THEN rc.sqd_dn_currency_balance ELSE 0 END AS uncollected_rmb,
        CASE WHEN rc.is_recieving_or_paying = 1 AND rc.dn_currency_code = 'USD' THEN rc.subtotal ELSE 0 END AS payable_usd,
        CASE WHEN rc.is_recieving_or_paying = 1 AND rc.dn_currency_code = 'RMB' THEN rc.subtotal ELSE 0 END AS payable_rmb,
        CASE WHEN rc.is_recieving_or_paying = 1 AND rc.dn_currency_code = 'USD' THEN rc.sqd_dn_currency_balance ELSE 0 END AS unpaid_usd,
        CASE WHEN rc.is_recieving_or_paying = 1 AND rc.dn_currency_code = 'RMB' THEN rc.sqd_dn_currency_balance ELSE 0 END AS unpaid_rmb

        from rs_charge rc
        left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
        left join ext_company ec on rc.clearing_company_id = ec.company_id
        left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        left join rich.rs_rct rr on rr.rct_id = rc.sqd_rct_id
        left join bas_dist_location dep on rr.pol_id = dep.location_id
        left join bas_dist_location dep2 on rr.destination_port_id = dep2.location_id
        left join (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY bank_record_id,charge_id ORDER BY
        writeoff_time DESC) AS
        row_num FROM mid_charge_bank_writeoff WHERE bank_record_id = #{bankRecordId}) AS subquery WHERE row_num = 1)
        mcbw on mcbw.charge_id=rc.charge_id
        <where>
            <!--            rr.op_accept=1 and rc.writeoff_status = '0' and rc.sqd_dn_currency_balance != 0-->
            rr.op_accept=1
            <if test="clientId != null ">
                and rr.client_id = #{clientId}
            </if>
            <if test="writeoffStatus != null ">
                and rc.writeoff_status = #{writeoffStatus}
            </if>
            <if test="serviceId != null ">
                and service_id = #{serviceId}
            </if>
            <if test="sqdServiceTypeId != null ">
                and sqd_service_type_id = #{sqdServiceTypeId}
            </if>
            <if test="sqdRctNo != null  and sqdRctNo != ''">
                and rc.sqd_rct_no like concat(#{sqdRctNo}, '%')
            </if>
            <if test="relatedFreightId != null ">
                and related_freight_id = #{relatedFreightId}
            </if>
            <if test="isRecievingOrPaying != null ">
                and rc.is_recieving_or_paying = #{isRecievingOrPaying}
            </if>
            <!--没销过的流水-->
            <if test="isRecievingOrPaying != null and writeoffStatusString =='NULL'.toString()">
                and rc.sqd_dn_currency_balance <![CDATA[<>]]> 0
            </if>
            <if test="clearingCompanyId != null ">
                and rc.clearing_company_id = #{clearingCompanyId}
            </if>
            <if test="clearingCompanySummary != null  and clearingCompanySummary != ''">
                and clearing_company_summary = #{clearingCompanySummary}
            </if>
            <if test="quotationStrategyId != null ">
                and qoutation_strategy_id = #{quotationStrategyId}
            </if>
            <if test="dnChargeNameId != null ">
                and dn_charge_name_id = #{dnChargeNameId}
            </if>
            <if test="dnCurrencyCode != null  and dnCurrencyCode != ''">
                and dn_currency_code = #{dnCurrencyCode}
            </if>
            <if test="dnUnitRate != null ">
                and dn_unit_rate = #{dnUnitRate}
            </if>
            <if test="dnUnitCode != null  and dnUnitCode != ''">
                and dn_unit_code = #{dnUnitCode}
            </if>
            <if test="dnAmount != null ">
                and dn_amount = #{dnAmount}
            </if>
            <if test="basicCurrencyRate != null ">
                and basic_currency_rate = #{basicCurrencyRate}
            </if>
            <if test="dutyRate != null ">
                and duty_rate = #{dutyRate}
            </if>
            <if test="subtotal != null ">
                and subtotal = #{subtotal}
            </if>
            <if test="chargeRemark != null  and chargeRemark != ''">
                and charge_remark = #{chargeRemark}
            </if>
            <if test="isDnSalesConfirmed != null  and isDnSalesConfirmed != ''">
                and is_dn_sales_confirmed = #{isDnSalesConfirmed}
            </if>
            <if test="isDnClientConfirmed != null  and isDnClientConfirmed != ''">
                and is_dn_client_confirmed = #{isDnClientConfirmed}
            </if>
            <if test="isDnOpConfirmed != null  and isDnOpConfirmed != ''">
                and is_dn_op_confirmed = #{isDnOpConfirmed}
            </if>
            <if test="isDnPsaConfirmed != null  and isDnPsaConfirmed != ''">
                and is_dn_psa_confirmed = #{isDnPsaConfirmed}
            </if>
            <if test="isDnSupplierConfirmed != null  and isDnSupplierConfirmed != ''">
                and is_dn_supplier_confirmed = #{isDnSupplierConfirmed}
            </if>
            <if test="isAccountConfirmed != null  and isAccountConfirmed != ''">
                and rc.is_account_confirmed = #{isAccountConfirmed}
            </if>
            <if test="confirmAccountId != null ">
                and confirm_account_id = #{confirmAccountId}
            </if>
            <if test="accountConfirmTime != null ">
                and account_confirm_time = #{accountConfirmTime}
            </if>
            <if test="clearingCurrencyCode != null  and clearingCurrencyCode != ''">
                and clearing_currency_code = #{clearingCurrencyCode}
            </if>
            <if test="dnCurrencyReceived != null ">
                and dn_currency_received = #{dnCurrencyReceived}
            </if>
            <if test="dnCurrencyPaid != null ">
                and dn_currency_paid = #{dnCurrencyPaid}
            </if>
            <if test="dnCurrencyBalance != null ">
                and dn_currency_balance = #{dnCurrencyBalance}
            </if>
            <if test="accountReceivedIdList != null  and accountReceivedIdList != ''">
                and account_received_id_list = #{accountReceivedIdList}
            </if>
            <if test="accountPaidIdList != null  and accountPaidIdList != ''">
                and account_paid_id_list = #{accountPaidIdList}
            </if>
            <if test="logisticsInvoiceIdList != null  and logisticsInvoiceIdList != ''">
                and logistics_invoice_id_list = #{logisticsInvoiceIdList}
            </if>
            <if test="sqdInvoiceIssued != null ">
                and sqd_invoice_issued = #{sqdInvoiceIssued}
            </if>
            <if test="sqdInvoiceBalance != null ">
                and sqd_invoice_balance = #{sqdInvoiceBalance}
            </if>
            <if test="sqdIsAccountConfirmed != null  and sqdIsAccountConfirmed != ''">
                and sqd_is_account_confirmed = #{sqdIsAccountConfirmed}
            </if>
            <if test="sqdDnCurrencyPaid != null ">
                and sqd_dn_currency_paid = #{sqdDnCurrencyPaid}
            </if>
            <if test="sqdDnCurrencyBalance != null ">
                and sqd_dn_currency_balance = #{sqdDnCurrencyBalance}
            </if>
            <if test="sqdWriteoffNoList != null  and sqdWriteoffNoList != ''">
                and sqd_writeoff_no_list = #{sqdWriteoffNoList}
            </if>
            <if test="sqdRaletiveRctList != null  and sqdRaletiveRctList != ''">
                and find_in_set(rc.sqd_rct_no,#{sqdRaletiveRctList})
            </if>
            <if test="ATDDate != null  and ATDDate.length == 2">
                and rr.pod_eta BETWEEN #{ATDDate[0],javaType=java.util.Date,jdbcType=TIMESTAMP} AND #{ATDDate[1],javaType=java.util.Date,jdbcType=TIMESTAMP}
            </if>
            <if test="ATADate != null  and ATADate.length == 2">
                and rr.destination_port_eta BETWEEN #{ATADate[0],javaType=java.util.Date,jdbcType=TIMESTAMP} AND #{ATADate[1],javaType=java.util.Date,jdbcType=TIMESTAMP}
            </if>
            <if test="orderBelongsTo != null">
                and rr.order_belongs_to = #{orderBelongsTo}
            </if>
        </where>
    </select>

    <select id="selectRsChargeByChargeId" parameterType="Long" resultMap="RsChargeResult">
        <include refid="selectRsChargeVo"/>
        where charge_id = #{chargeId}
    </select>


    <insert id="upsertRsCharge" parameterType="RsCharge" useGeneratedKeys="true" keyProperty="chargeId">
        insert into rs_charge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chargeId != null">
                charge_id,
            </if>
            <if test="sqdRctId != null">
                sqd_rct_id,
            </if>
            <if test="serviceId != null">
                service_id,
            </if>
            <if test="debitNoteId != null">
                debit_note_id,
            </if>
            <if test="sqdServiceTypeId != null">
                sqd_service_type_id,
            </if>
            <if test="sqdRctNo != null">
                sqd_rct_no,
            </if>
            <if test="relatedFreightId != null">
                related_freight_id,
            </if>
            <if test="isRecievingOrPaying != null">
                is_recieving_or_paying,
            </if>
            <if test="clearingCompanyId != null">
                clearing_company_id,
            </if>
            <if test="clearingCompanySummary != null">
                clearing_company_summary,
            </if>
            <if test="quotationStrategyId != null">
                quotation_strategy_id,
            </if>
            <if test="dnChargeNameId != null">
                dn_charge_name_id,
            </if>
            <if test="dnCurrencyCode != null">
                dn_currency_code,
            </if>
            <if test="dnUnitRate != null">
                dn_unit_rate,
            </if>
            <if test="dnUnitCode != null">
                dn_unit_code,
            </if>
            <if test="dnAmount != null">
                dn_amount,
            </if>
            <if test="basicCurrencyRate != null">
                basic_currency_rate,
            </if>
            <if test="dutyRate != null">
                duty_rate,
            </if>
            <if test="subtotal != null">
                subtotal,
            </if>
            <if test="chargeRemark != null">
                charge_remark,
            </if>
            <if test="clearingCurrencyCode != null">
                clearing_currency_code,
            </if>
            <if test="dnCurrencyReceived != null">
                dn_currency_received,
            </if>
            <if test="dnCurrencyPaid != null">
                dn_currency_paid,
            </if>
            <if test="dnCurrencyBalance != null">
                dn_currency_balance,
            </if>
            <if test="accountReceivedIdList != null">
                account_received_id_list,
            </if>
            <if test="accountPaidIdList != null">
                account_paid_id_list,
            </if>
            <if test="logisticsInvoiceIdList != null">
                logistics_invoice_id_list,
            </if>
            <if test="sqdServiceDetailsCode != null">
                sqd_service_details_code,
            </if>
            <if test="paymentTitleCode != null">
                payment_title_code,
            </if>
            <if test="logisticsPaymentTermsCode != null">
                logistics_payment_terms_code,
            </if>
            <if test="isAccountConfirmed != null">
                is_account_confirmed,
            </if>
            <if test="sqdDnCurrencyPaid != null">
                sqd_dn_currency_paid,
            </if>
            <if test="sqdDnCurrencyBalance != null">
                sqd_dn_currency_balance,
            </if>
            <if test="sqdWriteoffNoList != null">
                sqd_writeoff_no_list,
            </if>
            <if test="sqdInvoiceIssued != null">
                sqd_invoice_issued,
            </if>
            <if test="sqdInvoiceBalance != null">
                sqd_invoice_balance,
            </if>
            <if test="currencyRateCalculateDate != null">
                currency_rate_calculate_date,
            </if>
            <if test="writeoffStatus != null">
                writeoff_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chargeId != null">
                #{chargeId},
            </if>
            <if test="sqdRctId != null">
                #{sqdRctId},
            </if>
            <if test="serviceId != null">
                #{serviceId},
            </if>
            <if test="debitNoteId != null">
                #{debitNoteId},
            </if>
            <if test="sqdServiceTypeId != null">
                #{sqdServiceTypeId},
            </if>
            <if test="sqdRctNo != null">
                #{sqdRctNo},
            </if>
            <if test="relatedFreightId != null">
                #{relatedFreightId},
            </if>
            <if test="isRecievingOrPaying != null">
                #{isRecievingOrPaying},
            </if>
            <if test="clearingCompanyId != null">
                #{clearingCompanyId},
            </if>
            <if test="clearingCompanySummary != null">
                #{clearingCompanySummary},
            </if>
            <if test="quotationStrategyId != null">
                #{quotationStrategyId},
            </if>
            <if test="dnChargeNameId != null">
                #{dnChargeNameId},
            </if>
            <if test="dnCurrencyCode != null">
                #{dnCurrencyCode},
            </if>
            <if test="dnUnitRate != null">
                #{dnUnitRate},
            </if>
            <if test="dnUnitCode != null">
                #{dnUnitCode},
            </if>
            <if test="dnAmount != null">
                #{dnAmount},
            </if>
            <if test="basicCurrencyRate != null">
                #{basicCurrencyRate},
            </if>
            <if test="dutyRate != null">
                #{dutyRate},
            </if>
            <if test="subtotal != null">
                #{subtotal},
            </if>
            <if test="chargeRemark != null">
                #{chargeRemark},
            </if>
            <if test="clearingCurrencyCode != null">
                #{clearingCurrencyCode},
            </if>
            <if test="dnCurrencyReceived != null">
                #{dnCurrencyReceived},
            </if>
            <if test="dnCurrencyPaid != null">
                #{dnCurrencyPaid},
            </if>
            <if test="dnCurrencyBalance != null">
                #{dnCurrencyBalance},
            </if>
            <if test="accountReceivedIdList != null">
                #{accountReceivedIdList},
            </if>
            <if test="accountPaidIdList != null">
                #{accountPaidIdList},
            </if>
            <if test="logisticsInvoiceIdList != null">
                #{logisticsInvoiceIdList},
            </if>
            <if test="sqdServiceDetailsCode != null">
                #{sqdServiceDetailsCode},
            </if>
            <if test="paymentTitleCode != null">
                #{paymentTitleCode},
            </if>
            <if test="logisticsPaymentTermsCode != null">
                #{logisticsPaymentTermsCode},
            </if>
            <if test="isAccountConfirmed != null">
                #{isAccountConfirmed},
            </if>
            <if test="sqdDnCurrencyPaid != null">
                #{sqdDnCurrencyPaid},
            </if>
            <if test="sqdDnCurrencyBalance != null">
                #{sqdDnCurrencyBalance},
            </if>
            <if test="sqdWriteoffNoList != null">
                #{sqdWriteoffNoList},
            </if>
            <if test="sqdInvoiceIssued != null">
                #{sqdInvoiceIssued},
            </if>
            <if test="sqdInvoiceBalance != null">
                #{sqdInvoiceBalance},
            </if>
            <if test="currencyRateCalculateDate != null">
                #{currencyRateCalculateDate},
            </if>
            <if test="writeoffStatus != null">
                #{writeoffStatus},
            </if>
        </trim>
        <trim prefix="ON DUPLICATE KEY UPDATE" suffixOverrides=",">
            <if test="sqdRctId != null">
                sqd_rct_id = VALUES(sqd_rct_id),
            </if>
            <if test="serviceId != null">
                service_id = VALUES(service_id),
            </if>
            <if test="debitNoteId != null">
                debit_note_id = VALUES(debit_note_id),
            </if>
            <if test="sqdServiceTypeId != null">
                sqd_service_type_id = VALUES(sqd_service_type_id),
            </if>
            <if test="sqdRctNo != null">
                sqd_rct_no = VALUES(sqd_rct_no),
            </if>
            <if test="relatedFreightId != null">
                related_freight_id = VALUES(related_freight_id),
            </if>
            <if test="isRecievingOrPaying != null">
                is_recieving_or_paying = VALUES(is_recieving_or_paying),
            </if>
            <if test="clearingCompanyId != null">
                clearing_company_id = VALUES(clearing_company_id),
            </if>
            <if test="clearingCompanySummary != null">
                clearing_company_summary = VALUES(clearing_company_summary),
            </if>
            <if test="quotationStrategyId != null">
                quotation_strategy_id = VALUES(quotation_strategy_id),
            </if>
            <if test="dnChargeNameId != null">
                dn_charge_name_id = VALUES(dn_charge_name_id),
            </if>
            <if test="dnCurrencyCode != null">
                dn_currency_code = VALUES(dn_currency_code),
            </if>
            <if test="dnUnitRate != null">
                dn_unit_rate = VALUES(dn_unit_rate),
            </if>
            <if test="dnUnitCode != null">
                dn_unit_code = VALUES(dn_unit_code),
            </if>
            <if test="dnAmount != null">
                dn_amount = VALUES(dn_amount),
            </if>
            <if test="basicCurrencyRate != null">
                basic_currency_rate = VALUES(basic_currency_rate),
            </if>
            <if test="dutyRate != null">
                duty_rate = VALUES(duty_rate),
            </if>
            <if test="subtotal != null">
                subtotal = VALUES(subtotal),
            </if>
            <if test="chargeRemark != null">
                charge_remark = VALUES(charge_remark),
            </if>
            <if test="clearingCurrencyCode != null">
                clearing_currency_code = VALUES(clearing_currency_code),
            </if>
            <if test="dnCurrencyReceived != null">
                dn_currency_received = VALUES(dn_currency_received),
            </if>
            <if test="dnCurrencyPaid != null">
                dn_currency_paid = VALUES(dn_currency_paid),
            </if>
            <if test="dnCurrencyBalance != null">
                dn_currency_balance = VALUES(dn_currency_balance),
            </if>
            <if test="accountReceivedIdList != null">
                account_received_id_list = VALUES(account_received_id_list),
            </if>
            <if test="accountPaidIdList != null">
                account_paid_id_list = VALUES(account_paid_id_list),
            </if>
            <if test="logisticsInvoiceIdList != null">
                logistics_invoice_id_list = VALUES(logistics_invoice_id_list),
            </if>
            <if test="sqdServiceDetailsCode != null">
                sqd_service_details_code = VALUES(sqd_service_details_code),
            </if>
            <if test="paymentTitleCode != null">
                payment_title_code = VALUES(payment_title_code),
            </if>
            <if test="logisticsPaymentTermsCode != null">
                logistics_payment_terms_code = VALUES(logistics_payment_terms_code),
            </if>
            <if test="isAccountConfirmed != null">
                is_account_confirmed = VALUES(is_account_confirmed),
            </if>
            <if test="sqdDnCurrencyPaid != null">
                sqd_dn_currency_paid = VALUES(sqd_dn_currency_paid),
            </if>
            <if test="sqdDnCurrencyBalance != null">
                sqd_dn_currency_balance = VALUES(sqd_dn_currency_balance),
            </if>
            <if test="sqdWriteoffNoList != null">
                sqd_writeoff_no_list = VALUES(sqd_writeoff_no_list),
            </if>
            <if test="sqdInvoiceIssued != null">
                sqd_invoice_issued = VALUES(sqd_invoice_issued),
            </if>
            <if test="sqdInvoiceBalance != null">
                sqd_invoice_balance = VALUES(sqd_invoice_balance),
            </if>
            <if test="currencyRateCalculateDate != null">
                currency_rate_calculate_date = VALUES(currency_rate_calculate_date),
            </if>
            <if test="writeoffStatus != null">
                writeoff_status = VALUES(writeoff_status),
            </if>
        </trim>
    </insert>


    <insert id="insertRsCharge" parameterType="RsCharge" useGeneratedKeys="true" keyProperty="chargeId">
        insert into rs_charge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">
                service_id,
            </if>
            <if test="sqdServiceTypeId != null">
                sqd_service_type_id,
            </if>
            <if test="sqdRctNo != null">
                sqd_rct_no,
            </if>
            <if test="relatedFreightId != null">
                related_freight_id,
            </if>
            <if test="isRecievingOrPaying != null">
                is_recieving_or_paying,
            </if>
            <if test="clearingCompanyId != null">
                clearing_company_id,
            </if>
            <if test="clearingCompanySummary != null">
                clearing_company_summary,
            </if>
            <if test="quotationStrategyId != null">
                quotation_strategy_id,
            </if>
            <if test="dnChargeNameId != null">
                dn_charge_name_id,
            </if>
            <if test="dnCurrencyCode != null">
                dn_currency_code,
            </if>
            <if test="dnUnitRate != null">
                dn_unit_rate,
            </if>
            <if test="dnUnitCode != null">
                dn_unit_code,
            </if>
            <if test="dnAmount != null">
                dn_amount,
            </if>
            <if test="basicCurrencyRate != null">
                basic_currency_rate,
            </if>
            <if test="dutyRate != null">
                duty_rate,
            </if>
            <if test="subtotal != null">
                subtotal,
            </if>
            <if test="chargeRemark != null">
                charge_remark,
            </if>
            <if test="isDnSalesConfirmed != null">
                is_dn_sales_confirmed,
            </if>
            <if test="isDnClientConfirmed != null">
                is_dn_client_confirmed,
            </if>
            <if test="isDnOpConfirmed != null">
                is_dn_op_confirmed,
            </if>
            <if test="isDnPsaConfirmed != null">
                is_dn_psa_confirmed,
            </if>
            <if test="isDnSupplierConfirmed != null">
                is_dn_supplier_confirmed,
            </if>
            <if test="isAccountConfirmed != null">
                is_account_confirmed,
            </if>
            <if test="confirmAccountId != null">
                confirm_account_id,
            </if>
            <if test="accountConfirmTime != null">
                account_confirm_time,
            </if>
            <if test="clearingCurrencyCode != null">
                clearing_currency_code,
            </if>
            <if test="dnCurrencyReceived != null">
                dn_currency_received,
            </if>
            <if test="dnCurrencyPaid != null">
                dn_currency_paid,
            </if>
            <if test="dnCurrencyBalance != null">
                dn_currency_balance,
            </if>
            <if test="accountReceivedIdList != null">
                account_received_id_list,
            </if>
            <if test="accountPaidIdList != null">
                account_paid_id_list,
            </if>
            <if test="logisticsInvoiceIdList != null">
                logistics_invoice_id_list,
            </if>
            <if test="sqdRctId != null">
                sqd_rct_id,
            </if>
            <if test="sqdInvoiceIssued != null">
                sqd_invoice_issued,
            </if>
            <if test="sqdInvoiceBalance != null">
                sqd_invoice_balance,
            </if>
            <if test="sqdIsAccountConfirmed != null">
                sqd_is_account_confirmed,
            </if>
            <if test="sqdDnCurrencyPaid != null">
                sqd_dn_currency_paid,
            </if>
            <if test="sqdDnCurrencyBalance != null">
                sqd_dn_currency_balance,
            </if>
            <if test="sqdWriteoffNoList != null">
                sqd_writeoff_no_list,
            </if>
            <if test="currencyRateCalculateDate != null">
                currency_rate_calculate_date,
            </if>
            <if test="writeoffStatus != null">
                writeoff_status,
            </if>
            <if test="paymentTitleCode != null">
                payment_title_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">
                #{serviceId},
            </if>
            <if test="sqdServiceTypeId != null">
                #{sqdServiceTypeId},
            </if>
            <if test="sqdRctNo != null">
                #{sqdRctNo},
            </if>
            <if test="relatedFreightId != null">
                #{relatedFreightId},
            </if>
            <if test="isRecievingOrPaying != null">
                #{isRecievingOrPaying},
            </if>
            <if test="clearingCompanyId != null">
                #{clearingCompanyId},
            </if>
            <if test="clearingCompanySummary != null">
                #{clearingCompanySummary},
            </if>
            <if test="quotationStrategyId != null">
                #{quotationStrategyId},
            </if>
            <if test="dnChargeNameId != null">
                #{dnChargeNameId},
            </if>
            <if test="dnCurrencyCode != null">
                #{dnCurrencyCode},
            </if>
            <if test="dnUnitRate != null">
                #{dnUnitRate},
            </if>
            <if test="dnUnitCode != null">
                #{dnUnitCode},
            </if>
            <if test="dnAmount != null">
                #{dnAmount},
            </if>
            <if test="basicCurrencyRate != null">
                #{basicCurrencyRate},
            </if>
            <if test="dutyRate != null">
                #{dutyRate},
            </if>
            <if test="subtotal != null">
                #{subtotal},
            </if>
            <if test="chargeRemark != null">
                #{chargeRemark},
            </if>
            <if test="isDnSalesConfirmed != null">
                #{isDnSalesConfirmed},
            </if>
            <if test="isDnClientConfirmed != null">
                #{isDnClientConfirmed},
            </if>
            <if test="isDnOpConfirmed != null">
                #{isDnOpConfirmed},
            </if>
            <if test="isDnPsaConfirmed != null">
                #{isDnPsaConfirmed},
            </if>
            <if test="isDnSupplierConfirmed != null">
                #{isDnSupplierConfirmed},
            </if>
            <if test="isAccountConfirmed != null">
                #{isAccountConfirmed},
            </if>
            <if test="confirmAccountId != null">
                #{confirmAccountId},
            </if>
            <if test="accountConfirmTime != null">
                #{accountConfirmTime},
            </if>
            <if test="clearingCurrencyCode != null">
                #{clearingCurrencyCode},
            </if>
            <if test="dnCurrencyReceived != null">
                #{dnCurrencyReceived},
            </if>
            <if test="dnCurrencyPaid != null">
                #{dnCurrencyPaid},
            </if>
            <if test="dnCurrencyBalance != null">
                #{dnCurrencyBalance},
            </if>
            <if test="accountReceivedIdList != null">
                #{accountReceivedIdList},
            </if>
            <if test="accountPaidIdList != null">
                #{accountPaidIdList},
            </if>
            <if test="logisticsInvoiceIdList != null">
                #{logisticsInvoiceIdList},
            </if>
            <if test="sqdRctId != null">
                #{sqdRctId},
            </if>
            <if test="sqdInvoiceIssued != null">
                #{sqdInvoiceIssued},
            </if>
            <if test="sqdInvoiceBalance != null">
                #{sqdInvoiceBalance},
            </if>
            <if test="sqdIsAccountConfirmed != null">
                #{sqdIsAccountConfirmed},
            </if>
            <if test="sqdDnCurrencyPaid != null">
                #{sqdDnCurrencyPaid},
            </if>
            <if test="sqdDnCurrencyBalance != null">
                #{sqdDnCurrencyBalance},
            </if>
            <if test="sqdWriteoffNoList != null">
                #{sqdWriteoffNoList},
            </if>
            <if test="currencyRateCalculateDate != null">
                #{currencyRateCalculateDate},
            </if>
            <if test="writeoffStatus != null">
                #{writeoffStatus},
            </if>
            <if test="paymentTitleCode != null">
                #{paymentTitleCode},
            </if>
        </trim>
    </insert>

    <update id="updateRsCharge" parameterType="RsCharge">
        update rs_charge
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">
                service_id = #{serviceId},
            </if>
            <if test="sqdServiceTypeId != null">
                sqd_service_type_id = #{sqdServiceTypeId},
            </if>
            <if test="sqdRctNo != null">
                sqd_rct_no = #{sqdRctNo},
            </if>
            <if test="relatedFreightId != null">
                related_freight_id = #{relatedFreightId},
            </if>
            <if test="isRecievingOrPaying != null">
                is_recieving_or_paying = #{isRecievingOrPaying},
            </if>
            <if test="clearingCompanyId != null">
                clearing_company_id = #{clearingCompanyId},
            </if>
            <if test="clearingCompanySummary != null">
                clearing_company_summary = #{clearingCompanySummary},
            </if>
            <if test="quotationStrategyId != null">
                qoutation_strategy_id = #{quotationStrategyId},
            </if>
            <if test="dnChargeNameId != null">
                dn_charge_name_id = #{dnChargeNameId},
            </if>
            <if test="dnCurrencyCode != null">
                dn_currency_code = #{dnCurrencyCode},
            </if>
            <if test="dnUnitRate != null">
                dn_unit_rate = #{dnUnitRate},
            </if>
            <if test="dnUnitCode != null">
                dn_unit_code = #{dnUnitCode},
            </if>
            <if test="dnAmount != null">
                dn_amount = #{dnAmount},
            </if>
            <if test="basicCurrencyRate != null">
                basic_currency_rate = #{basicCurrencyRate},
            </if>
            <if test="dutyRate != null">
                duty_rate = #{dutyRate},
            </if>
            <if test="subtotal != null">
                subtotal = #{subtotal},
            </if>
            <if test="chargeRemark != null">
                charge_remark = #{chargeRemark},
            </if>
            <if test="isDnSalesConfirmed != null">
                is_dn_sales_confirmed = #{isDnSalesConfirmed},
            </if>
            <if test="isDnClientConfirmed != null">
                is_dn_client_confirmed = #{isDnClientConfirmed},
            </if>
            <if test="isDnOpConfirmed != null">
                is_dn_op_confirmed = #{isDnOpConfirmed},
            </if>
            <if test="isDnPsaConfirmed != null">
                is_dn_psa_confirmed = #{isDnPsaConfirmed},
            </if>
            <if test="isDnSupplierConfirmed != null">
                is_dn_supplier_confirmed = #{isDnSupplierConfirmed},
            </if>
            <if test="isAccountConfirmed != null">
                is_account_confirmed = #{isAccountConfirmed},
            </if>
            <if test="confirmAccountId != null">
                confirm_account_id = #{confirmAccountId},
            </if>
            <if test="accountConfirmTime != null">
                account_confirm_time = #{accountConfirmTime},
            </if>
            <if test="clearingCurrencyCode != null">
                clearing_currency_code = #{clearingCurrencyCode},
            </if>
            <if test="dnCurrencyReceived != null">
                dn_currency_received = #{dnCurrencyReceived},
            </if>
            <if test="dnCurrencyPaid != null">
                dn_currency_paid = #{dnCurrencyPaid},
            </if>
            <if test="dnCurrencyBalance != null">
                dn_currency_balance = #{dnCurrencyBalance},
            </if>
            <if test="accountReceivedIdList != null">
                account_received_id_list = #{accountReceivedIdList},
            </if>
            <if test="accountPaidIdList != null">
                account_paid_id_list = #{accountPaidIdList},
            </if>
            <if test="logisticsInvoiceIdList != null">
                logistics_invoice_id_list = #{logisticsInvoiceIdList},
            </if>
            <if test="sqdRctId != null">
                sqd_rct_id = #{sqdRctId},
            </if>
            <if test="sqdInvoiceIssued != null">
                sqd_invoice_issued = #{sqdInvoiceIssued},
            </if>
            <if test="sqdInvoiceBalance != null">
                sqd_invoice_balance = #{sqdInvoiceBalance},
            </if>
            <if test="sqdIsAccountConfirmed != null">
                sqd_is_account_confirmed = #{sqdIsAccountConfirmed},
            </if>
            <if test="sqdDnCurrencyPaid != null">
                sqd_dn_currency_paid = #{sqdDnCurrencyPaid},
            </if>
            <if test="sqdDnCurrencyBalance != null">
                sqd_dn_currency_balance = #{sqdDnCurrencyBalance},
            </if>
            <if test="sqdWriteoffNoList != null">
                sqd_writeoff_no_list = #{sqdWriteoffNoList},
            </if>
            <if test="currencyRateCalculateDate != null">
                currency_rate_calculate_date = #{currencyRateCalculateDate},
            </if>
            <if test="writeoffStatus != null">
                writeoff_status = #{writeoffStatus},
            </if>
            <if test="paymentTitleCode != null">
                payment_title_code = #{paymentTitleCode},
            </if>
        </trim>
        where charge_id = #{chargeId}
    </update>

    <delete id="deleteRsChargeByChargeId" parameterType="Long">
        delete
        from rs_charge
        where charge_id = #{chargeId}
    </delete>

    <delete id="deleteRsChargeByChargeIds" parameterType="String">
        delete from rs_charge where charge_id in
        <foreach item="chargeId" collection="array" open="(" separator="," close=")">
            #{chargeId}
        </foreach>
    </delete>

    <delete id="deleteRsCharge">
        delete
        from rs_charge
        where service_id = #{serviceId}
    </delete>

    <select id="selectRsChargeListByServiceId" resultMap="RsChargeResult">
        select rc.charge_id,
               service_id,
               sqd_service_type_id,
               sqd_rct_no,
               related_freight_id,
               is_recieving_or_paying,
               clearing_company_id,
               clearing_company_summary,
               quotation_strategy_id,
               dn_charge_name_id,
               dn_currency_code,
               dn_unit_rate,
               dn_unit_code,
               dn_amount,
               basic_currency_rate,
               duty_rate,
               subtotal,
               charge_remark,
               clearing_currency_code,
               dn_currency_received,
               dn_currency_paid,
               dn_currency_balance,
               account_received_id_list,
               account_paid_id_list,
               logistics_invoice_id_list,
               sqd_invoice_issued,
               sqd_invoice_balance,
               rc.is_account_confirmed,
               sqd_dn_currency_paid,
               sqd_dn_currency_balance,
               sqd_writeoff_no_list,
               IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
               bc.charge_local_name                                                         charge_name,
               isnull(clearing_company_id)                                               as show_client,
               isnull(dn_charge_name_id)                                                 as show_quotation_charge,
               isnull(dn_currency_code)                                                  as show_quotation_currency,
               isnull(dn_unit_code)                                                      as show_quotation_unit,
               isnull(clearing_company_id)                                               as show_supplier,
               isnull(dn_charge_name_id)                                                 as show_cost_charge,
               isnull(dn_currency_code)                                                  as show_cost_currency,
               isnull(dn_unit_code)                                                      as show_cost_unit,
               isnull(quotation_strategy_id)                                             as show_strategy,
               isnull(dn_unit_rate)                                                      as show_unit_rate,
               isnull(dn_amount)                                                         as show_amount,
               isnull(basic_currency_rate)                                               as show_currency_rate,
               isnull(duty_rate)                                                         as show_duty_rate,
               bdst.service_local_name                                                   as service_local_name
        from rs_charge rc
                 left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
                 left join ext_company ec on rc.clearing_company_id = ec.company_id
                 left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        where service_id = #{serviceId}
    </select>

    <select id="selectRsChargeListByRctId" resultMap="RsChargeResult">
        select rc.charge_id,
               service_id,
                rc.debit_note_id as debit_note_id,
               sqd_service_type_id,
               sqd_rct_no,
               related_freight_id,
               is_recieving_or_paying,
               clearing_company_id,
               clearing_company_summary,
               quotation_strategy_id,
               dn_charge_name_id,
               dn_currency_code,
               dn_unit_rate,
               dn_unit_code,
               dn_amount,
               basic_currency_rate,
               duty_rate,
               subtotal,
               charge_remark,
               clearing_currency_code,
               dn_currency_received,
               dn_currency_paid,
               dn_currency_balance,
               account_received_id_list,
               account_paid_id_list,
               logistics_invoice_id_list,
               sqd_invoice_issued,
               sqd_invoice_balance,
               sqd_rct_id,
               rc.is_account_confirmed,
               sqd_dn_currency_paid,
               sqd_dn_currency_balance,
               sqd_writeoff_no_list,
               IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
               bc.charge_local_name                                                         charge_name,
               isnull(clearing_company_id)                                               as show_client,
               isnull(dn_charge_name_id)                                                 as show_quotation_charge,
               isnull(dn_currency_code)                                                  as show_quotation_currency,
               isnull(dn_unit_code)                                                      as show_quotation_unit,
               isnull(clearing_company_id)                                               as show_supplier,
               isnull(dn_charge_name_id)                                                 as show_cost_charge,
               isnull(dn_currency_code)                                                  as show_cost_currency,
               isnull(dn_unit_code)                                                      as show_cost_unit,
               isnull(quotation_strategy_id)                                             as show_strategy,
               isnull(dn_unit_rate)                                                      as show_unit_rate,
               isnull(dn_amount)                                                         as show_amount,
               isnull(basic_currency_rate)                                               as show_currency_rate,
               isnull(duty_rate)                                                         as show_duty_rate,
               bdst.service_local_name                                                   as service_local_name
        from rs_charge rc
                 left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
                 left join ext_company ec on rc.clearing_company_id = ec.company_id
                 left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        where rc.sqd_rct_id = #{rctId}
    </select>

    <update id="batchRsCharge" parameterType="RsCharge">
        update rs_charge
        <trim prefix="SET" suffixOverrides=",">
            <if test="isAccountConfirmed != null">
                is_account_confirmed = #{isAccountConfirmed},
            </if>
        </trim>
        where charge_id = #{chargeId}
    </update>

    <delete id="deleteRsChargeByServiceId">
        delete
        from rs_charge
        where service_id = #{serviceId}
    </delete>

    <delete id="deleteRsChargeByRctId">
        delete
        from rs_charge
        where sqd_rct_id = #{rctId}
    </delete>

    <update id="flushExchangeRate" parameterType="BasExchangeRate">
        update rs_charge
        set basic_currency_rate = #{exchangeRate}
        where currency_rate_calculate_date <![CDATA[>=]]> #{validFrom}
          and currency_rate_calculate_date <![CDATA[<=]]> #{validTo}
    </update>

    <select id="findHedging" parameterType="RsCharge" resultMap="RsChargeResult">
        select rc.charge_id,
        service_id,
        sqd_service_type_id,
        rc.sqd_rct_no,
        related_freight_id,
        rc.is_recieving_or_paying,
        rc.clearing_company_id,
        clearing_company_summary,
        quotation_strategy_id,
        dn_charge_name_id,
        dn_currency_code,
        dn_unit_rate,
        dn_unit_code,
        dn_amount,
        basic_currency_rate,
        duty_rate,
        currency_rate_calculate_date,
        subtotal,
        charge_remark,
        clearing_currency_code,
        dn_currency_received,
        dn_currency_paid,
        dn_currency_balance,
        account_received_id_list,
        account_paid_id_list,
        logistics_invoice_id_list,
        sqd_invoice_issued,
        sqd_invoice_balance,
        rc.is_account_confirmed,
        rc.sqd_dn_currency_paid,
        rc.sqd_dn_currency_balance,
        sqd_writeoff_no_list,
        IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
        bc.charge_local_name charge_name,
        bdst.service_local_name as service_local_name,
        rr.sales_id as staff_id,
        rc.payment_title_code,
        mcbw.mid_charge_bank_id, mcbw.charge_id, mcbw.bank_record_id, mcbw.sqd_rct_no, mcbw.sqd_is_recieving_or_paying,
        mcbw.sqd_clearing_company_id, mcbw.sqd_dn_charge_name_id, mcbw.sqd_charge_shortname, mcbw.sqd_dn_currency_code,
        mcbw.temp_dn_balance_remain, mcbw.sqd_is_account_confirmed, mcbw.sqd_dn_currency_paid,
        mcbw.sqd_dn_currency_balance, mcbw.writeoff_from_dn_balance, mcbw.sqd_bank_currency_code,
        mcbw.temp_bank_balance_remain, mcbw.dn_basic_rate, mcbw.bank_basic_rate, mcbw.exchange_rate_showing,
        mcbw.writeoff_from_bank_balance, mcbw.writeoff_status, mcbw.writeoff_staff_id, mcbw.writeoff_time
        from rs_charge rc
        left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
        left join ext_company ec on rc.clearing_company_id = ec.company_id
        left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        left join rich.rs_rct rr on rr.rct_id = rc.sqd_rct_id
        left join (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY bank_record_id,charge_id ORDER BY
        writeoff_time DESC) AS
        row_num FROM mid_charge_bank_writeoff WHERE bank_record_id = #{bankRecordId}) AS subquery WHERE row_num = 1)
        mcbw on mcbw.charge_id=rc.charge_id
        <where>
            rr.op_accept=1 and rc.writeoff_status = '0' and rc.sqd_dn_currency_balance != 0
            <if test="serviceId != null ">
                and service_id = #{serviceId}
            </if>
            <if test="sqdServiceTypeId != null ">
                and sqd_service_type_id = #{sqdServiceTypeId}
            </if>
            <if test="sqdRctNo != null  and sqdRctNo != ''">
                and rc.sqd_rct_no = #{sqdRctNo}
            </if>
            <if test="relatedFreightId != null ">
                and related_freight_id = #{relatedFreightId}
            </if>
            <if test="isRecievingOrPaying != null ">
                and rc.is_recieving_or_paying = #{isRecievingOrPaying}
            </if>
            <!--没销过的流水-->
            <if test="isRecievingOrPaying != null and writeoffStatusString =='NULL'.toString()">
                and rc.sqd_dn_currency_balance <![CDATA[<>]]> 0
            </if>
            <if test="clearingCompanyId != null ">
                and rc.clearing_company_id = #{clearingCompanyId}
            </if>
            <if test="clearingCompanySummary != null  and clearingCompanySummary != ''">
                and clearing_company_summary = #{clearingCompanySummary}
            </if>
            <if test="quotationStrategyId != null ">
                and qoutation_strategy_id = #{quotationStrategyId}
            </if>
            <if test="dnChargeNameId != null ">
                and dn_charge_name_id = #{dnChargeNameId}
            </if>
            <if test="dnCurrencyCode != null  and dnCurrencyCode != ''">
                and dn_currency_code = #{dnCurrencyCode}
            </if>
            <if test="dnUnitRate != null ">
                and dn_unit_rate = #{dnUnitRate}
            </if>
            <if test="dnUnitCode != null  and dnUnitCode != ''">
                and dn_unit_code = #{dnUnitCode}
            </if>
            <if test="dnAmount != null ">
                and dn_amount = #{dnAmount}
            </if>
            <if test="basicCurrencyRate != null ">
                and basic_currency_rate = #{basicCurrencyRate}
            </if>
            <if test="dutyRate != null ">
                and duty_rate = #{dutyRate}
            </if>
            <if test="subtotal != null ">
                and subtotal = #{subtotal}
            </if>
            <if test="chargeRemark != null  and chargeRemark != ''">
                and charge_remark = #{chargeRemark}
            </if>
            <if test="isDnSalesConfirmed != null  and isDnSalesConfirmed != ''">
                and is_dn_sales_confirmed = #{isDnSalesConfirmed}
            </if>
            <if test="isDnClientConfirmed != null  and isDnClientConfirmed != ''">
                and is_dn_client_confirmed = #{isDnClientConfirmed}
            </if>
            <if test="isDnOpConfirmed != null  and isDnOpConfirmed != ''">
                and is_dn_op_confirmed = #{isDnOpConfirmed}
            </if>
            <if test="isDnPsaConfirmed != null  and isDnPsaConfirmed != ''">
                and is_dn_psa_confirmed = #{isDnPsaConfirmed}
            </if>
            <if test="isDnSupplierConfirmed != null  and isDnSupplierConfirmed != ''">
                and is_dn_supplier_confirmed = #{isDnSupplierConfirmed}
            </if>
            <if test="isAccountConfirmed != null  and isAccountConfirmed != ''">
                and rc.is_account_confirmed = #{isAccountConfirmed}
            </if>
            <if test="confirmAccountId != null ">
                and confirm_account_id = #{confirmAccountId}
            </if>
            <if test="accountConfirmTime != null ">
                and account_confirm_time = #{accountConfirmTime}
            </if>
            <if test="clearingCurrencyCode != null  and clearingCurrencyCode != ''">
                and clearing_currency_code = #{clearingCurrencyCode}
            </if>
            <if test="dnCurrencyReceived != null ">
                and dn_currency_received = #{dnCurrencyReceived}
            </if>
            <if test="dnCurrencyPaid != null ">
                and dn_currency_paid = #{dnCurrencyPaid}
            </if>
            <if test="dnCurrencyBalance != null ">
                and dn_currency_balance = #{dnCurrencyBalance}
            </if>
            <if test="accountReceivedIdList != null  and accountReceivedIdList != ''">
                and account_received_id_list = #{accountReceivedIdList}
            </if>
            <if test="accountPaidIdList != null  and accountPaidIdList != ''">
                and account_paid_id_list = #{accountPaidIdList}
            </if>
            <if test="logisticsInvoiceIdList != null  and logisticsInvoiceIdList != ''">
                and logistics_invoice_id_list = #{logisticsInvoiceIdList}
            </if>
            <if test="sqdInvoiceIssued != null ">
                and sqd_invoice_issued = #{sqdInvoiceIssued}
            </if>
            <if test="sqdInvoiceBalance != null ">
                and sqd_invoice_balance = #{sqdInvoiceBalance}
            </if>
            <if test="sqdIsAccountConfirmed != null  and sqdIsAccountConfirmed != ''">
                and sqd_is_account_confirmed = #{sqdIsAccountConfirmed}
            </if>
            <if test="sqdDnCurrencyPaid != null ">
                and sqd_dn_currency_paid = #{sqdDnCurrencyPaid}
            </if>
            <if test="sqdDnCurrencyBalance != null ">
                and sqd_dn_currency_balance = #{sqdDnCurrencyBalance}
            </if>
            <if test="sqdWriteoffNoList != null  and sqdWriteoffNoList != ''">
                and sqd_writeoff_no_list = #{sqdWriteoffNoList}
            </if>
            <if test="sqdRaletiveRctList != null  and sqdRaletiveRctList != ''">
                and find_in_set(rc.sqd_rct_no,#{sqdRaletiveRctList})
            </if>
        </where>
    </select>

    <select id="selectRsChargeListForWriteOff" resultMap="RsChargeResult">
        select rc.charge_id,
        service_id,
        sqd_service_type_id,
        rc.sqd_rct_no,
        related_freight_id,
        rc.is_recieving_or_paying,
        rc.clearing_company_id,
        clearing_company_summary,
        quotation_strategy_id,
        dn_charge_name_id,
        dn_currency_code,
        dn_unit_rate,
        dn_unit_code,
        dn_amount,
        basic_currency_rate,
        duty_rate,
        currency_rate_calculate_date,
        subtotal,
        charge_remark,
        clearing_currency_code,
        dn_currency_received,
        dn_currency_paid,
        dn_currency_balance,
        account_received_id_list,
        account_paid_id_list,
        logistics_invoice_id_list,
        sqd_invoice_issued,
        sqd_invoice_balance,
        rc.is_account_confirmed,
        rc.sqd_dn_currency_paid,
        rc.sqd_dn_currency_balance,
        sqd_writeoff_no_list,
        IFNULL(NULLIF(TRIM(ec.company_short_name), ''), ec.company_en_short_name) AS company_name,
        bc.charge_local_name charge_name,
        bdst.service_local_name as service_local_name,
        rr.sales_id as staff_id,
        rc.payment_title_code,
        mcbw.mid_charge_bank_id, mcbw.charge_id, mcbw.bank_record_id, mcbw.sqd_rct_no, mcbw.sqd_is_recieving_or_paying,
        mcbw.sqd_clearing_company_id, mcbw.sqd_dn_charge_name_id, mcbw.sqd_charge_shortname, mcbw.sqd_dn_currency_code,
        mcbw.temp_dn_balance_remain, mcbw.sqd_is_account_confirmed, mcbw.sqd_dn_currency_paid,
        mcbw.sqd_dn_currency_balance, mcbw.writeoff_from_dn_balance, mcbw.sqd_bank_currency_code,
        mcbw.temp_bank_balance_remain, mcbw.dn_basic_rate, mcbw.bank_basic_rate, mcbw.exchange_rate_showing,
        mcbw.writeoff_from_bank_balance, mcbw.writeoff_status, mcbw.writeoff_staff_id, mcbw.writeoff_time,
        <!--费用列表中关联的操作单信息-->
        dep.location_local_name as pol,
        dep2.location_en_name as destination_port,
        rr.bl_no as bl_no,
        rr.sqd_containers_seals_sum as sqd_containers_seals_sum,
        rr.revenue_ton as revenue_ton,
        ifnull(rr.eta,rr.destination_port_eta) as eta,
        ifnull(rr.etd,rr.pod_eta) as etd
        from rs_charge rc
        left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
        left join ext_company ec on rc.clearing_company_id = ec.company_id
        left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
        left join rich.rs_rct rr on rr.rct_id = rc.sqd_rct_id
        left join bas_dist_location dep on rr.pol_id = dep.location_id
        left join bas_dist_location dep2 on rr.destination_port_id = dep2.location_id
        left join (SELECT * FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY bank_record_id,charge_id ORDER BY
        writeoff_time DESC) AS
        row_num FROM mid_charge_bank_writeoff WHERE bank_record_id = #{bankRecordId}) AS subquery WHERE row_num = 1)
        mcbw on mcbw.charge_id=rc.charge_id
        <where>
            rr.op_accept=1 and rc.writeoff_status = '0' and rc.sqd_dn_currency_balance != 0
            <if test="writeoffStatus != null ">
                and rc.writeoff_status = #{writeoffStatus}
            </if>
            <if test="serviceId != null ">
                and service_id = #{serviceId}
            </if>
            <if test="sqdServiceTypeId != null ">
                and sqd_service_type_id = #{sqdServiceTypeId}
            </if>
            <if test="sqdRctNo != null  and sqdRctNo != ''">
                and rc.sqd_rct_no like concat(#{sqdRctNo}, '%')
            </if>
            <if test="relatedFreightId != null ">
                and related_freight_id = #{relatedFreightId}
            </if>
            <if test="isRecievingOrPaying != null ">
                and rc.is_recieving_or_paying = #{isRecievingOrPaying}
            </if>
            <!--没销过的流水-->
            <if test="isRecievingOrPaying != null and writeoffStatusString =='NULL'.toString()">
                and rc.sqd_dn_currency_balance <![CDATA[<>]]> 0
            </if>
            <if test="clearingCompanyId != null ">
                and rc.clearing_company_id = #{clearingCompanyId}
            </if>
            <if test="clearingCompanySummary != null  and clearingCompanySummary != ''">
                and clearing_company_summary = #{clearingCompanySummary}
            </if>
            <if test="quotationStrategyId != null ">
                and qoutation_strategy_id = #{quotationStrategyId}
            </if>
            <if test="dnChargeNameId != null ">
                and dn_charge_name_id = #{dnChargeNameId}
            </if>
            <if test="dnCurrencyCode != null  and dnCurrencyCode != ''">
                and dn_currency_code = #{dnCurrencyCode}
            </if>
            <if test="dnUnitRate != null ">
                and dn_unit_rate = #{dnUnitRate}
            </if>
            <if test="dnUnitCode != null  and dnUnitCode != ''">
                and dn_unit_code = #{dnUnitCode}
            </if>
            <if test="dnAmount != null ">
                and dn_amount = #{dnAmount}
            </if>
            <if test="basicCurrencyRate != null ">
                and basic_currency_rate = #{basicCurrencyRate}
            </if>
            <if test="dutyRate != null ">
                and duty_rate = #{dutyRate}
            </if>
            <if test="subtotal != null ">
                and subtotal = #{subtotal}
            </if>
            <if test="chargeRemark != null  and chargeRemark != ''">
                and charge_remark = #{chargeRemark}
            </if>
            <if test="isDnSalesConfirmed != null  and isDnSalesConfirmed != ''">
                and is_dn_sales_confirmed = #{isDnSalesConfirmed}
            </if>
            <if test="isDnClientConfirmed != null  and isDnClientConfirmed != ''">
                and is_dn_client_confirmed = #{isDnClientConfirmed}
            </if>
            <if test="isDnOpConfirmed != null  and isDnOpConfirmed != ''">
                and is_dn_op_confirmed = #{isDnOpConfirmed}
            </if>
            <if test="isDnPsaConfirmed != null  and isDnPsaConfirmed != ''">
                and is_dn_psa_confirmed = #{isDnPsaConfirmed}
            </if>
            <if test="isDnSupplierConfirmed != null  and isDnSupplierConfirmed != ''">
                and is_dn_supplier_confirmed = #{isDnSupplierConfirmed}
            </if>
            <if test="isAccountConfirmed != null  and isAccountConfirmed != ''">
                and rc.is_account_confirmed = #{isAccountConfirmed}
            </if>
            <if test="confirmAccountId != null ">
                and confirm_account_id = #{confirmAccountId}
            </if>
            <if test="accountConfirmTime != null ">
                and account_confirm_time = #{accountConfirmTime}
            </if>
            <if test="clearingCurrencyCode != null  and clearingCurrencyCode != ''">
                and clearing_currency_code = #{clearingCurrencyCode}
            </if>
            <if test="dnCurrencyReceived != null ">
                and dn_currency_received = #{dnCurrencyReceived}
            </if>
            <if test="dnCurrencyPaid != null ">
                and dn_currency_paid = #{dnCurrencyPaid}
            </if>
            <if test="dnCurrencyBalance != null ">
                and dn_currency_balance = #{dnCurrencyBalance}
            </if>
            <if test="accountReceivedIdList != null  and accountReceivedIdList != ''">
                and account_received_id_list = #{accountReceivedIdList}
            </if>
            <if test="accountPaidIdList != null  and accountPaidIdList != ''">
                and account_paid_id_list = #{accountPaidIdList}
            </if>
            <if test="logisticsInvoiceIdList != null  and logisticsInvoiceIdList != ''">
                and logistics_invoice_id_list = #{logisticsInvoiceIdList}
            </if>
            <if test="sqdInvoiceIssued != null ">
                and sqd_invoice_issued = #{sqdInvoiceIssued}
            </if>
            <if test="sqdInvoiceBalance != null ">
                and sqd_invoice_balance = #{sqdInvoiceBalance}
            </if>
            <if test="sqdIsAccountConfirmed != null  and sqdIsAccountConfirmed != ''">
                and sqd_is_account_confirmed = #{sqdIsAccountConfirmed}
            </if>
            <if test="sqdDnCurrencyPaid != null ">
                and sqd_dn_currency_paid = #{sqdDnCurrencyPaid}
            </if>
            <if test="sqdDnCurrencyBalance != null ">
                and sqd_dn_currency_balance = #{sqdDnCurrencyBalance}
            </if>
            <if test="sqdWriteoffNoList != null  and sqdWriteoffNoList != ''">
                and sqd_writeoff_no_list = #{sqdWriteoffNoList}
            </if>
            <if test="sqdRaletiveRctList != null  and sqdRaletiveRctList != ''">
                and find_in_set(rc.sqd_rct_no,#{sqdRaletiveRctList})
            </if>
        </where>
    </select>
</mapper>