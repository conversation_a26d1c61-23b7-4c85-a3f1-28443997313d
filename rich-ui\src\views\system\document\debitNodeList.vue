<template>
  <div class="debit-note-list">
    <el-table
      ref="debitNoteTable"
      :data="debitNoteList"
      border
      style="width: 100%"
      @expand-change="handleExpandChange"
    >
      <!-- 可展开列 -->
      <el-table-column type="expand" width="50">
        <template slot-scope="scope">
          <!-- 嵌套 chargeList 组件 -->
          <charges
              :charge-data="scope.row.rsChargeList"
              :company-list="companyList"
              :disabled="disabled"
              :hidden-supplier="hiddenSupplier"
              :is-receivable="isReceivable"
              :open-charge-list="true"
              :debit-note="scope.row"
              @copyFreight="handleCopyFreight"
              @deleteAll="handleDeleteAll"
              @deleteItem="handleDeleteItem"
              @return="handleChargeDataChange(scope.row, $event)"
              @selectRow="handleChargeSelection(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <!-- 分账单基本信息列 -->
      <el-table-column label="所属公司" prop="sqdRctNo" width="60">
        <template slot-scope="scope">
          <tree-select :flat="false" :multiple="false" :pass="scope.row.companyBelongsTo" :placeholder="'收付路径'"
                       :type="'rsPaymentTitle'" @return="scope.row.companyBelongsTo=$event"
          />
        </template>
      </el-table-column>

      <el-table-column label="我司账户" prop="companyName" width="100">
        <template slot-scope="scope">
          <tree-select :flat="false" :multiple="false"
                       :pass="scope.row.clearingCompanyBankAccount" :placeholder="'我司账户'"
                       :type="'companyAccount'"
                       @return="scope.row.clearingCompanyBankAccount=$event" @returnData="selectBankAccount(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="收付标志" prop="isRecievingOrPaying" width="50">
        <template slot-scope="scope">
          <el-tag
              :type="getBillStatusType(scope.row.billStatus)"
              size="mini"
          >
            {{ scope.row.isRecievingOrPaying == 0 ? "收" : "付" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="结算单位" prop="dnCurrencyCode" width="100">
        <template slot-scope="scope">
          <tree-select :custom-options="companyList" :flat="false" :multiple="false"
                       :pass="scope.row.clearingCompanyId" :placeholder="'客户'"
                       :type="'clientCustom'" @return="scope.row.clearingCompanyId=$event"
                       @returnData="handleSelectCompany(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <el-table-column label="对方账户" prop="billReceivable" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.clearingCompanyBankAccount"/>
        </template>
      </el-table-column>

      <el-table-column label="结算币种" prop="billPayable" width="120">
        <template slot-scope="scope">
          <tree-select :pass="scope.row.dnCurrencyCode" :type="'currency'"
                       @return="changeCurrency(scope.row,$event)"
          />
        </template>
      </el-table-column>

      <el-table-column align="right" label="账单应收" v-if="isReceivable" prop="billReceivable" width="80"/>
      <el-table-column align="right" label="账单应付" v-if="!isReceivable" prop="billPayable" width="80"/>

      <el-table-column align="center" label="账单状态" prop="billStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getBillStatusType(scope.row.billStatus)"
            size="mini"
          >
            {{ getBillStatusText(scope.row.billStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="发票状态" prop="invoiceStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getInvoiceStatusType(scope.row.invoiceStatus)"
            size="mini"
          >
            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="申请支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
              v-model="scope.row.requestPaymentDate"
              placeholder="选择日期"
              type="date"
          />
        </template>
      </el-table-column>
      <el-table-column label="预计支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
              v-model="scope.row.expectedPaymentDate"
              placeholder="选择日期"
              type="date"
          />
        </template>
      </el-table-column>
      <el-table-column label="实际支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
              v-model="scope.row.actualPaymentDate"
              placeholder="选择日期"
              type="date"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="销账状态" prop="writeoffStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getWriteoffStatusType(scope.row.writeoffStatus)"
            size="mini"
          >
            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column class-name="small-padding fixed-width" fixed="right" label="操作" width="130">
        <template slot-scope="scope">
          <div style="display: flex; gap: 4px;">
            <el-button
                v-if="scope.row.billStatus==='confirmed'"
                icon="el-icon-unlock"
                size="mini"
                type="success"
                @click="applyUnlock(scope.row)"
            >
              申请解锁
            </el-button>
            <el-button
                v-if="scope.row.billStatus==='draft'"
                icon="el-icon-check"
                size="mini"
                type="success"
                @click="setComplete(scope.row)"
            >
              设置完成
            </el-button>
            <el-button
                icon="el-icon-delete"
                size="mini"
                type="danger"
                @click="deleteDebitNote(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-button :disabled="disabled" style="padding: 0"
               type="text"
               @click="addDebitNote"
    >[＋]
    </el-button>
  </div>
</template>

<script>
import currency from "currency.js"
import Charges from "@/views/system/document/chargeList.vue"

export default {
  name: "debitNoteList",
  components: {Charges},
  props: [
    "companyList",
    "disabled",
    "hiddenSupplier",
    "rctId",
    "debitNoteList",
    "isReceivable"
  ],
  data() {
    return {
      loading: false,
      expandedRows: [],
      localDebitNoteList: []
    }
  },
  watch: {
    debitNoteList: {
      immediate: true,
      handler(newVal) {
        this.$emit('update:debitNoteList', newVal)
      }
    }
  },
  methods: {
    applyUnlock(row) {
      this.$emit("applyUnlock", row)
    },
    setComplete(row) {
      this.$emit("setComplete", row)
    },
    changeCurrency(row, currency) {
      row.dnCurrencyCode = currency
    },
    selectBankAccount(row, bankAccount) {
      row.bankAccountCode = bankAccount.bankAccountCode
      row.bankAccountName = bankAccount.bankAccountName
    },
    handleSelectCompany(row, company) {
      row.clearingCompanyId = company.companyId
      row.clearingCompanyName = company.companyShortName
    },
    addDebitNote() {
      this.$emit("addDebitNote")
    },
    currency,

    // 展开/收起行
    handleExpandChange(row, expandedRows) {
      this.expandedRows = expandedRows
    },

    // 创建分账单
    async createDebitNote(row) {
      try {

      } catch (error) {
        console.error("创建分账单失败:", error)
        this.$message.error("创建分账单失败")
      }
    },

    // 编辑分账单
    editDebitNote(row) {
      this.$emit("editDebitNote", row)
    },

    // 删除分账单
    async deleteDebitNote(row) {
      try {
        await this.$confirm("确定要删除该分账单吗？", "提示", {
          type: "warning"
        })
        this.$emit("deleteDebitNote", row)
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除分账单失败:", error)
          this.$message.error("删除分账单失败")
        }
      }
    },

    // 处理费用数据变化
    handleChargeDataChange(row, chargeData) {
      let billReceivable = 0
      let billPayable = 0

      // 统计chargeData的费用
      if (this.isReceivable) {
        // 应收
        chargeData.forEach(item => {
          // 使用currency.js计算
          billReceivable = currency(billReceivable).add(item.subtotal).toString()
        })
      } else {
        // 应付
        chargeData.forEach(item => {
          billPayable = currency(billPayable).add(item.subtotal).toString()
        })
      }
      row.billReceivable = billReceivable
      row.billPayable = billPayable
    },

    // 处理费用选择
    handleChargeSelection(row, selectedCharges) {
      const index = this.localDebitNoteList.findIndex(item => item === row)
      if (index !== -1) {
        this.localDebitNoteList[index].selectedCharges = selectedCharges
        // 通知父组件数据变化
        this.$emit('update:debitNoteList', this.localDebitNoteList)
      }
    },

    // 复制费用
    handleCopyFreight(charge) {
      this.$emit("copyFreight", charge)
    },

    // 删除费用项
    handleDeleteItem(charge) {
      this.$emit("deleteItem", charge)
    },

    // 删除所有费用
    handleDeleteAll() {
      this.$emit("deleteAll")
    },

    // 获取账单状态类型
    getBillStatusType(status) {
      const statusMap = {
        "draft": "info",
        "confirmed": "success",
        "closed": "danger"
      }
      return statusMap[status] || "info"
    },

    // 获取账单状态文本
    getBillStatusText(status) {
      const statusMap = {
        "draft": "草稿",
        "confirmed": "已确认",
        "closed": "已关闭"
      }
      return statusMap[status] || "未知"
    },

    // 获取发票状态类型
    getInvoiceStatusType(status) {
      const statusMap = {
        "unissued": "info",
        "issued": "success",
        "canceled": "danger"
      }
      return statusMap[status] || "info"
    },

    // 获取发票状态文本
    getInvoiceStatusText(status) {
      const statusMap = {
        "unissued": "未开票",
        "issued": "已开票",
        "canceled": "已作废"
      }
      return statusMap[status] || "未知"
    },

    // 获取销账状态类型
    getWriteoffStatusType(status) {
      const statusMap = {
        "unwritten": "info",
        "partial": "warning",
        "written": "success"
      }
      return statusMap[status] || "info"
    },

    // 获取销账状态文本
    getWriteoffStatusText(status) {
      const statusMap = {
        "unwritten": "未销账",
        "partial": "部分销账",
        "written": "已销账"
      }
      return statusMap[status] || "未知"
    }
  }
}
</script>

<style lang="scss" scoped>
.expand-content {
  padding: 20px;
  background-color: #f9f9f9;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin: 10px 0;
}

.charge-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;

  span {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
  }
}

input:focus {
  outline: none;
}

.unHighlight-text {
  color: #b7bbc2;
  margin: 0;
}

// 覆盖 Element UI 表格样式
:deep(.el-table) {
  .el-table__expanded-cell {
    padding: 0;

    .expand-content {
      margin: 0;
      border: none;
      background-color: transparent;
    }
  }
}
</style>
